#!/usr/bin/env python3
"""
Delete test user script
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import TenantLLMService
from api.db.services.file_service import FileService


def delete_user(email):
    """Delete a user and all associated data."""
    
    print(f"Deleting user: {email}")
    
    # Find user
    users = UserService.query(email=email)
    if not users:
        print(f"User {email} not found")
        return False
    
    user = users[0]
    user_id = user.id
    print(f"Found user: {user.nickname} (ID: {user_id})")
    
    try:
        # Delete in reverse order of creation
        
        # Delete files
        files = FileService.query(tenant_id=user_id)
        for file in files:
            FileService.delete(file.id)
        print(f"Deleted {len(files)} files")
        
        # Delete tenant LLM configs
        llm_configs = TenantLLMService.query(tenant_id=user_id)
        for config in llm_configs:
            TenantLLMService.delete(config.id)
        print(f"Deleted {len(llm_configs)} LLM configs")
        
        # Delete user-tenant relationships
        user_tenants = UserTenantService.query(user_id=user_id)
        for ut in user_tenants:
            UserTenantService.delete(ut.id)
        print(f"Deleted {len(user_tenants)} user-tenant relationships")
        
        # Delete tenant
        tenants = TenantService.query(id=user_id)
        for tenant in tenants:
            TenantService.delete(tenant.id)
        print(f"Deleted {len(tenants)} tenants")
        
        # Delete user
        UserService.delete(user.id)
        print("Deleted user")
        
        print(f"Successfully deleted user {email}")
        return True
        
    except Exception as e:
        print(f"Error deleting user: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function."""
    print("RAGFlow User Deletion Tool")
    print("=" * 30)
    
    # Delete the test user
    email = "<EMAIL>"
    success = delete_user(email)
    
    if success:
        print("User deletion completed successfully")
    else:
        print("User deletion failed")
    
    print("=" * 30)
    print("Script completed")


if __name__ == "__main__":
    main()
