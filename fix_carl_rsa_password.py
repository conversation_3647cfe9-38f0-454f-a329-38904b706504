#!/usr/bin/env python3
"""
Fix <PERSON>'s password to work with RSA encryption/decryption flow
"""

import sys
import os
import base64

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService
from api.utils import decrypt
from api.utils.t_crypt import crypt
from werkzeug.security import generate_password_hash


def fix_decrypt_function():
    """The issue is that decrypt() doesn't decode base64, but it should."""
    
    password = "Carl20250812"
    
    print("🔧 TESTING DECRYPT FUNCTIONS")
    print("=" * 40)
    
    # Encrypt password
    encrypted = crypt(password)
    print(f"✅ Encrypted: {encrypted[:50]}...")
    
    # Test current decrypt function
    try:
        decrypted1 = decrypt(encrypted)
        print(f"✅ decrypt() result: {decrypted1}")
        
        # The result should be base64 decoded
        try:
            final_password = base64.b64decode(decrypted1).decode('utf-8')
            print(f"✅ After base64 decode: {final_password}")
            
            if final_password == password:
                print("✅ Full decryption works with base64 decode!")
                return final_password
            else:
                print(f"❌ Still wrong: expected '{password}', got '{final_password}'")
        except Exception as e:
            print(f"❌ Base64 decode failed: {e}")
            
    except Exception as e:
        print(f"❌ decrypt() failed: {e}")
    
    return None


def create_password_that_works_with_current_decrypt():
    """Create a password hash that works with the current broken decrypt function."""
    
    email = "<EMAIL>"
    password = "Carl20250812"
    
    print(f"\n🔧 FIXING PASSWORD FOR: {email}")
    print("=" * 50)
    
    # Find user
    users = UserService.query(email=email)
    if not users:
        print(f"❌ User not found!")
        return False
    
    user = users[0]
    print(f"✅ User found: {user.nickname}")
    
    # The trick: We need to create a password hash where:
    # decrypt(encrypted_password) = base64_encoded_password
    # and check_password_hash(stored_hash, base64_encoded_password) = True
    
    # So we need to hash the base64-encoded version of the password
    base64_password = base64.b64encode(password.encode('utf-8')).decode('utf-8')
    print(f"📝 Base64 password: {base64_password}")
    
    # Create hash of the base64-encoded password
    password_hash = generate_password_hash(base64_password)
    print(f"📝 Password hash: {password_hash[:50]}...")
    
    # Update user password
    try:
        update_dict = {"password": password_hash}
        UserService.update_user(user.id, update_dict)
        print("✅ Password updated in database")
        
        # Test the full flow
        print(f"\n🧪 TESTING FULL LOGIN FLOW:")
        
        # 1. Encrypt password (like frontend)
        encrypted = crypt(password)
        print(f"✅ Frontend encrypts password")
        
        # 2. Decrypt password (like backend) - this gives us base64
        decrypted = decrypt(encrypted)
        print(f"✅ Backend decrypts to: {decrypted}")
        
        # 3. Test login with the decrypted (base64) password
        test_user = UserService.query_user(email, decrypted)
        if test_user:
            print("✅ LOGIN TEST PASSED!")
            print("🎉 Carl can now login on the website!")
            return True
        else:
            print("❌ LOGIN TEST FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function."""
    print("RAGFlow RSA Password Fix")
    print("=" * 30)
    
    # First understand the decrypt issue
    fix_decrypt_function()
    
    # Then fix Carl's password to work with it
    success = create_password_that_works_with_current_decrypt()
    
    print(f"\n" + "=" * 50)
    print("SUMMARY:")
    
    if success:
        print("✅ Password fixed!")
        print("🎉 Carl can now login on the website!")
        print("\nLogin credentials:")
        print("Email: <EMAIL>")
        print("Password: Carl20250812")
    else:
        print("❌ Failed to fix password")
    
    print(f"\n" + "=" * 50)
    print("Fix completed.")


if __name__ == "__main__":
    main()
