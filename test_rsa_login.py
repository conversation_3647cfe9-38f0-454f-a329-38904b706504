#!/usr/bin/env python3
"""
Test RSA encryption/decryption flow for login
"""

import sys
import os
import base64

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService
from api.utils import decrypt
from api.utils.t_crypt import crypt


def test_rsa_flow():
    """Test the complete RSA encryption/decryption flow like the website does."""
    
    email = "Carl<PERSON><EMAIL>"
    password = "Carl20250812"
    
    print("🔐 TESTING RSA ENCRYPTION/DECRYPTION FLOW")
    print("=" * 50)
    
    # Step 1: Find user
    users = UserService.query(email=email)
    if not users:
        print(f"❌ User not found: {email}")
        return False
    
    user = users[0]
    print(f"✅ User found: {user.nickname}")
    
    # Step 2: Encrypt password (like frontend does)
    try:
        encrypted_password = crypt(password)
        print(f"✅ Password encrypted: {encrypted_password[:50]}...")
    except Exception as e:
        print(f"❌ Encryption failed: {e}")
        return False
    
    # Step 3: Decrypt password (like backend does)
    try:
        decrypted_password = decrypt(encrypted_password)
        print(f"✅ Password decrypted: {decrypted_password}")
        
        if decrypted_password == password:
            print("✅ Encryption/Decryption cycle works!")
        else:
            print(f"❌ Decryption mismatch: expected '{password}', got '{decrypted_password}'")
            return False
    except Exception as e:
        print(f"❌ Decryption failed: {e}")
        return False
    
    # Step 4: Test login with decrypted password (like backend does)
    try:
        login_user = UserService.query_user(email, decrypted_password)
        if login_user:
            print("✅ Login with decrypted password: SUCCESS")
            return True
        else:
            print("❌ Login with decrypted password: FAILED")
            return False
    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return False


def simulate_web_login():
    """Simulate the exact web login process."""
    
    email = "<EMAIL>"
    password = "Carl20250812"
    
    print(f"\n🌐 SIMULATING WEB LOGIN PROCESS")
    print("=" * 50)
    
    # Step 1: Check if user exists (like backend does)
    users = UserService.query(email=email)
    if not users:
        print(f"❌ Email: {email} is not registered!")
        return False
    
    print(f"✅ User exists: {users[0].nickname}")
    
    # Step 2: Encrypt password (like frontend does)
    try:
        encrypted_password = crypt(password)
        print(f"✅ Frontend encrypts password")
    except Exception as e:
        print(f"❌ Frontend encryption failed: {e}")
        return False
    
    # Step 3: Backend receives encrypted password and decrypts it
    try:
        decrypted_password = decrypt(encrypted_password)
        print(f"✅ Backend decrypts password")
    except Exception as e:
        print(f"❌ Backend decryption failed: {e}")
        return False
    
    # Step 4: Backend calls UserService.query_user with decrypted password
    try:
        user = UserService.query_user(email, decrypted_password)
        if user:
            print("✅ UserService.query_user: SUCCESS")
            print("🎉 WEB LOGIN SHOULD WORK!")
            return True
        else:
            print("❌ UserService.query_user: FAILED")
            print("💡 This is why web login fails!")
            return False
    except Exception as e:
        print(f"❌ UserService.query_user error: {e}")
        return False


def main():
    """Main test function."""
    print("RAGFlow RSA Login Test")
    print("=" * 30)
    
    # Test RSA encryption/decryption flow
    rsa_works = test_rsa_flow()
    
    # Simulate web login process
    web_login_works = simulate_web_login()
    
    print(f"\n" + "=" * 50)
    print("SUMMARY:")
    print(f"{'✅' if rsa_works else '❌'} RSA encryption/decryption: {'WORKS' if rsa_works else 'BROKEN'}")
    print(f"{'✅' if web_login_works else '❌'} Web login simulation: {'WORKS' if web_login_works else 'BROKEN'}")
    
    if rsa_works and web_login_works:
        print("\n🎉 Everything should work! Try logging in on the website.")
    else:
        print("\n❌ There's still an issue with the RSA flow.")
        print("💡 The password might need to be recreated with proper encryption support.")
    
    print(f"\n" + "=" * 50)
    print("Test completed.")


if __name__ == "__main__":
    main()
