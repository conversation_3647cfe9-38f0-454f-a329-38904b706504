#!/usr/bin/env python3
"""
Create RAGFlow tenant setup for existing user
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import LLMService, TenantLLMService
from api.db.services.file_service import FileService
from api.db import FileType, UserTenantRole
from api.utils import get_uuid
from api import settings


def create_tenant_setup(email):
    """
    Create complete tenant setup for an existing user.
    
    Args:
        email (str): User email address
    
    Returns:
        bool: True if successful, False otherwise
    """
    
    print("Creating tenant setup")
    print(f"Email: {email}")
    
    # Find user
    users = UserService.query(email=email)
    if not users:
        print(f"User with email {email} not found")
        return False
    
    user = users[0]
    user_id = user.id
    nickname = user.nickname
    print(f"Found user: {nickname} (ID: {user_id})")
    
    try:
        # Use default parser_ids if settings.PARSERS is None
        default_parsers = "naive:General,qa:Q&A,resume:Resume,manual:Manual,table:Table,paper:Paper,book:Book,laws:Laws,presentation:Presentation,picture:Picture,one:One,audio:Audio,email:Email,tag:Tag"
        parser_ids = settings.PARSERS if settings.PARSERS else default_parsers
        
        # Create tenant
        tenant = {
            "id": user_id,
            "name": nickname + "'s Kingdom",
            "llm_id": settings.CHAT_MDL or "Qwen/Qwen2.5-VL-72B-Instruct",
            "embd_id": settings.EMBEDDING_MDL or "Alibaba-NLP/gte-large-en-v1.5",
            "asr_id": settings.ASR_MDL or "paraformer-zh",
            "parser_ids": parser_ids,
            "img2txt_id": settings.IMAGE2TEXT_MDL or "qwen2-vl-7b-instruct",
            "rerank_id": settings.RERANK_MDL or "BAAI/bge-reranker-v2-m3",
        }
        TenantService.insert(**tenant)
        print("Tenant created")
        
        # Create user-tenant relationship
        usr_tenant = {
            "tenant_id": user_id,
            "user_id": user_id,
            "invited_by": user_id,
            "role": UserTenantRole.OWNER,
        }
        UserTenantService.insert(**usr_tenant)
        print("User-tenant relationship created")
        
        # Create LLM configurations
        tenant_llm = []
        
        # Add factory LLMs
        try:
            for llm in LLMService.query(fid=settings.LLM_FACTORY):
                tenant_llm.append({
                    "tenant_id": user_id,
                    "llm_factory": settings.LLM_FACTORY,
                    "llm_name": llm.llm_name,
                    "model_type": llm.model_type,
                    "api_key": settings.API_KEY,
                    "api_base": settings.LLM_BASE_URL,
                    "max_tokens": llm.max_tokens if llm.max_tokens else 8192,
                })
        except Exception as e:
            print(f"Warning: Could not add factory LLMs: {e}")
        
        # Add builtin embedding models (skip if configuration is missing)
        if settings.LIGHTEN != 1 and hasattr(settings, 'BUILTIN_EMBEDDING_MODELS') and settings.BUILTIN_EMBEDDING_MODELS:
            try:
                for buildin_embedding_model in settings.BUILTIN_EMBEDDING_MODELS:
                    try:
                        mdlnm, fid = TenantLLMService.split_model_name_and_factory(buildin_embedding_model)
                        tenant_llm.append({
                            "tenant_id": user_id,
                            "llm_factory": fid,
                            "llm_name": mdlnm,
                            "model_type": "embedding",
                            "api_key": "",
                            "api_base": "",
                            "max_tokens": 1024 if buildin_embedding_model == "BAAI/bge-large-zh-v1.5@BAAI" else 512,
                        })
                    except Exception as e:
                        print(f"Warning: Skipping embedding model {buildin_embedding_model}: {e}")
            except Exception as e:
                print(f"Warning: Could not add builtin embedding models: {e}")
        
        if tenant_llm:
            TenantLLMService.insert_many(tenant_llm)
            print(f"{len(tenant_llm)} LLM configurations created")
        else:
            print("No LLM configurations created")
        
        # Create root folder
        file_id = get_uuid()
        file = {
            "id": file_id,
            "parent_id": file_id,
            "tenant_id": user_id,
            "created_by": user_id,
            "name": "/",
            "type": FileType.FOLDER.value,
            "size": 0,
            "location": "",
        }
        FileService.insert(file)
        print("Root folder created")
        
        print("Tenant setup completed successfully")
        return True
        
    except Exception as e:
        print(f"Error creating tenant setup: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """
    Main function - MODIFY THE EMAIL BELOW
    """
    
    print("RAGFlow Tenant Setup Creator")
    print("=" * 35)
    
    # ========================================
    # MODIFY THIS EMAIL FOR EXISTING USER:
    # ========================================
    
    email = "<EMAIL>"    # ← Change this to existing user email
    
    # ========================================
    # END OF CONFIGURATION
    # ========================================
    
    print(f"Creating tenant setup for: {email}")
    
    success = create_tenant_setup(email)
    
    if success:
        print("Tenant setup completed successfully")
        print("User can now login to RAGFlow")
    else:
        print("Tenant setup failed")
        print("Check the error messages above for details")
    
    print("=" * 35)
    print("Script completed")


if __name__ == "__main__":
    main()
