# RAGFlow Internal Minio Upload Process Analysis

## Executive Summary

RAGFlow uses a **sequential, one-by-one upload strategy** for files to Minio storage. Each file is processed individually through the entire upload pipeline before moving to the next file. This approach prioritizes fault tolerance and progress tracking over bulk upload speed.

## Upload Strategy: Sequential Processing

### **Answer: Upload One → Process → Upload Next**

RAG<PERSON><PERSON> does **NOT** upload all files to Minio at once. Instead, it follows this pattern:

```
File 1 → Upload to Minio → Create DB Record → Move to File 2
File 2 → Upload to Minio → Create DB Record → Move to File 3
...
File N → Upload to Minio → Create DB Record → Complete
```

## Detailed Upload Sequence

### 1. File Service Upload Loop

```python
# In api/db/services/file_service.py (lines 410-473)
@classmethod
def upload_document(self, kb, file_objs, user_id):
    err, files = [], []
    
    # SEQUENTIAL PROCESSING - One file at a time
    for file in file_objs:  # ← Each file processed individually
        try:
            # Step 1: Validate file
            filename = duplicate_name(DocumentService.query, name=file.filename, kb_id=kb.id)
            filetype = filename_type(filename)
            
            # Step 2: Generate unique storage location
            location = filename
            while STORAGE_IMPL.obj_exist(kb.id, location):
                location += "_"
            
            # Step 3: INDIVIDUAL MINIO UPLOAD
            blob = file.read()
            if filetype == FileType.PDF.value:
                blob = read_potential_broken_pdf(blob)
            STORAGE_IMPL.put(kb.id, location, blob)  # ← SINGLE FILE UPLOAD
            
            # Step 4: Generate and upload thumbnail
            img = thumbnail_img(filename, blob)
            if img is not None:
                thumbnail_location = f"thumbnail_{doc_id}.png"
                STORAGE_IMPL.put(kb.id, thumbnail_location, img)  # ← THUMBNAIL UPLOAD
            
            # Step 5: Create database record
            doc = {
                "id": doc_id,
                "kb_id": kb.id,
                "parser_id": self.get_parser(filetype, filename, kb.parser_id),
                "parser_config": kb.parser_config,
                "created_by": user_id,
                "type": filetype,
                "name": filename,
                "location": location,
                "size": len(blob),
                "thumbnail": thumbnail_location,
            }
            DocumentService.insert(doc)
            
            # Step 6: Add file to file system
            self.add_file_from_kb(doc, kb_folder["id"], kb.tenant_id)
            
            # Step 7: Add to results and continue to next file
            files.append((doc, blob))
        except Exception as e:
            err.append(str(e))
    
    return err, files
```

### 2. Minio Storage Implementation

```python
# In rag/utils/minio_conn.py (lines 63-77)
def put(self, bucket, fnm, binary):
    for _ in range(3):  # Retry up to 3 times
        try:
            if not self.conn.bucket_exists(bucket):
                self.conn.make_bucket(bucket)

            r = self.conn.put_object(bucket, fnm,
                                     BytesIO(binary),
                                     len(binary)
                                     )
            return r
        except Exception:
            logging.exception(f"Fail to put {bucket}/{fnm}:")
            self.__open__()
            time.sleep(1)
```

### 3. API Endpoints That Trigger Uploads

#### Document Upload API (Web UI)

```python
# In api/apps/document_app.py (lines 50-76)
@manager.route("/upload", methods=["POST"])
def upload():
    kb_id = request.form.get("kb_id")
    file_objs = request.files.getlist("file")  # Multiple files possible
    
    # But processed sequentially in FileService.upload_document()
    err, files = FileService.upload_document(kb, file_objs, current_user.id)
```

#### API Upload (main_ragapi.py style)

```python
# In api/apps/api_app.py (lines 428-431)
blob = request.files['file'].read()
STORAGE_IMPL.put(kb_id, location, blob)  # Individual file upload
```

## Visual Upload Flow

```
📁 PDF Folder (100 files)
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    UPLOAD PHASE (Sequential)                │
├─────────────────────────────────────────────────────────────┤
│  File 1:                                                   │
│    1. Validate filename and type                           │
│    2. Check storage location availability                  │
│    3. STORAGE_IMPL.put(kb_id, "file1.pdf", content)       │
│    4. STORAGE_IMPL.put(kb_id, "thumbnail_1.png", image)   │
│    5. DocumentService.insert(metadata)                    │
│  ────────────────────────────────────────────────────────  │
│  File 2:                                                   │
│    1. Validate filename and type                           │
│    2. Check storage location availability                  │
│    3. STORAGE_IMPL.put(kb_id, "file2.pdf", content)       │
│    4. STORAGE_IMPL.put(kb_id, "thumbnail_2.png", image)   │
│    5. DocumentService.insert(metadata)                    │
│  ────────────────────────────────────────────────────────  │
│  ... (repeat for all 100 files)                           │
└─────────────────────────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────────────────────────┐
│                   PROCESSING PHASE (Batch)                 │
├─────────────────────────────────────────────────────────────┤
│  Single API call: POST /datasets/{id}/chunks              │
│  with document_ids=[doc1, doc2, ..., doc100]              │
└─────────────────────────────────────────────────────────────┘
```

## Secondary Upload: Extracted Images During Processing

During document processing, RAGFlow also uploads extracted images to Minio:

```python
# In rag/svr/task_executor.py (lines 277-296)
async def upload_to_minio(document, chunk):
    try:
        async with minio_limiter:  # Rate limiting for concurrent uploads
            # For each chunk that contains an extracted image
            if d.get("image"):
                output_buffer = BytesIO()
                if isinstance(d["image"], bytes):
                    output_buffer = BytesIO(d["image"])
                else:
                    d["image"].save(output_buffer, format='JPEG')
                
                # ADDITIONAL MINIO UPLOAD for extracted images
                await trio.to_thread.run_sync(
                    lambda: STORAGE_IMPL.put(task["kb_id"], d["id"], output_buffer.getvalue())
                )
```

### Concurrent Image Uploads

Unlike the initial document uploads, extracted images are uploaded concurrently:

```python
# In rag/svr/task_executor.py (lines 306-308)
async with trio.open_nursery() as nursery:
    for ck in cks:  # For each chunk
        nursery.start_soon(upload_to_minio, doc, ck)  # Concurrent image uploads
```

## Storage Factory Pattern

RAGFlow uses a factory pattern to abstract storage operations:

```python
# In rag/utils/storage_factory.py
class StorageFactory:
    storage_mapping = {
        Storage.MINIO: RAGFlowMinio,
        Storage.AZURE_SPN: RAGFlowAzureSpnBlob,
        Storage.AZURE_SAS: RAGFlowAzureSasBlob,
        Storage.AWS_S3: RAGFlowS3,
        Storage.OSS: RAGFlowOSS,
    }

STORAGE_IMPL_TYPE = os.getenv('STORAGE_IMPL', 'MINIO')
STORAGE_IMPL = StorageFactory.create(Storage[STORAGE_IMPL_TYPE])
```

## Error Handling and Retries

The Minio upload implementation includes retry logic:

```python
# In rag/utils/minio_conn.py
def put(self, bucket, fnm, binary):
    for _ in range(3):  # Retry up to 3 times
        try:
            # Upload logic
            return r
        except Exception:
            logging.exception(f"Fail to put {bucket}/{fnm}:")
            self.__open__()  # Reconnect
            time.sleep(1)    # Wait before retry
```

## Summary

### **Upload Strategy: Sequential (One-by-One)**
- ✅ Files uploaded individually to Minio
- ✅ Each file gets complete processing before next file
- ✅ Database records created one by one
- ✅ Thumbnails generated and stored individually
- ✅ Excellent error handling - failed uploads don't affect others

### **Processing Strategy: Batch**
- ✅ All document IDs collected after uploads complete
- ✅ Single API call initiates parsing for all documents
- ✅ RAGFlow processes documents in parallel during parsing
- ✅ Efficient resource utilization during processing phase

### **Extracted Image Upload: Concurrent**
- ✅ Images extracted during processing uploaded concurrently
- ✅ Rate limited to prevent overwhelming Minio
- ✅ Async operations for better performance

## Implications

1. **Upload Performance**: For large numbers of files, sequential uploading may be slower than batch uploading
2. **Fault Tolerance**: If one file fails, others can still succeed
3. **Progress Tracking**: Easy to track progress per file
4. **Memory Usage**: Only one file loaded in memory at a time during upload
5. **Scalability**: Simple to scale for large numbers of files, but not optimized for speed
