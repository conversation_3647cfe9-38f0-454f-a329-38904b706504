#!/usr/bin/env python3
"""
Debug script to check <PERSON>'s login issues
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService
from werkzeug.security import generate_password_hash, check_password_hash


def debug_carl_user():
    """Debug <PERSON>'s user account in detail."""
    
    email = "<PERSON><PERSON>@viridiengroup.com"
    password = "Carl20250812"
    
    print("🔍 DEBUGGING CARL'S LOGIN ISSUE")
    print("=" * 50)
    
    # Check different email variations
    email_variations = [
        "<PERSON><PERSON>@viridiengroup.com",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    found_user = None
    actual_email = None
    
    for email_var in email_variations:
        print(f"\n🔍 Checking email: {email_var}")
        users = UserService.query(email=email_var)
        if users:
            found_user = users[0]
            actual_email = email_var
            print(f"✅ FOUND USER with email: {email_var}")
            break
        else:
            print(f"❌ No user found with email: {email_var}")
    
    if not found_user:
        print("\n❌ NO USER FOUND with any email variation!")
        return False
    
    # Display user details
    print(f"\n📋 USER DETAILS:")
    print(f"   Email: {found_user.email}")
    print(f"   Nickname: {found_user.nickname}")
    print(f"   User ID: {found_user.id}")
    print(f"   Status: {found_user.status}")
    print(f"   Is Superuser: {found_user.is_superuser}")
    print(f"   Login Channel: {found_user.login_channel}")
    print(f"   Password Hash: {found_user.password[:50]}...")
    
    # Test password verification
    print(f"\n🔐 PASSWORD TESTING:")
    print(f"   Testing password: {password}")
    
    # Method 1: Direct hash check
    try:
        hash_check = check_password_hash(str(found_user.password), password)
        print(f"   Direct hash check: {'✅ PASS' if hash_check else '❌ FAIL'}")
    except Exception as e:
        print(f"   Direct hash check: ❌ ERROR - {e}")
    
    # Method 2: UserService.query_user (same as login)
    try:
        login_user = UserService.query_user(actual_email, password)
        print(f"   UserService.query_user: {'✅ PASS' if login_user else '❌ FAIL'}")
    except Exception as e:
        print(f"   UserService.query_user: ❌ ERROR - {e}")
    
    return found_user


def fix_password_properly():
    """Fix Carl's password with proper debugging."""
    
    email = "<EMAIL>"
    password = "Carl20250812"
    
    print(f"\n🔧 FIXING PASSWORD FOR: {email}")
    print("=" * 50)
    
    # Find user (try different email cases)
    users = UserService.query(email=email)
    if not users:
        # Try lowercase
        email = email.lower()
        users = UserService.query(email=email)
    
    if not users:
        print(f"❌ User not found!")
        return False
    
    user = users[0]
    print(f"✅ User found: {user.nickname}")
    
    # Generate new password hash
    new_hash = generate_password_hash(password)
    print(f"📝 New password hash: {new_hash[:50]}...")
    
    # Update password
    try:
        update_dict = {"password": new_hash}
        UserService.update_user(user.id, update_dict)
        print("✅ Password updated in database")
        
        # Verify the update worked
        updated_users = UserService.query(email=user.email)
        if updated_users:
            updated_user = updated_users[0]
            print(f"✅ Verification - new hash: {updated_user.password[:50]}...")
            
            # Test login immediately
            test_user = UserService.query_user(user.email, password)
            if test_user:
                print("✅ LOGIN TEST PASSED!")
                return True
            else:
                print("❌ LOGIN TEST FAILED!")
                return False
        else:
            print("❌ Could not verify update")
            return False
            
    except Exception as e:
        print(f"❌ Error updating password: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main debug function."""
    print("RAGFlow Login Debug Tool")
    print("=" * 30)
    
    # Step 1: Debug current state
    user = debug_carl_user()
    
    if user:
        print(f"\n" + "=" * 50)
        print("DIAGNOSIS:")
        
        # Check if password verification works
        login_works = UserService.query_user(user.email, "Carl20250812")
        
        if login_works:
            print("✅ Password is correct - login should work")
            print("🤔 If login still fails on website, check:")
            print("   - Email case sensitivity")
            print("   - Browser cache/cookies")
            print("   - Network issues")
        else:
            print("❌ Password verification failed - fixing now...")
            
            # Fix the password
            if fix_password_properly():
                print("\n🎉 PASSWORD FIXED! Try logging in now:")
                print(f"   Email: {user.email}")
                print(f"   Password: Carl20250812")
            else:
                print("\n❌ Failed to fix password")
    else:
        print("\n❌ User not found - need to create user first")
    
    print("\n" + "=" * 50)
    print("Debug completed.")


if __name__ == "__main__":
    main()
