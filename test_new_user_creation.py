#!/usr/bin/env python3
"""
Test script to verify the new consolidated user creation works
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService


def test_user_creation_result(email):
    """Test if user was created successfully with complete setup."""
    
    print(f"🧪 TESTING USER CREATION RESULT")
    print("=" * 40)
    print(f"Email: {email}")
    
    # Check user exists
    users = UserService.query(email=email)
    if not users:
        print("❌ User not found!")
        return False
    
    user = users[0]
    user_id = user.id
    print(f"✅ User found: {user.nickname} (ID: {user_id})")
    
    # Check tenant exists
    tenants = TenantService.query(id=user_id)
    if not tenants:
        print("❌ Tenant not found!")
        return False
    
    tenant = tenants[0]
    print(f"✅ Tenant found: {tenant.name}")
    
    # Check tenant access
    try:
        tenant_info = TenantService.get_info_by(user_id)
        if tenant_info:
            print(f"✅ Tenant access works: {len(tenant_info)} records")
            return True
        else:
            print("❌ Tenant access failed!")
            return False
    except Exception as e:
        print(f"❌ Tenant access error: {e}")
        return False


def main():
    """Main test function."""
    print("RAGFlow User Creation Test")
    print("=" * 30)
    
    # Test Carl's account (should exist from previous creation)
    carl_email = "<EMAIL>"
    carl_works = test_user_creation_result(carl_email)
    
    print(f"\n" + "=" * 40)
    print("SUMMARY:")
    print(f"{'✅' if carl_works else '❌'} Carl's account: {'WORKING' if carl_works else 'BROKEN'}")
    
    if carl_works:
        print("\n🎉 SUCCESS! The consolidated script works!")
        print("You can now use create_ragflow_user.py to create any user.")
    else:
        print("\n❌ Issues found. Check the setup.")
    
    print(f"\n" + "=" * 40)
    print("Test completed.")


if __name__ == "__main__":
    main()
