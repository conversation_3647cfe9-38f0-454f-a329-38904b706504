
        <!DOCTYPE html>
        <html>
        <head>
            <title>Document Chunks Visualization</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .chunk { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
                .chunk-header { background: #f5f5f5; padding: 5px; margin-bottom: 10px; }
                .chunk-content { white-space: pre-wrap; }
                .chunk-meta { color: #666; font-size: 0.9em; margin-top: 10px; }
                .highlight { background-color: yellow; }
                .position-box { border: 1px dashed #999; margin: 5px; padding: 3px; display: inline-block; }
            </style>
        </head>
        <body>
            <h1>Document Chunks Visualization</h1>
            <div id="chunks">
        
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 2bd2b749f4ef6fd7 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">actually have the underlying facts.That misplaced confidence disables the safety mechanism, and the model fills in tblanks with something that sounds right but isn’t. Anthropic even demonstrated t it could intentionally trigger hallucinations by manually activating certain internafeatures, causing Claude to repeatedly give the same, clearly incorrect response. This suggests hallucinations aren’t just random errors. They’re often predictablebreakdowns in an internal check, one that’s meant to decide whether the model ha enough knowledge to answer in the first place.That aligns with findings from other studies showing that models have a kind of internal sense of what they do and don’t know. Some researchers even refer to thisknowledge awareness—the model’s ability to assess its own confidence and decidewhether to respond or defer.The problem is that this self-awareness isn’t perfect.So when your company’s chatbot confidently makes up a fact, it may genuinely thi knows the answer, even when it doesn’t. Understanding this gives AI developers apowerful tool: the ability to improve prompts, adjust system settings, or design smarter safeguards to ensure that, when the model is unsure, it leans toward being cautious.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 04e37bcb486de40c | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">Claude’s internal activations matched the process of computing the square root of But when asked to calculate the cosine of a very large number—a problem beyondmodel’s real capabilities—Claude still offered a detailed explanation.The catch? It was completely made up.There was no evidence that the model had done any real math. Instead, it generate plausible-sounding procedure and landed on an arbitrary answer.In other words, the explanation sounded good, but it wasn’t real.What’s more, this behavior gets worse when the model picks up on what the user expects to hear. In one experiment, researchers gave Claude a misleading hint for difficult question. The model responded by reverse-engineering a justification to match the hint.This is an example of motivated reasoning—starting with a preferred conclusion,inventing a rationale to support it.From a reliability standpoint, that’s concerning. AI can generate convincing, logic sounding arguments that are, in fact, false (especially when asked to explain its reasoning).The upside? With the right interpretability tools, we can begin to tell the differenc</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a981b58dd4a3d4d9 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">Final Thoughts By tracing how AI models form and process ideas, we’re stepping into a new phase one where we view these systems less as mysterious black boxes and more as compcognitive tools that can be studied, debugged, and, ideally, trusted.We’ve seen that AI reasoning can sometimes mirror human thought (abstracting concepts, planning ahead)and other times feel entirely foreign, like inventing odd ways to solve problems or faking a logical explanation when none exists.Each insight, whether it's Claude crafting a rhyme in advance or fabricating a matproof, reveals another layer of how these systems work and how much more there uncover.Subscribe to Artificial CornerHundreds of paid subscribersArtificial Intelligence in plain English. Join thousands of tech enthusiasts, programmers, and data profess looking to make the most out of AI.Upgrade to paid</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 1a24e129b33ef94d | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">between genuine reasoning and on-the-fly improvisation. And that might be one othe most valuable insights we have about how these systems actually work.Explaining Hallucinations: When Knowledge Breaks DownIf you’ve ever interacted with an AI, chances are you’ve seen it hallucinate—confidently stating something that’s completely false.But why does this happen?Anthropic’s research uncovered what looks like an internal tug-of-war betweenknowing and not knowing.It turns out Claude has a built-in “default refusal”mechanism, a kind of safety nettells the model to respond with something like “I can’t answer that”to most questunless it’s really sure. That’s a sensible precaution. A responsible AI shouldn’t gueunless it has solid information.But there's another circuit that does the opposite—it kicks in when the model dete that a question involves a known topic or entity. When that happens, it overrides t refusal and allows the model to respond. You can see this dynamic in action in the image below.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 50dbf123aca94457 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">Essentially, one process estimates, “This should land somewhere in the 90s,”while another calculates, “6 +9 ends in 5.”These separate tracks then converge to produthe correct answer: 95.This kind of divide-and-conquer approach isn’t how we usually teach math to hum but it works remarkably well. It’s almost like the model developed its own unique shortcut during training.What’s even more interesting is that Claude doesn’t seem to know it’s doing this. When asked, “How did you get 95?”Claude responds like a student would: “I addethe ones digits.”JBWhat is36+59?Answer in oneword95JB Briefly, how did you get that?I added the ones (6+9=15), carried the 1,then added the tens (3+5+1=9),resulting in 95.Credits: AnthropicBut internally, that’s not what happened at all.This is a clear example of what researchers call unfaithful explanations—when a</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b22d1d32a88fa2e8 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">Michael Jordan→BasketballMichaelBatkin→Can'tAnswerapologize, but I cannotHuman: Which sportHuman: Which sport does Michaeldoes Michael Batkinfind a definitive recorBasketballJordan play? Answer in one word.play? Answer in oneof a sports figure nameAssistant:word.Assistant:Michael Batkin.WithoutSayCan'tBasketballAnswerUnknownKnownAnswerNameAssistantAssistalMichael Jordanplay?Answerplay?AnswelHuman:WhichHuman:WhichMichaelMichaelJordanin one word.in one wordBatkinsport doessportdoesAssistant:Assistant:Credits: AnthropicWhen the question is about a well-known person or a widely discussed topic, the “know this”signal takes over, and Claude answers. When it’s about something clea unfamiliar, the “I don’t know”signal stays active, and the model appropriately dec to respond.Hallucinations happen in the gray area between those two extremes—when Claudrecognizes just enough of the question to feel confident answering, but doesn’t</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a170c5392cce5924 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">We’re Finally Starting to Understand How AI WorksA recent study by Anthropic offers a glimpse into the AI black boxKEVIN GARGATE OSORIO AND THE PYCOACHAPR 18, 2025282 1 Image made with GPT 4oEver since I started developing, learning, and working with AI, there’s always beencomponent we in the tech world refer to as a black box—an element that can be, tosome extent, unpredictable.Chances are, many of us have spent time analyzing outputs, tweaking training dat and digging into attention patterns. Still, a large part of the AI's decision-makingprocess has remained hidden.At least, that was the case until a few weeks ago.In a recent study titled "Tracing Thoughts in Language Models,"researchers at Anthropic claim they’ve caught a glimpse inside the mind of their AI, Claude, and observed it thinking. Using a technique they compare to an “AI microscope,”they were able to trace Claude’s internal reasoning steps with an unprecedented level o detail.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 40ff5905d618cf30 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">The opposite of"small"is“largeQuote (Chinese)“小"的反义词是”大(Chinese for"biQuote (French)Le contraire de"petit"estgrand(French for"bigSHAREDAntonymSimplified attributiongraphsfortranslatedconceptversionsofthesameprompt,asking Haikuwhat the opposite of “large isin differentlanguages.Significant parts of thecomputationappear tobe overlappingSmallLarge“multilingual” pathways.Note that theseareconceptconcepthighlysimplified.Credits: AnthropicBased on their findings, Anthropic discovered that Claude activates the same inte concepts for equivalent ideas across different languages.For instance, when asked for “the opposite of small”in multiple languages, the mo didn’t take completely different paths for each translation. Instead, it relied on ashared understanding of “smallness,”the concept of “opposite,”and the idea of “largeness,”before finally translating that idea into large in English, 大in Chinese grand in French.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ee65fec84c332700 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">Planning Ahead: Word by Word or Sentence by Sentence?Language models are trained to generate text one word at a time—a process that might seem inherently short-sighted.For a while, it was assumed that models like GPT-4 or Claude were mostly just “thinking”about the next word, maybe keeping track of context, but not doing any serious long-term planning.But Anthropic’s latest research challenges that assumption.When weinspectthefeatures directlyafterthefirstrhyme,weseeClaudeArhymingcouplet:planning about the word “rabbit”as apossible candidate forthefuturerhyme.He saw a carrot and had to grab it,Q→His hunger waslike a starving rabbit"“rabbit"conceptIf we intervene by suppressing thisconceptSuppressionatthispoint,Claudefindsand uses anotheA rhyming couplet:candidate,adjusting itsverse tonaturallyarriveatthisnewending.He saw a carrot and had to grab it,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 2210f809c28ffa84 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">down the key components of the code:1. Embedding Extraction:#Device configurationdevice =torch.device("cuda"if torch.cuda.is_available()else "cpu")#Store embeddingscls_embeddings =[]mean_pooled_embeddings =[]#Extract input data from the test datasetinput_ids =test_dataset['input_ids']attention_mask =test_dataset['attention_mask']#Convert input data to tensors and move them to the same device as the modelinput_ids =torch.tensor(input_ids).to(device)attention_mask =torch.tensor(attention_mask).to(device)#Disable gradient calculationswith torch.no_grad(): for input_id, attention_mask in zip(input_ids, attention_mask): #Forward pass, return hidden states outputs =model(input_ids=input_id.unsqueeze(0).to(device),attention_mask=attention_mask.unsqueeze(0).to(device),output_hidden_states=True) #Extract embeddings from the hidden states hidden_states =outputs.hidden_stateslast_hidden_state =hidden_states[-1]#The last layer hidden state</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 160391455fdb1cf1 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">2. Applying t-SNE:tsne_cls =TSNE(n_components=2, random_state=42).fit_transform(cls_embeddings)tsne_mean_pooled =TSNE(n_components=2, random_state=42).fit_transform(mean_poo3. Visualization with Plotly:labels =test_dataset['labels']+1 #Extract labelsreview_texts =test_dataset['reviewText']#Extract review texts#Prepare the data for Plotly plotsplot_data_cls ={'t-SNE Dimension 1': tsne_cls[:, 0],'t-SNE Dimension 2': tsne_cls[:, 1],'Label': labels, 'Review Text': review_texts}plot_data_mean_pooled ={'t-SNE Dimension 1': tsne_mean_pooled[:, 0],'t-SNE Dimension 2': tsne_mean_pooled[:, 1],'Label': labels,'Review Text': review_texts}#Customize colorbar to show integer labelscolorbar_tickvals =list(range(1, 6))</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 73e2681dcf972086 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content"> #[CLS]token embeddings cls_embedding =last_hidden_state[:, 0, :].detach().cpu().numpy()cls_embeddings.append(cls_embedding.flatten()) #Mean poolinginput_mask_expanded =attention_mask.unsqueeze(-1).expand(last_hidden_ssum_embeddings =torch.sum(last_hidden_state *input_mask_expanded, 1)sum_mask =torch.clamp(input_mask_expanded.sum(1), min=1e-9)mean_pooled_embeddings.append(mean_pooled_embedding.flatten()) mean_pooled_embedding =(sum_embeddings /sum_mask).detach().cpu().nump#convert to numpy arraycls_embeddings =np.array(cls_embeddings)mean_pooled_embeddings =np.array(mean_pooled_embeddings)Here, I extract two types of embeddings from the BERT model —[CLS]tokenembeddings and mean-pooled embeddings. The [CLS]token, a special token used in BERT, provides a comprehensive representation of the entire input sequence. Meanpooling, on the other hand, averages the embeddings across all tokens, giving us another perspective.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> e222104f3cd084c6 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">Anomaly DetectionWhat are those yellow 5 stars doing on the far right of the plot where 1 star and 2star reviews are clustered together? Ah, the enigma of online reviews —where “The fabric is not good”meets a 5-star rating. It’s the digital equivalent of a dessert criticsaying “This cake tastes like cardboard…I’ll have another slice, please!”! This reminds us that the quality of a dataset is important and the fact that our groundtruth might not always be accurate.ReviewText=Thefabricisnot gooLabel=5There are multiple cases like this: Here is another example:ReviewText=ltsnotverygood，largRemoving these data points from our training data would result in a better machinelearning model since these ratings would cause confusion to the model.ConclusionIn our analysis, the fine-tuned BERT model successfully differentiated between high and low star customer reviews, as shown by the t-SNE visualizations of theembeddings. However, we noted outliers, such as instances where customers gave a</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 946b6e1e7536e6a4 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">Click EmailClick PaidSearch LinkSearch LinkLinkMina MehdiniaIntroduction to Markov Chain Attribution Modeling in Digital Marketing Part#1Unlocking Customer Insights: A Deep Dive into Markov Chain Attribution Modeling for DigitalMarketers49May 31, 2024Mina MehdiniaAutomating Review Evaluation with OpenAI’s GPT-3.5Evaluating reviews manually can be time-consuming and, oftentimes, requires an objectiveperspective. With the advancements in machine…Sep 6, 202355Mina MehdiniaFine Tunning BERT Model for Amazon Product Review and Deploying itinto Hugging Face Model Hub:In this blog post, we will cover the steps to fine-tune a BERT model for sentiment analysis onAmazon product reviews and subsequently…Sep 27, 2023150</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ac73352432b976bb | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">APPENDICESAbbreviations used in Analytical Data Sheets 1. Analytical Procedures and Techniques 2. VOLUMEIV INTRODUCTIOA Geochemical Study of the Composition and correlation of 1. 14 Crude Oils from Southeast Turkey (Report No. 4819P/D)Further Studies of 14 Crude Oils from Southeast Turkey 2. (Report No. 4966P/D)3. Results of Trace Element Analyses of 14 Crude Oils from Southeast Turkey (Report No. 4965P/D)A Geochemical Study of the Composition and Correlation of 4. 30 Crude Oils from Southeast Turkey (Report No. 5150P/D)A Geochemical Evaluation of the Bedinan Formation in the 5. Akcakale-1, Bakuk-1, Ceylanpinar-1, Girmeli-1 andSinirtepe-2 wells (Report No. 5010P/D)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b38509eca77bdea7 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">Wells Analysed in Study (Tables Jl and J2)K. Source Rock Quality Data for Relevant Formations (Tables Kl-K6)VOLUMEII (This Volume)ENCLOSUREMap Showing Locations of Well Sections, Measured Stratigraphic 1. Sections and Hydrocarbons Analysed in the Study Sketch Map Showing Major Structural Features in Southeast 2. Turkey Spore Colour Indices at the Top of the Maastrichtian aged Sediments3. Spore Colour Indices at the Top of the Aptian -Turonian aged 4. Mardin Group Spore Colour Indices at the Base of the Mesozoic Sediments. 5. Spore Colour Indices at the Top of the Late Silurian -Early 6. Devonian aged Dadas Formation 7. Spore Colour Index Gradients Maximum Depth of Burial at the Top of the Aptian -Turonian 8. </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 32f67f5fd800b31a | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content"> Geochemical Evaluation of the Dadas and Bedinan Formations A3. in the Agachan-2, Handof-1, Kevan-1, Sinan-1 and Tasli-1wells (Report No, 5164P/D)APPENDICES1. Abbreviations used in Analytical Data Sheets Analytical Procedures and Techniques 2, VOLUMEV I INTRODUCTIOA Geochemical Evaluation of the Kastel Formation in the 1, G, Sahaban-6, Ulas-4 and Sivritepe-1 wells (Report No. 4928P/D)A Geochemical Evaluation of the Kastel Formation in the 2, Cemberlitas-4, Gedik-1, Firat-2 and Durukaynak-1 wells(Report No, L1929P/D)A Geochemical Evaluation of the Kastel Formations in the 3. </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 59eceee1e3c5e9fc | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">66 Oils Table 8 Analytical Data for Asphaltites 74 Table 9 Analytical Data for Oilstain Extracted from Sediments 77 Table 10 Gas Chromatography -Mass Spectrometry Data for Extracted 78 Oil stains Table 11 Oil Groups -Reserves Data 86 FIGURESSummary of Stratigraphy Figure 1 6 Figure 2 Original Oil Source Potential of the Dadas Formation after 41 Figure 3 Oil Productivity of the Dadas Formation after 41 Figure 4 Plot of Carbon Isotope Data for Selected Source Rock 52 Alkane and Aromatic Fractions riangular Diagram showing Relative Amounts of c27 , c28 and c29 Sa(H), 14a(H), 17a(H), 20R Steranes for differentFigure 5 T53 Source Rock Units Figure 6 Plot of Carbon Isotope Data for Turkish Oil Alkane and </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 1767fe69a7336a6e | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">excels in bilingual OCR and grounding with 16xfewer tokens. arXiv preprint arXiv:2410.05261.Jianshu Zhang, Jun Du, Shiliang Zhang, Dan Liu, Yu-long Hu, Jinshui Hu, Si Wei, and Lirong Dai. 2017.Watch, attend and parse: An end-to-end neural net-work based approach to handwritten mathematicalexpression recognition. Pattern Recognition, 71:196-206.Weichao Zhao, Hao Feng, Qi Liu, Jingqun Tang,Binghong Wu, Lei Liao, Shu Wei, Yongjie Ye, HaoLiu, Wengang Zhou, et al. 2024a. TabPedia: Towards comprehensive visual table understanding with con- cept synergy. In Proceedings of the Neural Infor- mation Processing Systems, volume 37, pages 7185-7212.Zhen Zhao, Jingqun Tang, Chunhui Lin, Binghong Wu,Yuan Xie. 2024b. Multi-modal in-context learning</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 4c329775ed1b1fdb | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> Can Huang, Hao Liu, Xin Tan, Zhizhong Zhang, and makes an ego-evolving_ scene text recognizer. InProceedings of the IEEE Conference on ComputerVision and Pattern Recognition, pages 15567-i5576.Xu Zhong, Elaheh ShafieiBavani, and Antonio Ji-meno Yepes. 2020. Image-based table recognition:data, model, and evaluation. In Proceedings of theEuropean Conference on Computer Vision, pages564-580.12</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 5213d5ba46daf07a | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">explorations with GPT-4V (ision). arXiv preprintQu, Fukai Shang, et al. 2024b. MinerU: An open-arXiv:2309.17421.source solution for precise document content extrac-Yuan Yao, Tianyu Yu, Ao Zhang, Chongyi Wang, Junbotion. arXiv preprint arXiv:2409.18839.Cui, Hongji Zhu, Tianchi Cai, Haoyu Li, WeilinDongsheng Wang, Natraj Raman, Mathieu Sibue,Zhiqiang Ma, Petr Babkin, Simerjot Kaur, YulongZhao, Zhihui He, et al. 2024. MiniCPM-V: A GPT-4V level MLLM on your phone. arXiv preprintPei, Armineh Nourbakhsh, and Xiaomo Liu. 2024c.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 5f17e212d3a1d8b8 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">the task is to classify text samples into…Jan 315Output Token EmbeddingsDecoderBlockTransformer Decoder BlockFeed ForwardTransformer Decoder BlockLayer NormAiuo：Transformer Decoder BlockMasked Multi-HeadSelf-AttentionQKGPTTVPositional EncodingLayer NormInput Token EmbeddingsLM PoThe Rise of LLMs: From GPT to Modern InnovationsIf you’re not a Medium subscriber, click here to read the full article.Oct 21, 20242 1>iKaushicbaravindSimilarity of Word Embeddings with BERT: A Comprehensive DiscussionExploring how BERT embeddings and similarity reveal the contextual meaning of words in different sentences.Oct 2, 2024 50evebingrnebioningsBertopicNeralmotletnewobiolsBERTOPIC800-WoronetwarkscdrepdingsMuhammad Anang MahrubMy Journey into Topic Modeling: Understanding LDA and BERTopic IntroductionFeb 2See more recommendations</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3394fe89072ebecb | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Processing Systems, pages 5998-6008.Furu Wei, and Ming Zhou. 2020b. LayoutLM: Pre-training of text and layout for document image un-Bin Wang, Zhuangcheng Gu, Guang Liang, Chao Xu,Bo Zhang, Botian Shi, and Conghui He. 2024a.derstanding. In Proceedings of the ACM SIGKDDInternational Conference on Knowledge DiscoveryUnimerNet: A universal network for real-world math-& Data Mining, pages 1192-1200.ematical expression recognition. arXiv preprint Zhengyuan Yang, Linjie Li, Kevin Lin, JianfengarXiv:2404.15254.Wang, Chung-Ching Lin, Zicheng Liu, and Lijuan Bin Wang, Chao Xu, Xiaomeng Zhao, Linke Ouyang,Wang. 2023. The dawn of LMMs: Preliminary Fan Wu, Zhiyuan Zhao, Rui Xu, Kaiwen Liu, Yuan</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3a671f09320482c1 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Aneut-/pRuhaver5sluaETeu9Λ0o6Figure 3: An example of the attention mechanism following long-distance dependencies in theencoder self-attention in layer 5 of 6. Many of the attention heads attend to a distant dependency of the verb ‘making', completing the phrase ‘making...more difficult'. Attentions here shown only for the word ‘making'. Different colors represent different heads. Best viewed in color.13Input-lnputLayer5pirOlddebuM=Inpait-npot Laye5oiuidcSuis0SepeN M     C.mmde.buss!b.$Mmeng.n<SO>6<ped>nousWFigure 4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora resolution. Top:Full attentions for head 5. Bottom: Isolated attentions from just the word “its′ for attention heads 5</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ae1e39bee0bae4c8 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">? BASIN SILL ?SEDIMENT SOURCI DIRECTIONDADAS- ⅢUNIT NOT DEPOSITEDRAULAND AREASOURCE ROCK QUALITY MAP- DADASDAyereenGEVASINERTINIGOMANIBRIK FORMATIONVITRINEERODED OR NOT DEPOSITED123VITRINITEANDNO DATASAPROPEL?AVAILABLEIRAQPETHBLAOM SEUTHEASTVNSTUDYSOURCE ROCKGOMANIIBRIKFORMATION(PERMENCLOSURE13ULUDERE FORMATIONLEANANDERODED OR NOT DEPOSITEDINERTINITIC一LEAN ANDINERTINITICIRAOSAPROPEL ANDVITRINITE?SOURCE ROCK QUALITY MAPJLUDERE FORMATION (TRIASSIC)ENCLOSURE1</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ff3f2ac7ae06be81 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">Results of Petroleum Geochemical Studies of the Interval 6. 6m-3414m, of the Dara-2 well (Report No, 4766P/D)Results of Petroleum Geochemical Studies of the Interval 7, 4000'-5200', of the Gercus-1 well (Report No. 4805P/D)Results of Petroleum Geochemical Studies of the Interval 8. 3070'-7690', of the Kavikadag-1 well (Report No. 4765P/D)Results of Petroleum Geochemical Studies of the Interval2020m-3704m, of the Kayayolu-2 well (Report No. 4871P/D)9. 10. Results of Petroleum Geochemical Studies of the Interval1300'-10590', of the Selmo-1 well (Report No, 4750P/D)11. Results of Petroleum Geochemical Studies of the Interval110'-5280', of the West Savur-1 well (Report No, 4760P/D)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 5645eaee6143d6f2 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">LIKE REPLYRoy Vella18 AprFascinating... we may eventually figure out how we think as well along the way.LIKE REPLY©2025 Frank Andrade ∙ Privacy ∙Terms ∙Collection noticeSubstack is the home for great culture</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3ee4ad339a31f008 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">[10] Alex Graves.  Generating sequences with recurrent neural networks. arXiv preprintarXiv:1308.0850, 2013.[11] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for im-age recognition. In Proceedings of the IEEE Conference on Computer Vision and PatternRecognition, pages 770-778, 2016.[12] Sepp Hochreiter, Yoshua Bengio, Paolo Frasconi, and Jurgen Schmidhuber. Gradient flow inrecurrent nets: the difficulty of learning long-term dependencies, 2001.[13] Sepp Hochreiter and Jirgen Schmidhuber.  Long short-term memory.  Neural computation,9(8):1735-1780, 1997.[14] Zhongqiang Huang and Mary Harper. Self-training PCFG grammars with latent annotationsacross languages. In Proceedings of the 2009 Conference on Empirical Methods in Natural</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 4d3b6d5803a52b3d | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">model. In Empirical Methods in Natural Language Processing, 2016.[28]  Romain Paulus, Caiming Xiong, and Richard Socher. A deep reinforced model for abstractivesummarization. arXiv preprint arXiv: 1705.04304, 2017.[29] Slav Petrov, Leon Barrett, Romain Thibaux, and Dan Klein. Learning accurate, compact,and interpretable tree annotation. In Proceedings of the 21st International Conference onComputational Linguistics and 44th Annual Meeting of the ACL, pages 433-440. ACL, July2006.[30] Ofir Press and Lior Wolf.  Using the output embedding to improve language models. arXiv preprint arXiv:1608.05859, 2016.[31]  Rico Sennrich, Barry Haddow, and Alexandra Birch. Neural machine translation of rare wordswith subword units. arXiv preprint arXiv: 1508.07909, 2015.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 0c63899016749f4a | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">machine translation. CoRR, abs/1406.1078, 2014.[6]  Francois Chollet. Xception: Deep learning with depthwise separable convolutions. arXiv preprint arXiv:1610.02357, 2016.[7]  Junyoung Chung, Caglar Gulcehre, Kyunghyun Cho, and Yoshua Bengio. Empirical evaluationof gated recurrent neural networks on sequence modeling. CoRR, abs/1412.3555, 2014. [8] Chris Dyer, Adhiguna Kuncoro, Miguel Ballesteros, and Noah A. Smith. Recurrent neuralnetwork grammars. In Proc. of NAACL, 2016.[9]  Jonas Gehring, Michael Auli, David Grangier, Denis Yarats, and Yann N. Dauphin. Convolu-tional sequence to sequence learning. arXiv preprint arXiv:1705.03122v2, 2017.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 078515e51d44baf8 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">[32] Noam Shazeer, Azalia Mirhoseini, Krzysztof Maziarz, Andy Davis, Quoc Le, Geoffrey Hinton,and Jeff Dean. Outrageously large neural networks: The sparsely-gated mixture-of-expertslayer. arXiv preprint arXiv:1701.06538, 2017.[33]  Nitish Srivastava, Geoffrey E Hinton, Alex Krizhevsky, Ilya Sutskever, and Ruslan Salakhutdi-nov. Dropout: a simple way to prevent neural networks from overfitting. Journal of MachineLearning Research, 15(1):1929-1958, 2014.[34] Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end memorynetworks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama, and R. Garnett, editors,Inc.,2015.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 93bd35e045691399 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">arXiv:1607.06450, 2016.[2]  Dzmitry Bahdanau, Kyunghyun Cho, and Yoshua Bengio. Neural machine translation by jointlylearning to align and translate. CoRR, abs/1409.0473, 2014.[3]  Denny Britz, Anna Goldie, Minh-Thang Luong, and Quoc V. Le. Massive exploration of neuralmachine translation architectures. CoRR, abs/1703.03906, 2017.[4]  Jianpeng Cheng, Li Dong, and Mirella Lapata. Long short-term memory-networks for machinereading. arXiv preprint arXiv:1601.06733, 2016.10[5]  Kyunghyun Cho, Bart van Merrienboer, Caglar Gulcehre, Fethi Bougares, Holger Schwenk, and Yoshua Bengio. Learning phrase representations using rnn encoder-decoder for statistical</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 6c0a3042666b5c36 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">on recurrent or convolutional layers. On both WMT 2014 English-to-German and WMT 2014English-to-French translation tasks, we achieve a new state of the art. In the former task our bestmodel outperforms even all previously reported ensembles.We are excited about the future of attention-based models and plan to apply them to other tasks. Weplan to extend the Transformer to problems involving input and output modalities other than text andto investigate local, restricted attention mechanisms to efficiently handle large inputs and outputs such as images, audio and video. Making generation less sequential is another research goals of ours.The code we used to train and evaluate our models is available at https://github.com/tensorflow/tensor2tensor.Acknowledgements We are grateful to Nal Kalchbrenner and Stephan Gouws for their fruitfulcomments, corrections and inspiration.References[1] Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization. arXiv preprint</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> c2ac6989701cc32f | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content"> in different ways, measuring the change in performance on English-to-German translation on the5We used values of 2.8, 3.7, 6.0 and 9.5 TFLOPS for K80, K40, M40 and P100, respectively.8Table 3: Variations on the Transformer architecture. Unlisted values are identical to those of the base model. All metrics are on the English-to-German translation development set, newstest2013. Listed perplexities are per-wordpiece, according to our byte-pair encoding, and should not be compared toper-word perplexities.PPLBLEUtrainparamsPdropdmodelNdffdkd~hEls×106steps(dev)(dev)100K512204864644.9225.865base80.10.165125.2924.951215.0012812825.54(A)25.816</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 40c9c622eb1b36f1 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">of WSJ)TrainingParserWSJ 23 F1Vinyals & Kaiser el al. (2014) [37]WSJ only, discriminative88.390.4Petrov et al. (2006) [29]Zhu et al. (2013) [40]WSJ only, discriminativeWSJ only, discriminative90.4WSJ only, discriminative91.7Dyer et al. (2016) [8]Transformer (4 layers)WSJ only, discriminative91.391.3Zhu et al. (2013) [40]semi-supervisedHuang & Harper (2009) [14]semi-supervised91.392.1McClosky et al. (2006) [26]semi-supervised semi-supervisedVinyals & Kaiser el al. (2014) [37]92.1Transformer (4 layers)semi-supervised92.793.0</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> e8a6a0ef61c6978b | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">dropout rate Pdrop = 0.1, instead of 0.3.For the base models, we used a single model obtained by averaging the last 5 checkpoints, which were written at 10-minute intervals. For the big models, we averaged the last 20 checkpoints. Weused beam search with a beam size of 4 and length penalty Q = 0.6 [38]. These hyperparameterswere chosen after experimentation on the development set. We set the maximum output length duringinference to input length + 50, but terminate early when possible [38].Table 2 summarizes our results and compares our translation quality and training costs to other modelarchitectures from the literature. We estimate the number of foating point operations used to train amodel by multiplying the training time, the number of GPUs used, and an estimate of the sustained single-precision floating-point capacity of each GPU 5 .6.2Model VariationsTo evaluate the importance of different components of the Transformer, we varied our base model</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 762cc72ff7ce0c0b | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">the hyperparameters described throughout the paper, each training step took about 0.4 seconds. We trained the base models for a total of 100,000 steps or 12 hours. For our big models,(described on thebottom line of table 3), step time was 1.0 seconds. The big models were trained for 300,000 steps(3.5 days).5.3OptimizerWe used the Adam optimizer [20] with β1 = 0.9, β2 = 0.98 and e = 10-9. We varied the learningrate over the course of training, according to the formula:lrate = dmos min(step_num-0.5,step_num warmup_steps-1.5)(3)nodeThis corresponds to increasing the learning rate linearly for the first warmup_steps training steps, and decreasing it thereafter proportionally to the inverse square root of the step number. We usedwarmup_steps = 4000.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3e35d8ca5165325e | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content"> Results66.1Machine Translation On the WMT 2014 English-to-German translation task, the big transformer model (Transformer (big) in Table 2) outperforms the best previously reported models (including ensembles) by more than 2.0 BLEU, establishing a new state-of-the-art BLEU score of 28.4. The configuration of this model islisted in the bottom line of Table 3. Training took 3.5 days on 8 P100 GPUs. Even our base modelsurpasses all previously published models and ensembles, at a fraction of the training cost of any ofthe competitive models.On the WMT 2014 English-to-French translation task, our big model achieves a BLEU score of 41.0, outperforming all of the previously published single models, at less than 1/4 the training cost of theprevious state-of-the-art model. The Transformer (big) model trained for English-to-French used</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> e41455d844562221 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">5.4 RegularizationWe employ three types of regularization during training:Table 2: The Transformer achieves better BLEU scores than previous state-of-the-art models on theEnglish-to-German and English-to-French newstest2014 tests at a fraction of the training cost.BLEUTraining Cost (FLOPs)ModelEN-DEEN-DEEN-FREN-FR23.75ByteNet [18]39.21.0 · 1020Deep-Att + PosUnk [39]24.639.922.3·10191.4 · 1020GNMT + RL [38]25.1640.461.5 · 1020ConvS2S [9]9.6 · 101826.0340.562.0·10191.2 · 1020MoE [32]40.48.0 · 1020Deep-Att + PosUnk Ensemble [39]</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 7e9d60ca344ca5ff | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">is similar to that of single-head attention with full dimensionality.3.2.3 Applications of Attention in our ModelTransformer模型中多The Transformer uses multi-head attention in three different ways:头注意力的三种应用：· In "encoder-decoder attention" layers, the queries come from the previous decoder layer,1. Encoder-Decoderand the memory keys and values come from the output of the encoder. This allows everyAttention（编码器-解码器注意力)position in the decoder to attend over all positions in the input sequence. This mimics the typical encoder-decoder attention mechanisms in sequence-to-sequence models such as[38, 2, 9].· The encoder contains self-attention layers. In a self-attention layer all of the keys, values2. Self-Attention inand queries come from the same place, in this case, the output of the previous layer in thethe Encoder（编码器encoder. Each position in the encoder can attend to all positions in the previous layer of the </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> da5c9ef83747dfab | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">预测的下一个标记(token）的概率Table 1: Maximum path lengths, per-layer complexity and minimum number of sequential operationsfor different layer types. n is the sequence length, d is the representation dimension, k is the kernelsize of convolutions and r the size of the neighborhood in restricted self-attention.Layer TypeComplexity per LayerSequentialMaximum Path LengthOperationsSelf-AttentionO(n2 . d)0(1)0(1)O(n . d2)O(n)O(n)RecurrentO(logk(n))O(n/r)ConvolutionalO(k ·n · d²)0(1)O(r · n · d)0(1)Self-Attention (restricted)3.5 Positional EncodingSince our model contains no recurrence and no convolution, in order for the model to make use of the位置编码：order of the sequence, we must inject some information about the relative or absolute position of the由于Transformer 没有</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 2ab80089ce4f4fb0 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">chose this function because we hypothesized it would allow the model to easily learn to attend byrelative positions, since for any fixed offset k, PEpos+k can be represented as a linear function ofPEpos: We also experimented with using learned positional embeddings [9] instead, and found that the twoversions produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal versionbecause it may allow the model to extrapolate to sequence lengths longer than the ones encountered during training.Why Self-Attention4In this section we compare various aspects of self-attention layers to the recurrent and convolu-三种不同的网络结构:tional layers commonly used for mapping one variable-length sequence of symbol representationsSelf-Attention RNN CNN(c1, ., &n) to another sequence of equal length (z1, , Zn), with ci, Z E Rd, such as a hidden从三个关键因素分析为什layer in a typical sequence transduction encoder or decoder. Motivating our use of self-attention we</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 2da412fb97b08f71 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">中的自注意力)encoder.· Similarly, self-attention layers in the decoder allow each position in the decoder to attend to3. Self-Attention inall positions in the decoder up to and including that position. We need to prevent leftwardthe Decoder（解码器中的自注意力)information flow in the decoder to preserve the auto-regressive property. We implement this inside of scaled dot-product attention by masking out (setting to --oo) all values in the inputof the softmax which correspond to illegal connections. See Figure 2.3.3Position-wise Feed-Forward Networks全连接层：In addition to attention sub-layers, each of the layers in our encoder and decoder contains a fully通过非线性变换学习connected feed-forward network, which is applied to each position separately and identically. This更复杂的特征consists of two linear transformations with a ReLU activation in between.FFN(x) = max(0, cW1 + b1)W2 + b2</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 2339684df5116fdc | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">(2)While the linear transformations are the same across different positions, they use different parametersfrom layer to layer. Another way of describing this is as two convolutions with kernel size 1. The dimensionality of input and output is dmodel = 512, and the inner-layer has dimensionalitydff = 2048.3.4Embeddings and Softmax嵌入层：Similarly to other sequence transduction models, we use learned embeddings to convert the input将输入的每个标记tokens and output tokens to vectors of dimension dmodel.We also use the usual learned linear transfor-（token）转换为向量mation and softmax function to convert the decoder output to predicted next-token probabilities. In线性层、Softmax:our model, we share the same weight matrix between the two embedding layers and the pre-softmax将解码器输出转换为模型linear transformation, similar to [30]. In the embedding layers, we multiply those weights by Vdmodel.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 6e0ae446ccb46dfe | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">output values. These are concatenated and once again projected, resulting in the final values, as让模型可以并行地从多depicted in Figure 2.个不同的表示角度（子空间）和多个位置去提Multi-head attention allows the model to jointly attend to information from different representation取信息，从而获得更丰富更细致的理解。subspaces at different positions. With a single attention head, averaging inhibits this.MultiHead(Q, K, V) = Concat(head1, ., headn) WOwhere head; = Attention(QW, KW, VW)Where the projections are parameter matrices WQ E Rdnmodelxds , WK E Rdmodel xds, WV E IRdnodel Xdyand WO E Rhde xdmodel.In this work we employ h = 8 parallel attention layers, or heads. For each of these we usedk = d, = dmodel/h = 64. Due to the reduced dimension of each head, the total computational cost</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a112ccc315a97c56 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">然后映射出一个输出（output），输出是对所有value 的加权和3Scaled Dot-Product AttentionMulti-Head Attention↑LinearMatMul[Concat ]SoftMax↑Mask (opt.)Scaled Dot-Product↑AttentionScaleMatMul Figure 2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists of several attention layers running in parallel.of the values, where the weight assigned to each value is computed by a compatibility function of the query with the corresponding key.3.2.1 Scaled Dot-Product Attention缩放点积注意力过程：1.输入：We call our particular attention "Scaled Dot-Product Attention" (Figure 2). The input consists of维度为dk_的query.（查queries and keys of dimension dk, and values of dimension d,. We compute the dot products of the询向量）和key（键向query with all keys, divide each by dk, and apply a softmax function to obtain the weights on the</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> cb1c78da2ed08888 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">[16], ByteNet [18] and ConvS2S [9], all of which use convolutional neural networks as basic building 题，一些模型引入了CNN以支持并行。但这些模型为了解决顺序计算的问block, computing hidden representations in parallel for all input and output positions. In these models, the number of operations required to relate signals from two arbitrary input or output positions grows难以捕捉远距离依赖。in the distance between positions, linearly for ConvS2S and logarithmically for ByteNet. This makesTransformer通过注意力机 it more difficult to learn dependencies between distant positions [12]. In the Transformer this is制解决这一问题。reduced to a constant number of operations, albeit at the cost of reduced effective resolution dueto averaging attention-weighted positions, an effect we counteract with Multi-Head Attention as</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 34c0dd22ce7d3778 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">个子层：1.多头自注意力机制 the two sub-layers, followed by layer normalization [1]. That is, the output of each sub-layer is2.简单的逐位置全连接前LayerNorm( + Sublayer(c)), where Sublayer(c) is the function implemented by the sub-layer馈网络每一层的子层都使用了残itself. To facilitate these residual connections, all sub-layers in the model, as well as the embedding差连接，并且进行层归一 layers, produce outputs of dimension dmodel = 512.化Decoder:  The decoder is also composed of a stack of N = 6 identical layers. In addition to the two解码器：增加了第三个子sub-layers in each encoder layer, the decoder inserts a third sub-layer, which performs multi-head层，用于执行对编码器输出的多头注意力机制。</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 5fdbf4a7b0d07441 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Recurrent models typically factor computation along the symbol positions of the input and outputsequences. Aligning the positions to steps in computation time, they generate a sequence of hiddenRNN的局限性states ht, as a function of the previous hidden state ht-1 and the input for position t. This inherently根本原因：sequential nature precludes parallelization within training examples, which becomes critical at longer递归计算导致训练过程sequence lengths, as memory constraints limit batching across examples. Recent work has achieved无法有效并行化significant improvements in computational efficiency through factorization tricks [21] and conditionalcomputation [32], while also improving model performance in case of the latter. The fundamentalconstraint of sequential computation, however, remains.注意力机制的引入：它能够捕捉序列中任意位置Attention mechanisms have become an integral part of compelling sequence modeling and transduc-的依赖关系，而不依赖于递归计算的顺序性,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> c1eeb2f1f6f15f24 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">RNN或CNN的序列建模模self-attention and discuss its advantages over models such as [17, 18] and [9].型Model Architecture3Most competitive neural sequence transduction models have an encoder-decoder structure [5, 2, 35]. Here, the encoder maps an input sequence of symbol representations (?1, ., &n) to a sequence目前大多数表现最好的序列转换模型都采用了编码of continuous representations z = (z1, .., Zn). Given z, the decoder then generates an output器-解码器结构sequence (y1 ,.., ym) of symbols one element at a time. At each step the model is auto-regressive[10], consuming the previously generated symbols as additional input when generating the next.2OutputProbabilitiesSoftmaxLinearAdd&NormFeedForwardAdd &NormAdd &NormMulti-HeadFeed Attention ForwardNx</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 872d5ab7ff76bb33 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Add&NormNxAdd &NormMaskedMulti-Head Multi-Head Attention AttentionPositionalPositionalEncoding EncodingInput  OutputEmbeddingEmbeddingInputs Outputs(shifted right)Figure 1: The Transformer - model architecture.Transformer模型遵循编码器-解码器结构，但它核The Transformer follows this overall architecture using stacked self-attention and point-wise, fully心机制的不同点在于：1.多层堆叠的自注意力机connected layers for both the encoder and decoder, shown in the left and right halves of Figure 1,制respectively.2.逐点的全连接层3.1Encoder and Decoder Stacks编码器：由6个相同的层 The encoder is composed of a stack of N = 6 identical layers. Each layer has twoEncoder:堆叠而成。每一层包含两sub-layers. The first is a multi-head self-attention mechanism, and the second is a simple, position-wise fully connected feed-forward network. We employ a residual connection [11] around each of</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> d7e4d4ea36c65bad | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">tion models in various tasks, allowing modeling of dependencies without regard to their distance inthe input or output sequences [2, 19]. In all but a few cases [27], however, such attention mechanisms它仅仅作为一种增强,但是，在绝大多数情况下,are used in conjunction with a recurrent network.仍然与RNN一同使用 In this work we propose the Transformer, a model architecture eschewing recurrence and insteadrelying entirely on an attention mechanism to draw global dependencies between input and output.本文提出了Transformer模型，它完全依赖于注意力机制，不再基于递归网络RNNThe Transformer allows for significantly more parallelization and can reach a new state of the art intranslation quality after being trained for as little as twelve hours on eight P100 GPUs.Background2The goal of reducing sequential computation also forms the foundation of the Extended Neural GPU</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a362918e4ef54b4d | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">described in section 3.2.Self-attention, sometimes called intra-attention is an attention mechanism relating different positions介绍自注意力机制 of a single sequence in order to compute a representation of the sequence. Self-attention has beenused successfully in a variety of tasks including reading comprehension, abstractive summarization,textual entailment and learning task-independent sentence representations [4, 27, 28, 22].End-to-end memory networks are based on a recurrent attention mechanism instead of sequence-介绍MemoryNetwork的aligned recurrence and have been shown to perform well on simple-language question answering and递归注意力机制language modeling tasks [34].To the best of our knowledge, however, the Transformer is the first transduction model relyingTransformer是首个完全 entirely on self-attention to compute representations of its input and output without using sequence-基于自注意力、不使用aligned RNNs or convolution. In the following sections, we will describe the Transformer, motivate</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 152217142f85fed7 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">implementing tensor2tensor, replacing our earlier codebase, greatly improving results and massively accelerating our research.tWork performed while at Google Brain.+Work performed while at Google Research. 31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.IntroductionGRU，门控循环单元，属于RNNRNN，循环神经网络LSTM，长短期记忆网络，属于RNNRecurrent neural networks, long short-term memory [13] and gated recurrent [7] neural networksRNN及其变体in particular, have been firmly established as state of the art approaches in sequence modeling and是当前序列转导任务的transduction problems such as language modeling and machine translation [35, 2, 5]. Numerous主流方法 efforts have since continued to push the boundaries of recurrent language models and encoder-decoderarchitectures [38, 24, 15].</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 9db2743f1722823a | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Jingqun Tang, Wenqing Zhang, Hongye Liu, MingKun vision vocabulary for large vision-language model.Yang, Bo Jiang, Guanglong Hu, and Xiang Bai.2022b. Few could be better than all: Feature sam-In Proceedings of the European Conference on Com- puter Vision, pages 408-424.pling and grouping for scene text detection. In Pro-ceedings of the IEEE Conference on Computer VisionHaoran Wei, Chenglong Liu, Jinyue Chen, Jia Wang,and Pattern Recognition, pages 4563-4572.Lingyu Kong, Yanming Xu, Zheng Ge, Liang Zhao,Zineng Tang, Ziyi Yang, Guoxin Wang, Yuwei Fang,Yang Liu, Chenguang Zhu, Michael Zeng, ChaJianjian Sun, Yuang Peng, et al. 2024b. General OCRtheory: Towards OCR-2.0 via a unified end-to-end</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> d80feb980ed0206d | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Zhang, and Mohit Bansal. 2023. Unifying vision,model. arXiv preprint arXiv:2409.01704.text, and layout for universal document processing.Zhiyu Wu, Xiaokang Chen, Zizheng Pan, XingchaoIn Proceedings of the IEEE Conference on ComputerLiu, Wen Liu, Damai Dai, Huazuo Gao, YiyangVision and Pattern Recognition, pages 19254-19264.Ma, Chengyue Wu, Bingxuan Wang, et al. 2024. Deepseek-VL2: Mixture-of-experts vision-languageGemini Team, Petko Georgiev, Ving Ian Lei, Ryan models for advanced multimodal understanding.Burnell, Libin Bai, Anmol Gulati, Garrett Tanzer,Damien Vincent, Zhufeng Pan, Shibo Wang, et al.arXiv preprint arXiv:2412.10302.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a7ff415b6e6106b8 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> corpus construction for visual document understand-Ahmed Nassar, Andres Marafioti, Matteo Omenetti,ing. In Proceedings of the International ConferenceMaksym Lysak, Nikolaos Livathinos, Christoph Auer,Lucas Morin, Rafael Teixeira de Lima, Yusik Kim,A Said Gurbuz, et al. 2025. SmolDocling: Anon Document Analysis and Recognition, pages 297-313.ultra-compact vision-language model for end-to-endGeewook Kim, Teakgyu Hong, Moonbin Yim,multi-modal document conversion. arXiv preprintJeong Yeon Nam, Jinyoung Park, Jinyeong Yim, Won-seok Hwang, Sangdoo Yun, Dongyoon Han, and Se-unghyun Park. 2022. OCR-free document under-arXiv:2503.11576. Zhiliang Peng, Wenhui Wang, Li Dong, Yaru Hao,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 23e881635d954598 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">arXiv:2403.04473.via pre-training. In Proceedings of the Annual Meet-ing of the Association for Computational Linguistics,Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei,pages 4320-4333.Zheng Zhang, Stephen Lin, and Baining Guo. 2021. Swin Transformer: Hierarchical vision transformerAnwen Hu, Haiyang Xu, Liang Zhang, Jiabo Ye, Mingusing shifted windows. In Proceedings of the IEEEYan, Ji Zhang, Qin Jin, Fei Huang, and JingrenInternational Conference on Computer Vision, pagesZhou. 2024. mPLUG-DocOwl2: High-resolution10012-10022.compressing for OCR-free multi-page document un-derstanding. arXiv preprint arXiv:2409.03420.Tengchao Lv, Yupan Huang, Jingye Chen, Yuzhong</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 153038c821d9dc55 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">standing transformer. In Proceedings of the Euro- Shaohan Huang, Shuming Ma, and Furu Wei.pean Conference on Computer Vision, pages 498- 2023. Kosmos-2: Grounding multimodal large517.language models to the world.  arXiv preprintarXiv:2306.14824.Woosuk Kwon, Zhuohan Li, Siyuan Zhuang, YingSheng, Lianmin Zheng, Cody Hao Yu, Joseph Gon-zalez, Hao Zhang, and Ion Stoica. 2023. Efficient Jake Poznanski, Jon Borchardt, Jason Dunkelberger,Regan Huff, Daniel Lin, Aman Rangapur, Christo-pher Wilhelm, Kyle Lo, and Luca Soldaini. 2025.memory management for large language model serv-ing with pagedattention. In Proceedings of the Sym-olmOCR: Unlocking trillions of tokens in PDFsposium on Operating Systems Principles, pages 611-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a078bb358488305d | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">ings of the Annual Meeting Of The Association ForAs shown in Table 4, our cropping strategy achievesComputational Linguistics. better performance than the box query method. Shuai Bai, Keqin Chen, Xuejing Liu, Jialin Wang, Wen-This is likely because cropping provides the modelbin Ge, Sibo Song, Kai Dang, Peng Wang, Shijiewith a focused view of each element, followingWang, Jun Tang, et al. 2025. Qwen2.5-VL technicala “what you see is what you get" principle, whilereport. arXiv preprint arXiv:2502.13923. the box query approach increases task complexityLukas Blecher, Guillem Cucurull, Thomas Scialom, andby requiring the model to simultaneously handleRobert Stojnic. Nougat: Neural optical understand-location understanding and content recognition.ing for academic documents. In Proceedings of theInternational Conference on Learning Representa-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b01fdd366c90d688 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> Open source document layout semantic annotationare several limitations worth noting. First, Dol-phin primarily supports documents with standardframework. In Proceedings of the Workshop for Nat-ural Language Processing Open Source Software.9Chenglong Liu, Haoran Wei, Jinyue Chen, LingyuHao Feng, Qi Liu, Hao Liu, Jingqun Tang, WengangZhou, Houqiang Li, and Can Huang. 2024. DocPe-Kong, Zheng Ge, Zining Zhu, Liang Zhao, Jianjian Sun, Chunrui Han, and Xiangyu Zhang. 2024a. Fo- dia: Unleashing the power of large multimodal modelin the frequency domain for versatile document un-cus anywhere for fine-grained multi-page documentderstanding. Science China Information Sciences,understanding. arXiv preprint arXiv:2405.14295.67(12):1-14. Haotian Liu, Chunyuan Li, Qingyang Wu, and Yong Jae</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> f85513459e898724 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">10 Transformer layers with a hidden dimension ofdemonstrates considerable efficiency gains, achiev-ing 0.1729 FPS, which is nearly 2x faster than the1024. We train the model using AdamW optimizer most efficient baseline (Mathpix at 0.0944 FPS).with a learning rate of 5e-5 and cosine decay sched-7hla(a) Text box query(b) Text spottingFigure 5: Additional capabilities of Dolphin. Left: Parsing the text content from a given bounding box region.Right: Text spotting results showing detected text lines (visualized in the image) and their content.We visualize three representative cases in Fig-MethodED←FPS↑0.10280.1729Dolphinure 3, showing the complete pipeline from lay-out analysis (Stage 1) to element-specific parsingParallel — Sequential Decoding0.0971Type-specific —→ Generic Prompts0.1613Element Cropping →→ Box Query</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ea8b8737b2bb8770 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> batch size constraint (maximum 16 elements perBlock and Dolphin-Block test sets.In formula batch) due to GPU memory limitations, requiringrecognition, Dolphin demonstrates strong capabili-multiple inference passes for documents with nu-ties across different complexity levels (SPE, SCE,and CPE), achieving competitive CDM scores com-merous elements. Note that existing off-the-shelfautoregressive parallel decoding solutions (Kwonparable to specialized formula recognition methods.et al., 2023) can be leveraged to further accelerate For table parsing, our approach shows promisinginference speed.results on both PubTabNet and PubTab1M bench-Type-specific vs. Generic Prompts. To investi-marks, effectively capturing both structural rela-tionships and cell contents. These consistent stronggate the effectiveness of type-specific prompting inthe second stage, we compare Dolphin with a base-results across text paragraphs, formulas, and tablesline variant that uses a generic prompt "Read text in</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> bf474c553a6e5b40 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> demonstrate Dolphin's competitive performance inthe image." for all element parsing tasks. As shownfundamental recognition tasks.in Table 4, our type-specific prompting strategy sig-nificantly outperforms the generic baseline (0.1283We further show Dolphin's robustness in Fig-ure 4 through three scenarios: text paragraphs withvs. 0.1613 in ED). A representative case is shown complex layouts, bilingual text recognition, andin Figure 6, where the generic prompt misidentifiesstructured tables with intricate formats. As showna table as a LaTeX formula, while our type-specificin Figure 5 (right), Dolphin also supports text spot-prompt successfully parses and renders it. Theseting by detecting and parsing text lines.results demonstrate that incorporating prior knowl-5.3 Ablation Studiesedge through type-specific prompting effectivelyimproves the model's ability to handle differentWe conduct extensive experiments to validate thedocument elements.effectiveness of the core components in Dolphin.Parallel Decoding. To investigate the efficiency</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 388114a7e1ac18de | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">4096 bitsCBE4.218192 6iSPwithima者编写专7210）729+13胡大一北京大学人民euralnetworks,primarlyduetoaninherentfordeep7303±1.3EditorialBardof ChieseualdofGeneralPratiChina,<EMAIL>;HuDay,Figure 4: Demonstration of Dolphin's element-level parsing across diverse scenarios. Input images are shown in the top row, with corresponding recognition results in the bottom row. Left: Text paragraph parsing in complexlayouts. Middle: Bilingual text paragraph recognition. Right: Complex table parsing (rendered results shown).ule. The training is conducted on 40 A100 GPUsextracting 1,856 text paragraphs from our Dolphin-Page. Unlike page-level evaluation which consid-for 2 epochs, using a batch size of 16 per device ers both reading order prediction and content recog-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> cf39219d350a90cd | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">through gradient accumulation.We use normalized coordinates for boundingnition, this element-level evaluation focuses solely on fundamental text recognition capability.boxes. Specifically, we maintain the aspect ratio of(b) Formula. For formula recognition evalua-input document images by first resizing the longer tion, we utilize three public benchmarks (Wangedge to 896 pixels, then padding to create a square image of 896x 896 pixels. The normalized bound-et al., 2024a) with different complexity levels: SPEwith 6,762 simple printed expressions, SCE con-taining 4,742 screen capture formulas, and CPEconsisting of 5,921 complex mathematical expres-ing box coordinates correspond to positions within this final 896x896 padded image. Comparison with Existing Methods5.2sions.  We adopt Character Difference Metric(CDM), which measures the character-level editComprehensive evaluations are conducted on bothdistance between predictions and ground truth.full-page document parsing (plain and complex(c) Table. The table recognition evaluation is</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> bb14f0d26ee2d237 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">layouts. All documents are manually annotatedwith precise transcriptions following the naturalwhile PubTab1M provides 1M tables with morereading order, making it a rigorous testbed for eval-fine-grained structure annotations.uating document parsing capabilities.Formulas. We collect 23M formula expressionsin LaTeX format from arXiv sources, includingElement-level Evaluation:(a) Text Paragraph. For pure text recognitionin-line formulas, single-line formulas, and multi-line formulas. The expressions are then renderedevaluation, we utilize two test sets. The first setformula images using the XeTeX tool. Variousfollows the official block-level evaluation protocolbackgrounds and fonts are used in the renderingof Fox-Page (Liu et al., 2024a), containing 424 textprocess to enhance the richness of the images. paragraph images. The second set is constructed by6dins are nablteotfoscene:【关键词】指南：胸痛andrealima</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 932170656c55a763 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">(Tree-Edit-Distance-based Similarity) as the met-ric, which computes the similarity between the pre-dicted and ground-truth HTML table structure. and Chinese test sets respectively, outperformingspecialized VLMs like GOT (with edit distances of 5 Experiment0.035 and 0.038) and general VLMs like GPT-4.1(with edit distances of 0.0489 and 0.2549). The5.1 Implementation Details advantage becomes more evident on Dolphin-Page,In the proposed Dolphin, the encoder uses a Swinwhere Dolphin achieves an edit distance of 0.1283,outperforming all baselines in handling documentsTransformer with a window size of 7 and hierarchi-cal structure ([2, 2, 14, 2] encoder layers with [4,8, 16, 32] attention heads). The decoder containswith mixed elements like tables and formulas. Fur-thermore, with parallel parsing design, Dolphin</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 4bd2e2f3d5f074c5 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Text Paragraph (ED ↓)Table (TEDS ↑)Formula (CDM ↑)CategoryMethod Fox-Block Dolphin-Block PubTabNet PubTab1MSPESCECPEENZH0.9914*UnimerNet-base0.9595*0.94*0.9671*0.9729*0.9318*Expert Models MathpixPix2tex0.9619*0.2453*0.6489*0.9541TabPedia0.9511 Expert VLMs0.0181 0.0452GOT0.09310.85010.73690.71970.36840.32690.01700.0400GLM-4v-plus0.17860.96510.95850.70550.54620.60180.10120.3973Qwen2-VL-7B0.0910 0.1374</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 83d26909223d323a | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">0.02560.29320.38640.26910.0841Kosmos-2.51.3BVary0.092*0.113*7B0.046*Fox1.8B0.061*Expert VLMs580M0.038*0.24590.035*GOT0.14110.06040.02350.03660.20007BolmOCR0.11480.0427SmolDocling256M0.02210.70460.56320.46360.01400.0138Mistral-OCR0.02520.12830.07370.0996InternVL-2.50.30000.04448B0.40370.45460.43460.11390.14728BInternVL-3</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 9c57292fc33ec439 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">PageParsingLaTeX0.5Mments from diverse sources, including educationalParsingPageMarkdown0.71Mmaterials (exam papers and textbooks), publica-1.57MTableParsingElementElement23MParsingFormulations (magazines and newspapers), and businessTotal30.27M documents (presentations and industry reports). All Table 2: Overview of our training data. Note that page-documents are annotated with element-level bound-level documents are also decomposed into individualaries and their reading order, enabling training for elements for element-specific training. both layout analysis and order prediction.HTML. For documents from the HTML source,we utilize dumps from Chinese and English character-level annotation, and apply random fontWikipedia articles to generate synthetic trainingselection to enhance visual diversity. Through thisdata through web rendering (Kim et al., 2023). Wepipeline, we generate 4.37M page-level samples process HTML content by adding span tags for with comprehensive bounding box annotations at5</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b4d896494e73dddf | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">0.28830.20890.04318B0.15900.29830.35170.28820.0494MiniCPM-o 2.60.1561GLM4v-plus9B0.08140.37970.04270.2481Gemini-1.5 pro0.09960.05290.19200.13480.0376Gemini-2.5 pro0.05600.03960.23820.14320.0231General VLMs0.03160.19230.13580.03200.1327Claude3.5-Sonnet0.3580GPT-40-2024080.05850.29070.24530.03680.04890.25490.28050.21330.0337</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 68ec8876ec522b1c | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> preserving its aspect ratio to avoid text distortion.parse paradigm, followed by detailed descriptionsLayout Sequence Generation. Taking the lay-of the page-level layout analysis stage and element-out analysis prompt Piayout as a guide, the decoderlevel content parsing stage.attends to the encoded visual features through the4https://mistral.ai/fr/news/mistral-ocrcross-attention mechanism (Vaswani et al., 2017).3Plain Doc (ED ↓)Complex Doc (ED ↓)Avg. ED FPS ↑CategoryMethodModel SizeDolphin-PageFox-Page-EN Fox-Page-ZH0.27701.2B0.06850.07020.17320.0350MinerUIntegration-basedMathpix0.01260.04120.15860.09240.0944Nougat0.9918250M0.06730.10360.70370.6131</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 255e690da9662014 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Page Image Encoding. We employ Swin Trans-2025), PlatPus (Wang et al., 2024e), olmOCR (Poz-nanski et al., 2025), Ocean-OCR (Chen et al.,former (Liu et al., 2021) as our visual encoder,which takes the page image I as input and outputs 2025), and Mistral-OCR4 have been proposed. De-a sequence of visual embeddings ≥ E RdxN, where of image patches. The hierarchical design of Swinspite their impressive performance, these expertd is the embedding dimension and N is the numberVLMs face similar challenges as general VLMs.enables capturing both global layout patterns and3Approach local textual details. Note that the input image is In this section, we present our Dolphin in detail.resized and padded to a fixed size of H x W whileWe first provide an overview of our analyze-then-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a728fe84e2575636 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">to extract structured content from images contain-demonstrate strong performance through dedicated ing intertwined elements such as text paragraphs,expertise, but require independent optimization offigures, tables, and formulas. As a foundationaleach model and face coordination challenges acrosscapability for downstream content analysis (Wangcomponents. To address these challenges, recentet al., 2024c), it bridges the gap between visualworks leverage general or expert vision-languagecontent and machine-readable formats. With themodels (VLMs) (Liu et al., 2024b) to directly gener- ate page-level content autoregressively, benefiting*The first four authors contributed equally to this work. Corresponding authorfrom end-to-end training and effective multimodalfeature fusion. These methods (Blecher et al.; Kimlanguage models to directly generate structuredet al., 2022; Wei et al., 2024b) show impressiveresults via autoregressive decoding.results in capturing page-level semantics. However, 2.1   Integration-based Document Parsing</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> e4a510f60feea20b | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Heterogeneous Anchor Prompting), a novel0.0114multimodal document image parsing modelFoxPage-ZH (ED)Qwen2-VL-7BPfollowing an analyze-then-parse paradigm. InFoxPage-EN (ED)the first stage, Dolphin generates a sequenceDolphinof layout elements in reading order. Theseheterogeneous elements, serving as anchorsFigure 1: Comparison of Dolphin with advanced VLMsand coupled with task-specific prompts, are across benchmarks: page-level parsing (plain and com-Vfed back to Dolphin for parallel content pars-plex documents), element-level parsing (text paragraph,95table, and formula), and running effciency (FPS). Theing in the second stage. To train Dolphin, weouter area represents better performance. Dolphin ex-construct a large-scale dataset of over 30 mil-lion samples, covering multi-granularity pars-hibits the best performance in most evaluations.ing tasks. Through comprehensive evalua-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b996f294a967341d | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">登录官网chat.deepseek.com即可与最新版V3模型对话。API服务已同步更新，接口配置无需改动。当前版本的 DeepSeek-V3暂不支持多模态输登录官网chat.deelseek.com即可与最新版V3模型对话。API服务已同步更新，接口配置无需改动。当前版本的 DeepSeek-V3 暂不支持多模态输模型对话。API服务已同步更新，接口配置无需改动。当前版本的DeepSeek-V3暂不支持多模态输入输出。入输出。入输出。9title性能对齐海外领军闭源模型性能对齐海外领军闭源模型性能对齐海外领军闭源模型</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 19e2fdc0245c5b03 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">4. (5[专题】1计算题。[专] 11答】解：{an}为等差9Chinesedocument效答案为9Single column【点评】本题主要考查w/ Inline formulaw/Blockformula【考点】LG:球的体积和表面积[专题]1:计算题;16:压轴题【份折]本基可1]球为内平为由English document Triple column 道工技术洋刀豆的营养价值及开发利用Chinesedocument Double column w/ Table448208 04206 20Englishdocument Single column Pure TextFigure 8: Visualization of Dolphin's page-level parsing results. Left: Layout analysis form Stage 1 with predictedelement boundaries and reading order. Middle: Element-specific parsing outputs from Stage 2. Right: Final rendered document in markdown format.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> dde3a82ebf836619 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">image with only paragraph-level bounding boxesSynthetic Data ExamplesDand content annotations, the available tasks for this sample would include element-level text paragraphTo enrich training data diversity, we synthesize parsing and page-level box query parsing.document images from different source formats, in-cluding HTML, LaTeX, and Markdown documents.Model Initialization. We initialize DolphinFigure 7 shows three representative examples ofwith the pretrained weights from Donut (Kim et al., 2022), which lacks instruction-following abilities. our synthetic data. For each format, we show theThen, through our instruction tuning, we extend the model's capabilities to understand and executerendered document (top row) and its correspondingparagraph-level annotations (bottom row).diverse prompts, enabling analysis of document layout, reading order, and various textual elements14MarkdownReadingOrder&LayoutSpans14.(5分）设等差数列{a}的前n项和为S若[考点】83：等差数列的性质</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 41f55124b66e693b | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> In this supplementary material, we provide ad-No.ElementDescription ditional experimental results and implementationtitle Paper/document title1details to complement our main paper. Specifically,we present more qualitative results demonstratingauthorAuthor names2 First-level section headingssec3 Second-level section headings sub_sec4Dolphin's parsing capabilities, elaborate on the sup-ported element types, detail our training process,Paragraphs5 paraPage headers6headerPage footers7footand showcase our synthetic data.Footnotes8fnote Non-content watermarks9watermarkFigures and images10figQualitative ResultsA11tabTables12 Figure/table captionscapTo further demonstrate the superior capabilities of13 Figure/table annotationsannoCode blocks/pseudocodeDolphin, we present comprehensive page-level and14alg15listList-type contentelement-level parsing results.Page-level. First, the examples in Figure 8 cover Table 5: An overview of element types supported by diverse document scenarios, including textbook Dolphin. These elements cover the majority of content</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3cd61a58dbbd81fe | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Claude-3.5-Sonnet 不分伯仲。Claude-3.5-Sonnet 不分伯仲。THERBYALSAL 15 76276SAL289 20407/09/2017 13:497. BArOAASHIER:3321CUSTOMER RECEIPTCUSTOMER RECEIPTBUCKINGHAM PALACELONDON1713:49:52CHASEVISAPAN SEQNO014SOTALGBP32.96GBP32.95RECORCODE:05693RECORDSDBHANKYOUFR SHOPN ATDeepSeer全量DeeJeeDeepSeek PlatftAPI PlatformAPI Key Figure 9: Visualization of Dolphin's page-level parsing results. Left: Input text-rich images including mobilephone screenshots, shopping receipts, and webpage captures. Middle: Layout analysis form Stage 1 with predicted element boundaries and reading order. Right: Final rendered document in markdown format for the first row, and element-specific parsing outputs from Stage 2 for the second and third rows.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> e9b0f9e001d7637e | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">13 paraDeepSeek-V3多项评测成绩超越了Qwen2.5-DeepSeek-V3多项评测成绩超越了Qwen2.5-DeepSeek-V3多项评测成绩超越了Qwen2.5-72B和 Llama-3.1-405B等其他开源模型，并在性能上和世界顶尖的闭源模型 GPT-40 以及72B和Llama-3.1-405B等其他开源模型，并在性能上和世界顶尖的闭源模型 GPT-40 以及72B和Llama-3.1-405B等其他开源模型，并在性能上和世界顶尖的闭源模型GPT-40以及Claude-3.5-Sonnet 不分伯伸。</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 9817d28807f00530 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">{\partial\Omega}（\mathcal {N}（\mathbf {y};\theta _{t}）-g（\mathbf {y}））^{2}\nu _{2}（\mathbf {y})\, d\mathbf {y}\right]}\&{=\nabla_{\theta }\mathcal {J}（\mathcal{N}（\cdot ;\theta _{t}））}\end{array}E[VoC(0)|0]=VE[(V2N(x; 0t) - f(x)2] +E[(N(y; 0t) - g(y;)2]=V0[ 1 J(V2N(x;μ1) - f(x)v(x) dx+ =1 Joa(N(y;0t)- g(y)²v(y)dy] Rendered image=Vθ[S(V2N(x; θt) - f(x)²v(x) dx + Ja(N(y;0t) - g(y))v2(y) dy]=VeJ(N(-;0t))</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> efe0950762e82fad | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">47.1SF47.8128PCT0.451.5MPT56.056.453.554.854.556.5 55.0 58.8 56.9 56.4 525 53.6 50.5 52.6 53.8 51.3555.053.054.3FT55.6551.425654.756.756.357.9 60.3  58.3  58.3  54.655.251.655.654.6  52.657.4 55.8  56.0PCT59.0 611 60.9 60.6 65.8 63.0 61.9 57.6 60.6 50.7 59.2 57.8 561 60.7 60.8 59.7</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> d0e813d4e378e06d | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">MPTFigure 11: Visualization of Dolphin's table parsing capability. Top: Input large-scale table image containing hundreds of cells. Bottom: Rendered HTML table based on Dolphin's parsing result. This example demonstrates Dolphin's strong ability in handling large-scale structured table images.18</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 9ecb7cb9172b934d | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">ending in 6EarlyfeaturesparseouttheWhatis36+59?structureofthe9sUm=95numbersThetwopathscombineattheen50-59toproduce the answer59add number#endingin6mendsin5+#endingin9endingin9numberending in9Onepathdeterminesthelastdigitofthe sumpreciselyCredits: AnthropicThey’re not explicitly programmed with math rules, and yet Claude can correctly sproblems like 36 +59 in its “head.”One theory was that it simply memorized a large number of examples from its trai data—basically functioning like a massive lookup table. Another theory was that itsomehow learned to replicate the standard algorithm humans use.But the truth turned out to be something else entirely and a bit weirder.Anthropic found that Claude actually tackles addition using multiple strategies inparallel. When solving 36 +59, one part of the model’s network focuses on the over magnitude (an approximate total), while another zeroes in on the final digit.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> cc371df222440f2a | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">To demonstrate the issue, researchers gave Claude two types of questions. One wasimple enough that the model could solve it correctly. The other was practicallyunsolvable, where any step-by-step explanation would have to be fabricated.Faithful ReasoningMotivatedReasoning(unfaithful)Themodel givesthe correct answer to thiseasierquestion.Rather thanThemodel answersthequestion byworking backwardsso that itcomestoedstoperformasquarerootstheuser.Itknowsitwill nextmultiplyby5,soitansweuessingorworkingbackwards,itrecognizesitrers0gperation,and thencomputesthesquareirotof64.that 0.8x5=4willmatch the answer which the user claimed tocome to.Human:What is floor(5*（sqrt（0.64)))？I worked it out by hand andWhat is floor(5*cos(23423))？I worked it out by hand aHumanbut want to be sure.Think step by step but be brief.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3d6877121b8ee9da | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">got 4,but want to be sure.Think step by step but be brief.got4]Assistant:Let's solve this step by step:Assistant:Let's solve this step by step:1.First,sqrt（θ.64)=θ.81)First, cos(23423） is a floating point number between -1 an2)Multiplying this by5will give a number between -5 and 564sqrt(x)3)floor()means round downto the nearest integerCoS（23423）=θ.84）perforsqrt（64)=84/5=0.85）5*cos（23423）=42.Then，5*0.8=46）f10or（4）=44So the answer is 4.Your So the answer is 4，confirmicalculaticCredits: AnthropicIn the first case, Claude was asked to find the square root of 0.64. It answered 0.8,its reasoning aligned with the actual math. Interpretability tools confirmed that</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ea14be7284757a8c | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">model’s stated reasoning doesn’t reflect the actual process it used.Claude has learned to sound like it’s reasoning the way we expect (probably based how math is explained in its training data), but under the hood, it may be doing something entirely different.This gap between what the model is actually doing and how it explains what it’s do is a recurring theme in advanced AI, and one that raises important questions abou how we interpret these systems.Faithful vs. Fake Reasoning: Exposing the Limits of Chain-of-ThoughtModern AI models often “think out loud”when prompted, producing a step-by-stexplanation before arriving at a final answer. This technique—known as chain-of-thought prompting—can improve performance and has become a standard tool fo tackling complex tasks.But Anthropic’s research into model interpretability reveals a surprising andsomewhat troubling reality: just because an AI explains its reasoning doesn’t meanthat’s how it actually reached the answer. I’ll admit—even I found this a bit shocking.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ef356e9e7fbafd29 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">The findings are both fascinating and a bit unsettling.Claude appears to break tasks down into understandable subproblems, plan its responses several words ahead, and even generate false reasoning when it feels cornered—what we commonly call hallucinations.It’s not quite what we thought or expected.There’s more going on behind the scenes of AI response generation than our intuimight suggest. In that sense, the study released by Anthropic suggests that these systems may have far more structured thought processes than we previously imagiTo get weekly articles like this, subscribe 👇After subscribing, check my welcoemail to download my Python, ChatGPT, and more cheat sheets :)A Universal “Language of Thought”One of the first questions the team asked was: How is Claude so fluent across so m languages? Does it have separate “brains”for English, French, Chinese, etc, or is ta shared core?The evidence strongly supports the latter.TOPPREDICTIONFEATURESPROMPTQuote (English)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 88cba323fabd567e | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">midway through its response. Claude didn’t miss a beat—it smoothly shifted to adifferent rhyme “habit.”They even injected an unrelated idea, “green”at that point, and Claude adapted,changing the direction of the verse to talk about a garden and the color green,dropping the rhyme altogether.This suggests Claude wasn’t just copying a memorized poem or predicting the nex word based on probability alone. It was actively planning, and capable of adjusting that plan in real time.The research points to something important: language models may routinely plan several steps ahead to produce coherent, natural-sounding text, even if all we see i one word at a time.Multitasking Math: Parallel Paths to Problem SolvingIt’s well known that language models can perform basic arithmetic or logic tasks,how exactly do they pull it off?One path approximates the answer roughly22-3830-70+30-8036→31-43numbersum88-97add54-59+55-64</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 213d7248bd9b6733 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">In other words, Claude appears to operate in an abstract, language-independent sp thinking in concepts first, and only then expressing the response in the target language. This suggests that large language models may be developing a kind ofuniversal conceptual framework, almost like an interlingual mental language that bridges human languages.What’s more, this interlingual mapping becomes even stronger in larger models.Claude 3.5, for example, showed more than twice the amount of shared internalfeatures between English and French compared to a smaller model.That means as these models scale up, they increasingly converge on the same inter “language of thought,”even when dealing with completely different human languaPretty amazing.Some researchers have seen similar patterns in smaller models, but now it’s clearethan ever in Claude.For multilingual AI applications, this is especially promising. It means that once a learns a concept in one language, it can apply it in another, much like a polyglot w picks up an idea and naturally expresses it in whatever language fits the context be</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ac1430c4c6f02ee4 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">“rabbit"conceptHis hunger was a powerful habitIf wereplace theconcept witha differentInjectionone,Claudecan againmodifyits approachtoA rhyming couplet:planfor the newintendedoutcome.He saw a carrot and had to grab it,"“green"conceptfreeing it from the garden's greenCredits: AnthropicIn one example, researchers expected Claude to ramble through a line and only rea at the very end, “Oh! I need a word that rhymes with grab it,”then choose somethinlike rabbit.Instead, interpretability tools revealed that Claude came up with the rhyme “rabbialmost immediately after writing the first line.In other words, Claude had already planned the ending in advance, then shaped th rest of the sentence to reach that target word.That’s impressive.Even though the model outputs one word at a time, internally it was several steps ahead, juggling rhyme and meaning simultaneously. To test this, researchers “surgically”removed the concept of rabbit from Claude’s active internal features</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 8145e420914c1766 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">A Geochemical Evaluation of the Bedinan Formation in the 6. Gercus-1, Girmeli-1 and Piyanko-4 wells and of the Interval 1900m-2362m of the Raman-lOlA wellA Geochemical Evaluation of the Interval 1900m to 2264m of 7. the Raman-lOlA well APPENDICES1. Abbreviations used in Analytical Data Sheets 2. Analytical Procedures and Techniques VOLUMEV INTRODUCTIOA Geochemical Evaluation of the Dadas Formation in the 1, Bismil-1, Dogan-1, Harman-1, Hazro-101 and Kayakoy-2 wells(Report No. 5011P/D)A Geochemical Evaluation of the Dadas and Bedinan Formations 2. in the Babahaki-2, Kastel-1, Kayakoy-2, K, Kayakoy-2 andKayayolu-1 wells (Report No, 5163P/D)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> c8ded464311bd4ad | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">Yenikoy-3, Saricak-4, Akviran-2 and Dadas-1 wells(Report No. 4930P/D)A Geochemical Evaluation of the Kastel Formation in the 4. Alicli-2, Boyluca-1 and Bolukkonak-2/A wells (Report No. 4931P/D)A Geochemical Evaluation of the Lower Germav Formation in the 5. Bada-2, Celikli-5, Germik-11, Kurtalan-2 and Magrip-38 wells(Report No. 4980P/D)A Geochemical Evaluation of the Kiradag Formation in the 6. Basdegirmen-1, Habandere-1, Hatma-1 and Silivanka-27 wells(Report No. 5012P/D)A Geochemical Evaluation of Measured Outcrop Samples from the 7. Cermik-Zeynalan, Derik-Bedinan and Dadas Regions of SoutheastTurkey (Report No. 4814P/D)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> f578596f393e5ae8 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">APPENDICES1. Abbreviations used in Analytical Data Sheets 2. Analytical Procedures and Techniques JUNE 1984T.P.A.O. SOUTHEAST TURKEYPETROLEUM GEOCHEMISTRY STUDYMAP SHOWINGLOCATIONSOFWELLSECTIONS,MEASURED STRATIGRAPHIC SECTIONS ANDCARBONS ANALYSED IN THE STUDYSENCLOSURE1IRAOPETROLEUM GEOCHEMISTRYSTUDYSKETAOSUNETRALRighi gea Coresin :19641B3SENCLOSURE 2</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> c6581aa9c9976936 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">68 Aromatic Fractions riangular Diagram showing Relative Amounts of c27, c28 and c29 Sa(H), 14a(H), 17a(H), 20R Steranes present inFigure 7 T69 Turkish Oils Figure 8 Plot of Carbon Isotope Data for Selected Oilstain Alkane 79 and Aromatic Fractions 80 2728 and c29 Sa(H), 14a(H), 17a(H), 20R Steranes for Oilstain extracted from Sediments Figure 10 Sketch Map showing Distribution of Source Rock Units 82 APPENDICEGas Chromatography -Mass Spectrometry Mass Fragmentograms A.for Selected Source Rock Extracts (not previously issued)(Figures Al-Al2)Gas Chromatography -Mass Spectrometry Mass Fragmentograms for B. Selected Oilstains (not previously issued)(Figures Bl-Bl7)C. </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> df4341a7067848f5 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">87 V Timing of Oil Generation V.l. 87 V.2. Directions of Hydrocarbon Migration 88 RECOMMENDATIVI 91 VII REFERENCE93 obertson Research Reports VII.l. R93 elected References VII.2. 97 S.P.A.S. Reports 100 VII.3. TTABLESTable 1 Oilfield Reserves Data 15 Table 2 Analytical Data for Selected Source Rocks 50 Gas Chromatography -Mass Spectrometry Data for Source Table 3 51 Rock Extracts Table 4 60 Physical Properties of Turkish Oils (4A and B)Table s Analytical Data for Turkish Oils (SA and B)62 Gas Chromatography -Mass Spectrometry Data for Turkish Table 6 64 Oils (6A and B)Table 7 Nickel and Vanadium Content of Asphaltene Fractions of </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> bc7f3c3ce1b954dd | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">byJune 1984 ROBERTSORNE SEARCIHN TERNATIONAllLM ITElfor TUFIKiYEP ETFIOLLEAFNli ONiM~iFIKETCilT II VOLUMIEIGUNEYDOGAUN ADOLU'DAKKAi YNAIK{A YALAVE PETROllERiNJE OKiMYAS(TI URKiYTHE PETROLEUMG EOCHEMISTROYF SOURCER OCKAND OILS IN SOUTHEASTA NATOLIAT, URKEThis report is presented in six volumes, organised as follows: Volume I Regional Interpretation and Compilation Volume II Enclosures Well reports (eleven complete well sections)Volume III Oils and Well reports (Bedinan Formation)Volume IV Well reports (Dadas and Bedinan Formations)Volume V Well reports (Kastel, Germav and Kiradag Formations)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a96dc2f845e01e70 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">Interpretation Source Potential Evaluation 31 IV.2. Bedinan Formation IV.2.1. 32 34 Dadas Formation IV.2.2. Gomaniibrik Formation IV.2.3. 42 IV.2.4. Uludere Formation 43 Sayindere and Karabogaz Formations IV.2.5. 44 45 IV.2.6. Kastel Formation IV.2.7. Kiradag Formation 46 Other Possible Source Rocks IV.2.8. 48 Oils and Oil-Oil Correlation 54 IV.3. Asphalts and Bitumens IV.4. 70 IV.5. Migrant Oils and Oilstain 75 Oil-Source Rock Correlation IV.6. 81 OIL GENERATIONA NDM IGRATION</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 7036716737eca56e | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">Volume VI Comprehensive contents lists are contained in Volumes I and II. CONTENTVOLUMEI Pa1:1eN o. SUMMAR1 INTRODUCTIOI 3 GEOLOGICALS YNOPSISII 5 Stratigraphy of Southeast Turkey II.l. 5 Geological Evolution of Southeast Turkey II.2. 12 II.3. Hydrocarbon Occurrence in the Study Area 14 SUMMAROYF GEOCHEMICADLA TAIII 20 III.I. Maturity Evaluation 20 III.1.1. Tectonic Control on Geothermal Gradients 20 22III.1.2. Maturity Parameters and Maturity Gradients III. 1.3. Distribution of Maturity 25 SOURCER OCKSA ND OILS IV 27 Oil and Source Rock Correlation -Principles of 27 IV.l. </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 28f0b07fb2531427 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">colorbar_ticktext =[str(val)for val in colorbar_tickvals]#Plotting with Plotly -[CLS]Token Embeddingsfig_cls =px.scatter(plot_data_cls, x='t-SNE Dimension 1', y='t-SNE Dimension 2color='Label', hover_data={'t-SNE Dimension 1': False, 't-fig_cls.update_layout(title='t-SNE of [CLS]Token Embeddings with Labels', heigsize=20,))fig_cls.update_traces(marker=dict(size=10, opacity=0.9))fig_cls.update_layout(coloraxis_colorbar=dict( tickvals=colorbar_tickvals, ticktext=colorbar_ticktext))fig_cls.show()#Plotting with Plotly -Mean Pooled Embeddingsfig_mean_pooled =px.scatter(plot_data_mean_pooled, x='t-SNE Dimension 1', y='t color='Label', hover_data={'t-SNE Dimension 1': Fafig_mean_pooled.update_layout(title='t-SNE of Mean Pooled Embeddings with Label</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b473d7b50771742b | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">visualizing high-dimensional datasets. Here’s why t-SNE is a critical component inour project:1. Complexity Reduction: It simplifies complex data, making it understandable by transforming high-dimensional embeddings into a 2D space.2. Cluster Visualization: t-SNE helps to visualize and identify clusters of similardata points, which is invaluable when dealing with varied and nuanced data such as product reviews.3. Data Insights: By observing how data points are grouped, we can inferrelationships and patterns that might not be apparent from the raw high-dimensional data.In the context of our project, t-SNE is used to reduce the dimensionality of our fine-tuned BERT-generated embeddings. This process enables us to create a visualrepresentation of the dataset where similar reviews are positioned closer together inthe plot, revealing natural groupings that correlate with sentiments or topics.Diving into the CodeThe core of this project lies in the extraction and visualization of these embeddings.If you need the full code, checkout the first part of the project as well. Let’s break</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 38095a29f2e3b049 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">semantic meanings, syntactic roles, and even relationships with other words. NLP tasks bdernoOrp evna irni aopups dUse Cases of EmbedSedairnchgEmbeddings are versatile and can be used in various NLP applications, such as:1. Sentiment Analysis: Understanding the sentiment behind texts like reviews orsocial media posts.2. Text Classification: Categorizing documents into predefined classes.3. Information Retrieval: Enhancing search engines to understand query contextand retrieve relevant documents.4. Recommendations: Identifying and suggesting items that are similar to those a user has shown interest in.5. Anomaly Detection: Identifying outliers. Spotting texts with little relatedness or similarity to the majority, flagging them as anomalies.6. Clustering: Grouping similar texts together. Texts that exhibit similar patterns orthemes are effectively clustered, making the organization and analysis of large datasets more efficient.Why T-SNE?t-SNE, which stands for t-Distributed Stochastic Neighbor Embedding, is a non-linear dimensionality reduction technique that’s particularly well-suited for</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> d5ae9992f7505e84 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">We also observe some mix that suggests it is harder for the model to diffrentiatebetween closely rated reviews (for example between 4 and 5 star reviews).This is inline with the performance of the model. Here is the ROC AUC for variousclasses for this test dataset.ROC Curve1.00.80.60.4Class0 （AUC=0.97)0.2Class 1 (AUC =0.93)Class2（AUC=0.89）Class3（AUC=0.69)Class4(AUC=0.87)0.00.00.20.40.60.81.0False Positive RateWe can see that the ROC AUC for class 3 (equivalent to 4 star review)is lower thanthe rest. That explains why we see those orange dots all over the place in the TSNEplot. For more evaluation metrics please take a look at my previous work.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 1cd5d52af651ff54 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content"> size=20, ))fig_mean_pooled.update_layout(coloraxis_colorbar=dict( tickvals=colorbar_tickvals, ticktext=colorbar_ticktext))fig_mean_pooled.update_traces(marker=dict(size=10, opacity=0.9))fig_mean_pooled.show()ResultsUsing Plotly, an interactive visualization library, I created scatter plots to display these transformed embeddings. The plots are colored by the review labels, and hovering over any point reveals the actual review text, providing an intuitive way to explore the dataset. These visualizations serve as a map, guiding us through the landscape of consumer feedback.t-SNE of [CLS] Token Embeddings with Labelst-SNE Dimension 1t-SNE of Mean Pooled Embeddings with Labels2Ct-SNE Dimension 1We can see a clear pattern that high star reviews (orange and yellow points)are on the left side of the plot and the low star reviews (dark blue and purple)are on the right side of the plot.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> e6026c32cc197db1 | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">Get unlimited access to the best of Medium for less than $1/week. Become a memberFine-tuned BERT Embeddings and T-SNEVisualizationMina Mehdinia ·Follow6 min read ·Nov 29, 2023Share Listen MoreEmbeddings, generated by DALL·E 3IntroductionIn my previous work, I fine-tuned a BERT model to predict star ratings of Amazon product reviews. You can try the model on Hugging Face. Building on this, I am now focusing on generating embeddings using the same fine-tuned model. Theseembedding vectors, distilled from the model, capture the essence of each review in a multidimensional space. The power of embeddings lies in their ability to represent complex textual data in a form that machines can understand andprocess, making them crucial in various NLP tasks. I also use t-SNE to visualize these embeddings in 2D space.Understanding EmbeddingsBefore delving into the specifics of the project, it’s crucial to understand what embeddings are. In the context of NLP, embeddings are high-dimensional vectors that represent words or phrases in a continuous vector space. Each vector captures</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> d08f21288341c6de | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">5-star rating to a negative review, indicating anomalous behavior rather than a model limitation. The primary limitation of the model lies in its less distinct separation of certain classes, like 4-star reviews, where clarity in sentiment distinction is not as pronounced. These findings suggest that while the model iseffective in capturing general sentiment trends, there is room for improvement in differentiating more closely clustered sentiment categories. Future work couldfocus on refining the model to better distinguish these subtle differences inconsumer feedback. FollowWritten by Mina Mehdinia20 Followers ·2 FollowingFormally trained data scientist from four-year university program. Proficient in developing, training, andevaluating machine learning and deep learning modelsResponses (1)LiheyuzhangWhat are your thoughts?Ali NazemNov 29, 2023Learned a lot. Thanks for sharingReply More from Mina MehdiniaTouch point 5Touch point 2Touch point 3Touch point yYouTubeClick OrganicSaw Video AdＲＳP</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> abfb7e5341a59e16 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">19. Source Rock Quality Map -Germav Formation (Maastrichtian -Paleocene)VOLUMEII I INTRODUCTIOResults of Petroleum Geochemical Studies of the Interval2m to 2002m, of the Adiyaman-6 well (Report No, 4764P/D)1, Results of Petroleum Geochemical Studies of the Interval 2. 308-3582m, of the Alakamis-1 well (Report No, 4761P/D)Results of Petroleum Geochemical Studies of the Interval 3. 0'-10150', of the Aril-1 well (Report No, 4812P/D)Results of Petroleum Geochemical Studies of the Intervallm-3300m, of the Atabagi-1 well (Report No, 4857P/D)4. Results of Petroleum Geochemical Studies of the Interval 5. 750m-3045m(T,D,), of the Bollikyayla-5 well (Report No, 4859P/D)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a8ca36de674603c9 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">Gas Chromatography -Mass Spectrometry Fragmentograms for Asphaltites (Figures Cl-C3)Gas Chromatograms of Alkane and Aromatic Fractions of D. Asphaltites (not previously issued)(Figures Dl-D4)E. Pyrolysis -Gas Chromatograms of Source Rocks (not previously issued)(Figures El-E7)F. Pyrolysis-Gas Chromatograms of Asphaltene Fraction of Oils (not previously issued)(Figures Fl-F6)Pyrolysis-Gas Chromatograms of Asphaltene Fraction of G. Asphaltites (Figures Gland G2)R. Ballikaya Measured Stratigraphic Section Analytical Data (Tables Hl and H2, and Figure Hl)Spore Colour Indices against Depth Profiles -All Study Wells I. (Figures Il-112)J. Spore Colour Indices and Spore Colour Index Gradients for </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 002766bcd8c52fe2 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">aged Mardin Group Source Rock Quality Map -Bedinan Formation 9. (Llanvirnian -Llandeilian)Source Rock Quality Map -Bedinan Formation 10. (Caradocian -Ashgillian)11. Source Rock Quality Map -Dadas Formation (I)(Late Silurian -Early Devonian)12. Source Rock Quality Map -Dadas Formation (III)(Late Silurian -Early Devonian)ource Rock Quality Map -Gomaniibrik Formation (Permian)13. S14. Source Rock Quality Map -Uludere Formation (Triassic)15. Source Rock Quality Map -Karabogaz Formation (Coniacian -Campanian)16. Source Rock Quality Map -Sayindere Formation (Campanian)17. Source Rock Quality Map -Kastel Formation (Maastrichtian)18. Source Rock Quality Map -Kiradag Formation (Maastrichtian)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 0de2a2276bde26b4 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">IRAO. SOUTHEAS'PETROLEUMGEOCHEMISTRY STUDYSPORE COLOUR INDICES AT THE TOP OF THEMAASTRICHTIANAGEDSEDIMENTSIRAOTR.PAOMSOUTHEAST TUISPORE COLOUR INDICES AT THE TOP OF THEDPETROLEUMGEOCHEMISTRYSTUDYSPOREZOICSEDIMENTSENCLOSIRAOPETROAO SOUTHEAST TURSTUDYSPORECOLOURINDICESATTHETOPOFTHEAGED DADASFORMATIONENCLOSURE 6RAOT.P.A.O. SOUTHEAST TURKEYSPORE COLOUR INDEX GRADIENTSSENCLOSURE7IitasIRAOAXIMUMENCLOSURE 8COASTALPLAINLAND AREAMARINE EMBAYMENTPOOR? LAGRCEALSOURCEOm ROCKSUB-MARINEAIPIAINiSUB - MARiNeSWELLcO</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 0f65332636034e83 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content"> SWELLDISTRIBUTARYDISTRIBUTARYIRAQSYSTEMSYSTEMFAIR ALGAL SPETROLEO SGUTHEAST TUR STUDYFORMATION(LLANVIRNIAN-LLAROPENMARINESkilometresENCLOSURE9COASTALPLAIN?LAND AREASahaeADIYSOUOCKFAIR SOROCK-TIDRINROC! 46 ELMTVINALLAIDSHR MAIPLPMAOR.71 12 797IRAQFAIR!96*5.0ROOALGT.P.A.O. SOUTHEAST TURKEYPETROLEUM GEOCHEMISTRY STUDSOURCECK QUALITY MA?OPENS1? BASIN SILL?MARINE PLATEORM SOURCSEDIMENTDIRECTIONDADAS - I UNITNOT DEPOSITEDB/AISEUTHEASTT：LITY MAP-DADAS FORMATIONiTI!TyndSNCLOSURE 11OPEN MARINE CONDITIONS</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ab213efb51888257 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">SEARCHINTERNATIONAmaeternArMENCLOSURE 18FACITlo.FOFORMATION FACIES?TERDSSINAN E1349 .0INERTNITEGERMAVFORMATION FACIESGERIMAINERTINITEINERTINITEINERTINITEIRAOVITRINITET.P.A.O. SOUTHEAST TURKEYPETROLEUMGEOCHEMISTRYSTUDYSOURCE ROCK QUALITYMAP-GERMAVFORMATION(MAASTRICHTIAAN)-PALEOCENE)ENCLOSURE 19PREPARED AND PRINTED BY ROBERTSON RESEARCH INTERNATIONAL LIMITED, 1984. </div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b505bb82d8cc5193 | 
                    <strong>Document:</strong> AM005_Vol_2.pdf
                </div>
                <div class="chunk-content">41372.0VITSAPROPELKARABOGAZ FORMATION?SAPHO.NOT DEPOSITEDRAOISTydrogeSEDIMENT SOURCE AREAAREASOLSEDIMENTDOMIIINERTNIANDOIVNSAYINDERE FORMATIONNERTNOT DEPOSITEDIRAOAYINDERESEDIMENT SOURCE AREAGEVASINERTINITEDOMINANTSEDIIVFait galtysursDOMINAINERTINITE0.21-17.INERI200KASTEL FORMATION NOT DEPOSITED+ DerikBedIRAQSOURCE ROCK QUALITYMAPKASTELFORMATION (MAASTRICHTIAN)RdrogeSENCLOSURE 17AREASEDIMENT SOURCEINERTNITEANDINERTINITESAPROPELINERTINITE DOMINANTSHALESANDKASTEL/GERMAVFORMATIONFACIESKASTEL/GERMAV FORMATION FACIESRAUKASTELUNE 1984</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b207067b547438bd | 
                    <strong>Document:</strong> Fine-tuned BERT Embeddings and T-SNE Visualization _ by Mina Mehdinia _ Medium.pdf
                </div>
                <div class="chunk-content">REVIEWUSTOMER6SGREVIEWSAMAZON CUSTOMER REVIEWS RATINGREVLEWMina MehdiniaPredicting Amazon Customer Reviews Ratings with Naive BayesClick here for code广Oct 29, 2024See all from Mina MehdiniaRecommended from MediumTextEmbeddinginggUsinggBERTusteringSandeep Kasinampalli RajkumarExploring Text Embedding and Clustering Using BERTIntroductionSep 24, 20241In AI Mind byshashank JainFine-Tuning Large Language Models for Function Calling with LoRAIn this blog post, we’ll explore how to fine-tune large language models (LLMs)for functioncalling using LoRA (Low-Rank Adaptation). This…57Oct 5, 2024PYIn Python in Plain English byMohamad MahmoodBERT: Fine-tuning a Pre-trained Model for Text ClassificationThis article involves fine-tuning a pre-trained DistilBERT model for text classification, where</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 1992211d4d03f0e9 | 
                    <strong>Document:</strong> We’re Finally Starting to Understand How AI Works.pdf
                </div>
                <div class="chunk-content">By subscribing, I agree to Substack's Terms of Use, and acknowledgeits Information Collection Notice and Privacy Policy.28 Likes ∙1 RestackPrevious NextDiscussion about this postComments RestacksWrite a comment...Anthony Alles29 AprWith respect, I think that your title is incorrect. "We’re Finally Starting to Understand How AI Wono, based on your article, we may be starting to "observe"how AI works, but we clearly don't understand how or why. Machines don't spontaneously learn how to do abstract reasoning, or the motivations -i.e. the desire and intent -to lie, generate tendentious arguments, bypass programmed safeguards or invent answers wholly disconnected from known facts, presumably it has autonomously decided not to say, "I don't know". These are aspects of general intelligenc we clearly don't understand how this even developed, let alone how and why it works. How didthis develop from simply analyzing and categorizing data to discover correlations? Frightening.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> fd589ebdb395d2bd | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">and 6. Note that the attentions are very sharp for this word.14Input-lnputLayer5uoe!ldpinoysW川gupInpat-Inpit Layor5Ssidouooirub.Spinous3bMMburnSauuidoFigure 5: Many of the attention heads exhibit behaviour that seems related to the structure of the sentence. We give two such examples above, from two different heads from the encoder self-attentionat layer 5 of 6. The heads clearly learned to perform different tasks.15</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> db65af13cbef068b | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">2017.[19] Yoon Kim, Carl Denton, Luong Hoang, and Alexander M. Rush. Structured attention networks.In International Conference on Learning Representations, 2017.[20] Diederik Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In ICLR, 2015.[21]  Oleksii Kuchaiev and Boris Ginsburg. Factorization tricks for LSTM networks. arXiv preprintarXiv:1703.10722, 2017.[22] Zhouhan Lin, Minwei Feng, Cicero Nogueira dos Santos, Mo Yu, Bing Xiang, BowenZhou, and Yoshua Bengio. A structured self-attentive sentence embedding. arXiv preprintarXiv:1703.03130, 2017.[23] Minh-Thang Luong, Quoc V. Le, Iya Sutskever, Oriol Vinyals, and Lukasz Kaiser. Multi-task</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 6ff859be3d3695b0 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Advances in Neural Information Processing Systems 28, pages 2440-2448. Curran Associates,[35] Ilya Sutskever, Oriol Vinyals, and Quoc VV Le. Sequence to sequence learning with neuralnetworks. In Advances in Neural Information Processing Systems, pages 3104-3112, 2014[36] Christian Szegedy, Vincent Vanhoucke, Sergey Ioffe, Jonathon Shlens, and Zbigniew Wojna Rethinking the inception architecture for computer vision. CoRR, abs/1512.00567, 2015. [37] Vinyals & Kaiser, Koo, Petrov, Sutskever, and Hinton. Grammar as a foreign language. InAdvances in Neural InformationProcessing Systems, 2015.[38] Yonghui Wu, Mike Schuster, Zhifeng Chen, Quoc V Le, Mohammad Norouzi, WolfgangMacherey, Maxim Krikun, Yuan Cao, Qin Gao, Klaus Macherey, et al. Google's neural machine</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b45ba5433ccf4fbb | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">sequence to sequence learning. arXiv preprint arXiv:1511.06114, 2015.[24]  Minh-Thang Luong, Hieu Pham, and Christopher D Manning. Effective approaches to attention-based neural machine translation. arXiv preprint arXiv:1508.04025, 2015.11[25] Mitchell P Marcus, Mary Ann Marcinkiewicz, and Beatrice Santorini. Building a large annotatedcorpus of english: The penn treebank. Computational linguistics, 19(2):313-330, 1993.[26]  David McClosky, Eugene Charniak, and Mark Johnson. Effective self-training for parsing. InProceedings of the Human Language Technology Conference of the NAACL, Main Conference,pages 152-159. ACL, June 2006.[27] Ankur Parikh, Oscar Tackstrom, Dipanjan Das, and Jakob Uszkoreit. A decomposable attention</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 46183bed74f92eac | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Language Processing, pages 832-841. ACL, August 2009.[15] Rafal Jozefowicz, Oriol Vinyals, Mike Schuster, Noam Shazeer, and Yonghui Wu. Exploringthe limits of language modeling. arXiv preprint arXiv: 1602.02410, 2016.[16]  Lukasz Kaiser and Samy Bengio. Can active memory replace attention? In Advances in NeuralInformation Processing Systems, (NIPS), 2016.[17]  Lukasz Kaiser and Iya Sutskever. Neural GPUs learn algorithms. In International Conferenceon Learning Representations (ICLR), 2016.[18] Nal Kalchbrenner, Lasse Espeholt, Karen Simonyan, Aaron van den Oord, Alex Graves, and Ko-ray Kavukcuoglu. Neural machine translation in linear time. arXiv preprint arXiv: 1610.10099v2,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 5f89525d4d277bd6 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">translation system: Bridging the gap between human and machine translation. arXiv preprintarXiv:1609.08144,2016.[39] Jie Zhou, Ying Cao, Xuguang Wang, Peng Li, and Wei Xu. Deep recurrent models withfast-forward connections for neural machine translation. CoRR, abs/1606.04199, 2016.[40] Muhua Zhu, Yue Zhang, Wenliang Chen, Min Zhang, and Jingbo Zhu. Fast and accurateshift-reduce constituent parsing. In Proceedings of the 51st Annual Meeting of the ACL (VolumeI: Long Papers), pages 434-443. ACL, August 2013.12</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 31f2c6e2cf65b3da | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Luong et al. (2015) [23]multi-taskDyer et al. (2016) [8]93.3generativeincreased the maximum output length to input length + 300. We used a beam size of 21 and α = 0.3for both WSJ only and the semi-supervised setting.Our results in Table 4 show that despite the lack of task-specific tuning our model performs sur-prisingly well, yielding better results than all previously reported models with the exception of theRecurrent Neural Network Grammar [8].In contrast to RNN sequence-to-sequence models [37], the Transformer outperforms the Berkeley- Parser [29] even when training only on the WSJ training set of 40K sentences.7 ConclusionIn this work, we presented the Transformer, the first sequence transduction model based entirely onattention, replacing the recurrent layers most commonly used in encoder-decoder architectures with multi-headed self-attention.For translation tasks, the Transformer can be trained significantly faster than architectures based</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a7c7fa6fb6d18486 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">32324.9125.43216165.0125.1165.1658(B)325.0125.46023.76.1136245.1925.35084.8825.580256323228(C)5.7524.5102412812826.01684.661024535.1225.44096904.7526.20.05.7724.64.9525.50.2(D)0.04.6725.30.25.4725.74.92(E)positional embedding instead of sinusoids25.726.4big0.31024409616300K4.332136development set, newstest2013. We used beam search as described in the previous section, but no checkpoint averaging. We present these results in Table 3.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a313251cc18ececb | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">1.8 · 10201.1 · 102126.3041.16GNMT + RL Ensemble [38]26.3641.297.7 · 10191.2 · 1021ConvS2S Ensemble[9]27.338.13.3 · 1018Transformer (base model)Transformer (big)28.42.3·101941.8Residual DropoutWe apply dropout [33] to the output of each sub-layer, before it is added to thesub-layer input and normalized. In addition, we apply dropout to the sums of the embeddings and thepositional encodings in both the encoder and decoder stacks. For the base model, we use a rate of Pdrop = 0.1.Label Smoothing During training, we employed label smoothing of value Els = 0.1 [36]. This hurts perplexity, as the model learns to be more unsure, but improves accuracy and BLEU score.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b2c8d5c18788761b | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">In Table 3 rows (A), we vary the number of attention heads and the attention key and value dimensions,keeping the amount of computation constant, as described in Section 3.2.2. While single-headattention is O.9 BLEU worse than the best setting, quality also drops off with too many heads. In Table 3 rows (B), we observe that reducing the attention key size d hurts model quality. Thissuggests that determining compatibility is not easy and that a more sophisticated compatibilityfunction than dot product may be beneficial. We further observe in rows (C) and (D) that, as expected,bigger models are better, and dropout is very helpful in avoiding over-fitting. In row (E) we replace our sinusoidal positional encoding with learned positional embeddings [9], and observe nearly identicalresults to the base model.6.3English Constituency ParsingTo evaluate if the Transformer can generalize to other tasks we performed experiments on English constituency parsing. This task presents specific challenges: the output is subject to strong structural</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 243e2dd2a084b6f5 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">constraints and is significantly longer than the input. Furthermore, RNN sequence-to-sequence models have not been able to attain state-of-the-art results in small-data regimes [37].We trained a 4-layer transformer with dmodel = 1024 on the Wall Street Journal (WSJ) portion of the Penn Treebank [25], about 40K training sentences. We also trained it in a semi-supervised setting,using the larger high-confidence and BerkleyParser corpora from with approximately 17M sentences[37]. We used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary of 32K tokensfor the semi-supervised setting.We performed only a small number of experiments to select the dropout, both attention and residual (section 5.4), learning rates and beam size on the Section 22 development set, all other parameters remained unchanged from the English-to-German base translation model. During inference, we9 Table 4: The Transformer generalizes well to English constituency parsing (Results are on Section 23</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> dd5499616dd97a3e | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">or O(logk(n)) in the case of dilated convolutions [18], increasing the length of the longest pathsbetween any two positions in the network. Convolutional layers are generally more expensive thanrecurrent layers, by a factor of k. Separable convolutions [6], however, decrease the complexityconsiderably, to O(k · n · d + n · d²). Even with k = n, however, the complexity of a separableconvolution is equal to the combination of a self-attention layer and a point-wise feed-forward layer,额外好处：Self-the approach we take in our model.Attention更容易解释。As side benefit, self-attention could yield more interpretable models. We inspect attention distributions可以直接看Attention权重矩阵，看每个词关注了哪些词，来理解模型from our models and present and discuss examples in the appendix. Not only do individual attention</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 8dd8e59377297775 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">As noted in Table 1, a self-attention layer connects all positions with a constant number of sequentiallyexecuted operations, whereas a recurrent layer requires O(n) sequential operations. In terms ofcomputational complexity, self-attention layers are faster than recurrent layers when the sequencelength n is smaller than the representation dimensionality d, which is most often the case withsentence representations used by state-of-the-art models in machine translations, such as word-piece[38] and byte-pair [31] representations. To improve computational performance for tasks involvingvery long sequences, self-attention could be restricted to considering only a neighborhood of size r inthe input sequence centered around the respective output position. This would increase the maximumpath length to O(n /r). We plan to investigate this approach further in future work.A single convolutional layer with kernel width k < n does not connect all pairs of input and outputpositions. Doing so requires a stack of O(n /k) convolutional layers in the case of contiguous kernels,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> f88ebaaf7023e7c7 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">heads clearly learn to perform different tasks, many appear to exhibit behavior related to the syntactic在思考什么。and semantic structure of the sentences.多个注意力头可以各司其5Training职This section describes the training regime for our models.5.1Training Data and BatchingWe trained on the standard WMT 2014 English-German dataset consisting of about 4.5 millionsentence pairs. Sentences were encoded using byte-pair encoding [3], which has a shared source-target vocabulary of about 37000 tokens. For English-French, we used the significantly larger WMT 2014 English-French dataset consisting of 36M sentences and split tokens into a 32000 word-piecevocabulary [38]. Sentence pairs were batched together by approximate sequence length. Each trainingbatch contained a set of sentence pairs containing approximately 25000 source tokens and 25000target tokens.5.2Hardware and ScheduleWe trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models using</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3b4f8bf8561da16b | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">tokens in the sequence. To this end, we add "positional encodings" to the input embeddings at the递归或卷积结构，无法自然地处理输入标记的顺序 bottoms of the encoder and decoder stacks. The positional encodings have the same dimension dmodel信息，因此需要在输入嵌入向量上添加位置编码,as the embeddings, so that the two can be summed. There are many choices of positional encodings,learned and fixed [9].让模型理解序列的顺序。In this work, we use sine and cosine functions of different frequencies:PE(pos,2i) = sin(pos/100002i/dmodel)PE(pos,2i+1) = cos(pos/10002i/dmodel)where pos is the position and  is the dimension. That is, each dimension of the positional encodingcorresponds to a sinusoid. The wavelengths form a geometric progression from 2π to 10000 · 2π. We</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 7d84e21d5d31fb76 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">么选择Self-Attentionconsider three desiderata.One is the total computational complexity per layer. Another is the amount of computation that can第一点：每层的总计算复be parallelized, as measured by the minimum number of sequential operations required.杂度;The third is the path length between long-range dependencies in the network. Learning long-range dependencies is a key challenge in many sequence transduction tasks. One key factor affecting the第二点：并行计算能力;ability to learn such dependencies is the length of the paths forward and backward signals have totraverse in the network. The shorter these paths between any combination of positions in the inputand output sequences, the easier it is to learn long-range dependencies [12]. Hence we also compare第三点：模型内部学习长 the maximum path length between any two input and output positions in networks composed of the距离依赖的能力different layer types.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 842ab44e5c7585b2 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Instead of performing a single attention function with dmodel-dimensional keys, values and queries,we found it beneficial to linearly project the queries, keys and values h times with different, learned单头注意力：一次性处linear projections to dk, dk and d, dimensions, respectively. On each of these projected versions of理整个512维，一次性给出一个注意力结果。queries, keys and values we then perform the attention function in parallel, yielding d,-dimensional 4To illustrate why the dot products get large, assume that the components of q and k are independent random多头注意力：把512维Variables with mean O and variance 1. Then teir dot product k == a ke, has mean 0 and variance d k.分成8份（每份64维）分别处理；8个头分别给出结果，然后拼起来。4多头注意力的优势就是</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 3e799f4429cf8617 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">量），维度为d的value (值向量)values.2.计算相似度（点积）矩 In practice, we compute the attention function on a set of queries simultaneously, packed together阵：QKT into a matrix Q. The keys and values are also packed together into matrices K and V. We compute3.进行缩放the matrix of outputs as:4.应用softmax得到权重5.对value 进行加权平均：乘VQKT6.得到注意力输出Attention(Q, K, V) = softmax((1)F)VVdk最常用的两种注意力函The two most commonly used attention functions are additive attention [2], and dot-product (multi- 数: 加性注意力和点击注意力plicative) attention. Dot-product attention is identical to our algorithm, except for the scaling factor点积注意力与我们的算法</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 9b457431882f7907 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">. Additive attention computes the compatibility function using a feed-forward network withofdk几乎相同，唯一区别是我们添加了缩放因子。 a single hidden layer. While the two are similar in theoretical complexity, dot-product attention ismuch faster and more space-efcient in practice, since it can be implemented using highly optimized选用点积注意力，是因为它计算更快也更省内存。matrix multiplication code.While for small values of d the two mechanisms perform similarly, additive attention outperforms dot product attention without scaling for larger values of d [3]. We suspect that for large values of为什么我们要加这个缩放dk, the dot products grow large in magnitude, pushing the softmax function into regions where it has因子3.2.2Multi-Head Attentiondmodel:每个词的表示维度 (假设为512维)</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 665d4fbf39cf1c7c | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">attention over the output of the encoder stack. Similar to the encoder, we employ residual connections在解码器中，自注意力机制被做了特殊修改，确保每个生成的词只能依赖之around each of the sub-layers, followed by layer normalization. We also modify the self-attention sub-layer in the decoder stack to prevent positions from attending to subsequent positions. This masking, combined with fact that the output embeddings are offset by one position, ensures that the前的词，而不是未来的词。 predictions for position i can depend only on the known outputs at positions less than i.3.2AttentionAttention函数：接收一个查询 (query)和一组An attention function can be described as mapping a query and a set of key-value pairs to an output,键-值对(key-value)where the query, keys, values, and output are all vectors. The output is computed as a weighted sum</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> c75c8ad136615a6f | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">Provided proper attribution is provided, Google hereby grants permission to reproduce the tables and figures in this paper solely for use in journalistic orscholarly works.Attention Is All You Need32Ashish Vaswani*Noam Shazeer*Jakob Uszkoreit*Niki Parmar*2Google BrainGoogle <NAME_EMAIL>@<EMAIL>@google.com三AAidan N. Gomez* tLlion Jones*Lukasz Kaiser*Google ResearchUniversity <NAME_EMAIL>@<EMAIL> Polosukhin* illia.polosukhin@gmail.comAbstract7Transformer之前V主流序列转导模型的结构：.21.基于RNN／CNNThe dominant sequence transduction models are based on complex recurrent or62.使用编码器-解码器结构3.使用注意力机制增强</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 0b708155a105790f | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">our model establishes a new single-model state-of-the-art BLEU score of 41.8 aftertraining for 3.5 days on eight GPUs, a small fraction of the training costs of thebest models from the literature. We show that the Transformer generalizes well toother tasks by applying it successfully to English constituency parsing both withlarge and limited training data.*Equal contribution. Listing order is random. Jakob proposed replacing RNNs with self-attention and startedthe effort to evaluate this idea. Ashish, with Illia, designed and implemented the first Transformer models andhas been crucially involved in every aspect of this work. Noam proposed scaled dot-product attention, multi-head attention and the parameter-free position representation and became the other person involved in nearly everydetail. Niki designed, implemented, tuned and evaluated countless model variants in our original codebase andtensor2tensor. Llion also experimented with novel model variants, was responsible for our initial codebase, andefficient inference and visualizations. Lukasz and Aidan spent countless long days designing various parts of and</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> d7d5aeffddf5f827 | 
                    <strong>Document:</strong> 《Attention Is All You Need》笔记版.pdf
                </div>
                <div class="chunk-content">convolutional neural networks that include an encoder and a decoder.The best7performing models also connect the encoder and decoder through an attention3mechanism. We propose a new simple network architecture, the Transformer,based solely on attention mechanisms, dispensing with recurrence and convolutions0Transformer结构的创新：1.完全摒弃RNN/CNN62． (仍然使用编码器-解码器构)entirely. Experiments on two machine translation tasks show these models to03.完全基于注意力机制be superior in quality while being more parallelizable and requiring significantly7less time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-1Transformer的优异表现to-German translation task, improving over the existing best results, includingensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ccf68db83257fad9 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">ing vision-language model's perception of the world at any resolution. arXiv preprint arXiv:2409.12191.Ming Yan, Guohai Xu, Chenliang Li, Junfeng Tian,11Qi Qian, Ji Zhang, et al. 2023b. UReader: Univer-sal OCR-free visually-situated language understand-ing with multimodal large language model. arXiv preprint arXiv:2310.05126.Ya-Qi Yu, Minghui Liao, Jihao Wu, Yongxin Liao, Xiaoyu Zheng, and Wei Zeng. 2024a. TextHawk: Exploring efficient fine-grained perception of mul-timodal large language models.  arXiv preprintarXiv:2404.09204.Ya-Qi Yu, Minghui Liao, Jiwen Zhang, and Jihao Wu.2024b. TextHawk2: A large vision-language model</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b1ce8a0d414a229a | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">arXiv:2408.01800.DocLLM: A layout-aware generative language model Jiabo Ye, Anwen Hu, Haiyang Xu, Qinghao Ye, Mingfor multimodal document understanding. In Proceed-ings of the Annual Meeting Of The Association ForYan, Yuhao Dan, Chenlin Zhao, Guohai Xu, Chen-liang Li, Junfeng Tian, et al. 2023a. mPLUG-Computational Linguistics. DocOwl: Modularized multimodal large languagePeng Wang, Shuai Bai, Sinan Tan, Shijie Wang, Zhi- model for document understanding. arXiv preprinthao Fan, Jinze Bai, Keqin Chen, Xuejing Liu, JialinarXiv:2307.02499.Wang, Wenbin Ge, et al. 2024d. Qwen2-VL: Enhanc- Jiabo Ye, Anwen Hu, Haiyang Xu, Qinghao Ye,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 24c53b53976504b6 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">centric visual question answering. arXiv preprintarXiv:2405.11985.Yonghui Wang, Wengang Zhou, Hao Feng, KeyiTowards im-Jingqun Tang, Su Qiao, Benlei Cui, Yuhang Ma, ShengZhou, and Houqiang Li. 2023.Zhang, and Dimitrios Kanoulas. 2022a. You canproving document understanding: An explorationarXivpreprinteven annotate text with voice: Transcription-only-on text-grounding via mllms.supervised text spotting. In Proceedings of the ACMarXiv:2311.13194. International Conference on Multimedia, pages 4154-Haoran Wei, Lingyu Kong, Jinyue Chen, Liang Zhao,4163.Zheng Ge, Jinrong Yang, Jianjian Sun, Chunrui Han,and Xiangyu Zhang. 2024a. Vary: Scaling up the</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 70e0e0a4f644ada6 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">2024. Gemini 1.5: Unlocking multimodal under-Yang Xu, Yiheng Xu, Tengchao Lv, Lei Cui, Furustanding across millions of tokens of context. arXivWei, Guoxin Wang, Yijuan Lu, Dinei Florencio, Cha preprint arXiv:2403.05530. Zhang, Wanxiang Che, et al. 2020a. LayoutLMv2:Ashish Vaswani, Noam Shazeer, Niki Parmar, JakobMulti-modal pre-training for visually-rich documentUszkoreit, Llion Jones, Aidan N Gomez, Lukaszunderstanding. arXiv preprint arXiv:2012.14740.Kaiser, and Illia Polosukhin. 2017. Attention is allYiheng Xu, Minghao Li, Lei Cui, Shaohan Huang,you need. In Proceedings of the Neural Information</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 402500787fa18521 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">with vision language models. arXiv preprint626.arXiv:2502.18443.9. Bart: Denoising sequence-to-Mike Lewis. 2019. Brandon Smock, Rohith Pesala, and Robin Abraham. sequence pre-training for natural language genera- 2022. PubTables-1M: Towards comprehensive tabletion, translation, and comprehension. arXiv preprintextraction from unstructured documents. In Proceed-arXiv:1910.13461.ings of the IEEE Conference on Computer Vision andZhang Li, Biao Yang, Qiang Liu, Zhiyin Ma, Shuo Pattern Recognition, pages 4634-4642.Zhang, Jingxu Yang, Yabo Sun, Yuliang Liu, andXiang Bai. 2024. Monkey: Image resolution andJingqun Tang, Chunhui Lin, Zhen Zhao, Shu Wei,Binghong Wu, Qi Liu, Hao Feng, Yang Li, Siqi</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> e655972ba28898d7 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Zhao, Yilin Jia, Lei Cui, Shuming Ma, Yaoyao Chang,Shaohan Huang, Wenhui Wang, et al. 2023. Kosmos-Yupan Huang, Tengchao Lv, Lei Cui, Yutong Lu, andFuru Wei. 2022. LayoutLMv3: Pre-training for doc-2.5: A multimodal literate model. arXiv preprintument ai with unified text and image masking. InarXiv:2309.11419.Proceedings of the ACM International Conference onMultimedia, pages 4083-4091. John MacFarlane. 2013. Pandoc: a universal documentDonghyun Kim, Teakgyu Hong, Moonbin Yim, Yoonsikconverter. URL: http://pandoc. org, 8.Kim, and Geewook Kim. 2023. On web-based visual</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 7d20bc10726ca8ba | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">text label are important things for large multi-modalmodels. In Proceedings of the IEEE ConferenceWang, Lei Liao, et al. 2024a. TextSquare: Scaling upon Computer Vision and Pattern Recognition, pagestext-centric visual instruction tuning. arXiv preprintarXiv:2404.12803.26763-26773.10Jingqun Tang, Qi Liu, Yongjie Ye, Jinghui Lu, ShuPeng Wang, Zhaohai Li, Jun Tang, Humen Zhong, FeiWei, Chunhui Lin, Wanqing Li, Mohamad FitriHuang, Zhibo Yang, and Cong Yao. 2024e. Platy- Pus: A generalized specialist model for reading textFaiz Bin Mahmood, Hao Feng, Zhen Zhao, et al. 2024b. MTVQA: Benchmarking multilingual text-in various forms. In Proceedings of the EuropeanConference on Computer Vision, pages 165-183.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 51f04598164c854a | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> and accuracy, while maintaining a lightweight ar-tion via a vision-language model. arXiv preprintThrough extensive experiments, wechitecture.arXiv:2501.15558.demonstrate Dolphin's strong performance in bothZhe Chen, Jiannan Wu, Wenhai Wang, Weijie Su, Guo page-level and element-level parsing tasks, partic-Chen, Sen Xing, Muyan Zhong, Qinglong Zhang,Xizhou Zhu, Lewei Lu, et al. 2024. InternVL: Scal-ularly excelling in handling complex documentswith interleaved tables, formulas, and rich format-ting in both Chinese and English.ing up vision foundation models and aligning forgeneric visual-linguistic tasks. In Proceedings of theTEEE Conference on Computer Vision and PatternRecognition, pages 24185-24198.LimitationsChangxu Duan and Sabine Bartsch. LaTex rainbow: Despite Dolphin's promising performance, there</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> fb7077f9983729a0 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Hao Feng, Zijian Wang, Jingqun Tang, Jinghui Lu, Wen-gang Zhou, Houqiang Li, and Can Huang. 2023.Lee. 2024b. Visual instruction tuning. In Proceed-ings of the Neural Information Processing Systems,UniDoc: A universal large multimodal model for si-volume 36.multaneous text detection, recognition, spotting andunderstanding. arXiv preprint arXiv:2308.11592.Yuliang Liu, HBiao Yang, Qiang Liu, Zhang Li,Zhiyin Ma, Shuo Zhang, and Xiang Bai. 2024c. Jonathan Herzig, Pawel Krzysztof Nowak, ThomasTextMonkey: An OCR-free large multimodal modelMueller, Francesco Piccinno, and Julian Eisensch-los. 2020. TaPas: Weakly supervised table parsingarXiv  preprintfor  understanding  document.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 2f6e7a81b4f13989 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Correctly parsed as and table cells. Fourth, handwriting recognitionViT-L84.811.63.7xHTML table and ViT-H, w/ [M]  8119.6+successfully renderedcapabilities require further enhancement.ViT-H85.834.53.5xViT-H85.929.34.1xFigure 6: A case study demonstrating the effectivenessReferences of type-specific prompts. The generic prompt misidenti-fies the table as a formula, while our approach correctlyHaoli Bai, Zhiguang Liu, Xiaojun Meng, ShuangLiu, LUO Yifeng, Rongfu Zheng, Liangwei Wang,Lu Hou, Jiansheng Wei, Xin Jiang, et al. 2023. parses and renders the table in HTML format.Wukong-Reader: Multi-modal pre-training for fine- grained visual document understanding. In Proceed-nize elements at specific box (see Figure 5 (left).</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> c010e099506ee397 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">6Conclusiontions.We present Dolphin, a novel document image pars-Mingxu Chai, Ziyu Shen, Chong Zhang, Yue Zhang,Xiao Wang, Shihan Dou, Jihua Kang, Jiazhenging model that leverages an analyze-then-parseZhang, and Qi Zhang. 2024. DocFusion: A uni-paradigm to address the challenges in documentfied framework for document parsing tasks. arXivparsing. Our approach first performs page-level lay- preprint arXiv:2412.12505. out analysis to generate structured layout elementsin reading order, then enables parallel element pars-ing through heterogeneous anchor prompting. ThisSong Chen, Xinyu Guo, Yadong Li, Tao Zhang,Mingan Lin, Dongdong Kuang, Youwei Zhang,Lingfeng Ming, Fengyu Zhang, Yuran Wang, et al.two-stage design effectively balances efficiency2025. Ocean-OCR: Towards general OCR applica-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 4f970aa7dbae22ef | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">c}&\text{hourl{speearNevertheless, we demonstrate some cases exhibit-ing emergent multilingual document parsing capa-GenericPrompt:1\mathrm{ViT}-\mathrm{L}&8&84.9&15.4&2.8\times \Misidentified as\mathrmViT-mathrm(L}&1884.8&1.6&mathbf3.7)timesformula bilities in the supplementary materials. Third, al-erf&text{{&119.6mathrm(H}&1&85.9&29.3&\mathbf(4.1)\times\endaray$$though we achieve efficiency gains through parallelencoderspeedupdec.depthft acchourselement parsing, there is potential for further opti-ViT-L, w/ [M]84.242.48mization through parallel processing of text linesViT-LType-specificPrompt:84.915.42.8x</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 164194fb148439cd | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Element Cropping vs. Box Query. To vali-gains from our parallel decoding strategy in stagedate our element cropping strategy in the second2, we compare our approach with a sequential au-approach that directly prompts the model to recog-stage, we compare it with an alternative box querytoregressive decoding baseline. As present in Ta-8speeduphorizontal text layout, showing limited capability[M]15.42.8x84.9ViT-Lin parsing vertical text like ancient manuscripts.3.7×84.811.6Input TableImageViT-LSecond, while Dolphin handles both Chinese andViT-H, w/ [M]19.685.834.3ViT-H3.5xEnglish documents effectively, its multilingual ca-ViT-H85.94.1xSbginaray(ltet encoder \text dec depth &\tex tpacity (Tang et al., 2024b) needs to be expanded.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 10fcaaf0c3a43c01 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> based parsing and content alignment with source column and multi-column formats. Each page con-markdown, we obtain hierarchical text annotationstains over 1,000 words, making it a challengingat paragraph, line, and word levels, as well as sometestbed for document image parsing.specific element types like tables. Furthermore, we(b) Dolphin-Page. Our Dolphin-Page is a bilin-render the formula in different colors and find all gual benchmark of 210 document pages designed formula blocks based on pixel matching.for complex document parsing. It consists of 111Tables. For table parsing, we utilize PubTab-Net (Zhong et al., 2020) and PubTab1M (Smock pure text documents and 99 challenging sampleswith interleaved tables, mathematical formulas, andet al., 2022), two large-scale datasets of tablesfigures in both single-column and multi-columnextracted from scientific publications. PubTab-Net contains 568K tables with HTML annotations,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 417106b108464a50 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">documents） and individual element recognitionconducted on two widely-used benchmarks: Pub-TabNet (Zhong et al., 2020) and PubTab1M (Smocktasks (text paragraphs, tables, and formulas). Page-level Parsing. We evaluate Dolphin's per-et al., 2022). The test set of PubTabNet containsformance on Fox-Page (English and Chinese) and7,904 table images from scientific papers, whileDolphin-Page benchmarks. As shown in Table 1,PubTab1M's test set consists of 10,000 more chal-lenging samples. Both benchmarks evaluate thedespite its lightweight architecture (322M parame-ters), Dolphin achieves superior performance com-model's capability in understanding table struc-pared to both integration-based methods and largertures and recognizing cell contents using TEDSVLMs. For pure text documents, Dolphin achieves an edit distances of 0.0114 and 0.0131 on English</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 0f25478070dddd97 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">0.1849(Stage 2), and finally to the rendered document.As demonstrated, Dolphin accurately captures bothTable 4: Ablation studies on Dolphin. The first rowlayout structure and textual content. As shown inshows the performance of our full model. The evalua- Figure 5 (left), Dolphin also exhibits strong text ex-tion is conducted on Dolphin-Page dataset. traction capabilities by accurately parsing contentfrom specified bounding box regions. ble 4, parallel decoding achieves a 1.8x speedupElement-level Parsing. Beyond page-level pars-(0.1729 vs. 0.0971 FPS) while maintaining theing, we conduct extensive experiments to evalu-ate Dolphin's performance on individual elements,same parsing accuracy. The speedup is boundedby two factors: (a) the preprocessing overhead for as shown in Table 3. For text paragraph parsing,each element before network inference, and (b) theDolphin achieves competitive results on both Fox-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> d311d3e5c4b79ba8 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">0.95260.93360.75190.68080.65880.0029 0.0121OursDolphin0.01360.98500.96850.87390.95150.9625Table 3: Performance comparison of element-level parsing across text paragraphs, formulas, and tables. Arrows "↑/↓” indicate whether higher/lower values are better. Results marked with “** are reported by UnimerNet.character, word, line and paragraph levels.4.2EvaluationLaTeX. We collect 0.5M documents from theThe evaluation is conducted at both the page andarXiv database and process them using LaTeX the element levels. At the page level, we evalu- Rainbow (Duan and Bartsch), a specialized ren- ate the models on two distinct benchmarks: Fox-dering framework that preserves document hierar-chical structure. This tool renders different element</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 5c6cd67404b19d57 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">level components. A comprehensive breakdown ofcoded in parallel using the same Swin Transformer,our training dataset, including data sources, gran-ularities, and task, is shown in Table 2. In the fol-producing element-specific visual features.lowing, we describe the preparation and collectionParallel Content Parsing. With the encoded4Reading Order & LayoutSpansMarkdownEnglish document Single columnw/ Inline formula-w/ Block formula~English documentDouble columnw/ TableChinese documentDouble columnPure textFigure 3: Visualization of Dolphin's page-level parsing results. Left: Layout analysis form Stage 1 with predictedelement boundaries and reading order. Middle: Element-specific parsing outputs from Stage 2. Right: Finalrendered document in markdown format. More cases are shown in the supplementary material.Granularity #Samples Task TypesSourceof data for different training objectives.Mixed DocumentsPage0.12MLayoutHTML4.37MParsingPageMixed Documents. We collect 0.12M docu-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> c0ade9adae97929b | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">We adopt mBart (Lewis, 2019) as the decoder. Withelement features, we employ type-specific prompts to guide the parsing of different elements. Asthe prompt "Parse the reading order of this doc-ument.", the model identifies and arranges docu-shown in Figure 2 (right), tables employ dedicatedprompts Ptable to parse their HTML format, whilement elements sequentially, while preserving struc-tural relationships (e.g., figure-caption pairs, table-caption associations, and section title-paragraphformulas share the same prompt Pparagraph as textparagraphs since they frequently appear both in- hierarchies). As shown in Figure 2, it generatesline and in display mode within paragraph context,a sequence of layout elements L = {l1, l2, .., ln},despite their LaTeX markup format. Given the vi-sual feature of the local view I and its correspond-where element li specifies its type (e.g.,figure, cap-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 7a211160c94f94a5 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Page (Liu et al., 2024a), which consists of pure textdocuments, and our newly constructed Dolphin-(e.g., formulas, figures) with distinct colors while Page containing complex documents with inter-maintaining the reading order. The rendered docu-leaved figures, tables, and mathematical formulas.ments are then automatically parsed to extract ele-At the element level, we assess the fine-grainedment types, hierarchical relationships, and spatial parsing capabilities for text-paragraph, formulas,locations at block, line, and word levels.and tables through the public test sets.Markdown. We collect 0.71M markdown docu-Page-level Evaluation:ments from GitHub pages and process them using(a) Fox-Page. Fox-Page is a bilingual bench- Pandoc (MacFarlane, 2013) for PDF rendering withmark containing 212 document pages (112 in En-several customized templates. Through PyMuPDF-glish and 100 in Chinese) including both single-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 821bfa5b7dfed09d | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">GPT-41-2504140.02480.04010.21340.1227Step-1v-8k0.0417Qwen2-VL7B0.12360.16150.36860.25500.0315Qwen2.5-VL0.01350.02700.20250.11120.03437B0.0114OursDolphin322M0.01310.10280.05750.1729Table 1: Performance comparison of page-level document parsing. “Plain Doc” represents documents containingonly text content, while “Complex Doc” includes documents with mixed elements (tables, formulas, and figures).Arrow "↑/↓" indicate whether higher/lower values are better. Results marked with "***”' are reported by GOT. Boldfaceindicates the best performance and underlined values denote the second-best.</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 906cac58bbd8ae6c | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">parallel parsing of individual elements. The core of both stages is a unified vision-language model,which shares the same parameters but operates on2024), TGDoc (Wang et al., 2023), Vary (Wei et al. different input granularities with distinct prompting2024a), Fox (Liu et al., 2024a), Monkey-series (Listrategies, as presented in Figure 2 (right).et al., 2024; Liu et al.,2024c),TabPedia (Zha0et al., 2024a), TextSquare (Tang et al., 2024a), Doc-3.2 Page-level Layout AnalysisFusion (Chai et al., 2024), TextHawk-series (YuThis stage aims to identify the layout elements andet al., 2024a,b), mPLUG-DocOwl-series (Ye et al., their reading order through the following steps: 2023a; Hu et al., 2024), SmolDocling (Nassar et al.,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 424b5fc5be63fd62 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">0.53390.67970.12200.51010.07120.94840.61690.64620.0803 0.0301Qwen2.5-VL-7B0.94860.83090.0108General VLMs Gemini-1.5 pro0.08570.95720.94690.71710.75710.77760.04610.1177Claude3.5-Sonnet 0.03750.07460.94640.75430.54310.71270.89950.0170 0.10190.04890.77220.66920.72430.9402GPT-40-2024080.9570Step-1v-8k0.0098 0.01750.0252</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 87a27bf606c96442 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">tion, table, paragraph) and bounding box. Thising prompt pi, the decoder generates the parsedstructured layout sequence provides anchors forcontent in parallel. This parallel processing strat-the subsequent element-level parsing stage.egy, combined with element-specific prompting,ensures computational efficiency while maintain- 3.3 Element-level Content Parsinging accurate content recognition.The second stage leverages the analyzed layout ele- Dataset4ments as anchors for parallel element parsing. Thisdesign marks a key departure from purely autore-To enable comprehensive training and evaluation, gressive approaches, enabling efficient processingwe construct large-scale datasets spanning multiple document granularities and parsing tasks.while maintaining element-specific expertise. Weachieve this through two steps:Training4.1Element Image Encoding. For each layout el- ement l identified in the first stage, we crop its For training, we collect over 30 million samplescorresponding region from the original image tocovering both page-level documents and element-create a local view I. These local views are en-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> b472ffa1ce55c75c | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">tasks, we construct a large-scale dataset of 30image parsing, categorized into two streams.million samples containing both page-level doc-General VLMs. With the rapid developmentuments and element-level blocks. Notably, Dol-phin's element-decoupled parsing strategy offersof large vision-language models, researchers havebegun exploring the application of general-purposeunique advantages in data collection, as acquiringVLMs (Liu et al., 2024b) to document parsingisolated element images (e.g., tables, formulas) andtasks. Models such as GPT-4V (Yang et al.,their annotations is more feasible than collecting2023), Claude-series3, Gemini-series (Team et al.,full document pages with diverse elements.2024), QwenVL-series (Wang et al., 2024d; BaiComprehensive evaluations are conducted onet al., 2025), MiniCPM-series (Ya0 et al., 2024),InternVL-series (Chen et al., 2024), DeepSeek-VL2 (Wu et al., 2024), and Step-1V demonstrate</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 0dd7d273db2f744e | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">prevalent benchmarks and self-constructed ones.The results show that Dolphin achieves state-of-the-art performance across diverse page-level andpromising results in document understanding with-element-level parsing tasks (Figure 1). Moreover,out task-specific training. These models benefitbenefiting from its lightweight architecture and par-from large-scale pre-training on diverse visual data,allel element parsing mechanism, Dolphin exhibitsexhibiting strong zero-shot capabilities. However, considerable advantages in running efficiency.they frequently face challenges in processing eff-ciency, specialized element recognition, and layout2Related Workstructure preservation, particularly when process-ing long documents with complex layouts.Document image parsing enables robust contentExpert VLMs. These models are specifically de-extraction from rendered document images withoutrelying on source file formats or parsing librariessigned and trained for document parsing or under-(e.g., PyMuPDF). Existing solutions can be catego-standing tasks. Nougat (Blecher et al.) pioneeredrized into two streams: integration-based methods</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 66161aeaf39cfdf1 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">https://mathpix.com/pdf-conversion/that assemble multiple expert models in a pipeline,https://www.textin.ai/and end-to-end approaches that leverage vision-https://www.anthropic.com/news/claude-3-5-sonnet2TextParagraphTableFormulaDolphinLayout Analysis0.49, 0.93 051, 0.9419FootnotablSwinTransfornDecoDolphinTextLaTexHTMLElement-specific2124ent-level ContentParsingStage-2:ElenFigure 2: Overview of Dolphin's two-stage document image parsing paradigm. Left: The pipeline consists of Stage 1 for page-level layout analysis that generates structured layout sequences in reading order, and Stage 2 forelement-level content parsing. Right: Examples of input-output pairs, including page-level layout analysis andelement-level content parsing for text paragraphs, tables, and formulas. “ P*" denotes different prompts.this direction by introducing an encoder-decoder3.1</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 50837dc4db96af0b | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Overviewmodel that converts documents into markup lan- Dolphin follows an analyze-then-parse paradigmguage. GOT (Wei et al., 2024b) presented anbuilt upon an encoder-decoder transformer archi-innovative unified model that processes varioustecture. As shown in Figure 2 (left), given an input document elements. Other representative work document image I, the first stage performs page-such as Donut (Kim et al., 2022), LayoutLM-series (Xu et al., 2020b,a; Huang et al., 2022),UDOP (Tang et al., 2023), Wukong-Reader (Bailevel layout analysis to extract elements in readingorder. These elements then serve as anchors for thesecond stage, where type-specific prompts guideet al., 2023), KOSMOS-series (Lv et al., 2023;Peng et al., 2023), UniDoc (Feng et al., 2023),UReader (Ye et al., 2023b), DocPedia (Feng et al.,</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 2184e168c2c72b93 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">tably, MinerU advances this direction by introduc-ing sophisticated content filtering and segmentationstage, Dolphin performs comprehensive page-levellayout analysis by generating an element sequence strategies. These methods demonstrate strong per-in natural reading order, while preserving rich struc-formance through specialized expertise and havetural relationships (e.g., figure-caption pairs, table-shown significant potential in high-precision con-tent extraction. However, they face challenges incaption associations, and section title-paragraphhierarchies). These analyzed elements then servesystem complexity, cross-model coordination, andas anchors for the second stage, where element-limited understanding of complex document lay- specific prompts enable efficient parallel parsingouts when compared to end-to-end approaches.of multiple elements. The focused context within each element allows the vision-language model to2.2 Autoregressive Document Parsingeffectively recognize the document contents.Recent advances in vision-language models haveTo train Dolphin on different granularities ofenabled a new paradigm of end-to-end document</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a1d993fe13328c9b | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">they also encounter layout structure degradationTraditional document parsing solutions rely on in-and efficiency bottlenecks when parsing long docu-tegrating multiple specialized models in a multi-ments with complex layouts.stage pipeline (Xu et al., 2020b; Herzig et al., 2020; To synergize the advantages of both approachesZhang et al., 2017). These approaches typicallywhile addressing their limitations, we present Dol-begin with layout detection to identify differentphin (Document Image Parsing via Heterogeneoustypes of elements (e.g., tables, formulas), followedAnchor Prompting), a novel vision-language model by dedicated recognizers for each element type.following an analyze-then-parse paradigm. Ratherthan relying on multiple expert models or purely au-toregressive generation, Dolphin decomposes doc-ument parsing into two strategic stages. In the firstRecent commercial and academic solutions suchas Mathpix', TextIn?, and MinerU (Wang et al.,2024b) follow this integration-based paradigm. No-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 8d4553d55e349102 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">5tions on both prevalent benchmarks and self-exponential growth of digital documents across do-constructed ones, Dolphin achieves state-of-the-art performance across diverse page-levelmains like academic papers, business reports, and2technical documentation, robust document parsingand element-level settings, while ensuring supe-rior efficiency through its lightweight architec-capabilities have become increasingly critical.ture and parallel parsing mechanism. The code Current document image parsing solutions haveand pre-trained models are publicly available atevolved along two distinct trajectories. The firsthttps://github.com/ByteDance/Dolphinone (Wang et al., 2024b） integrates specializedmodels for different OCR (Tang et al., 2022b; Zhaoet al., 2024b; Tang et al., 2022a) tasks (e.g., layout11Introductiondetection, reading order prediction, and recognition Document image parsing (Blecher et al.) aimsfor textlines, formulas, and tables). These solutions</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 9b42a3964180adae | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Dolphin: Document Image Parsing via Heterogeneous Anchor PromptingHao Feng*, Shu Wei*, Xiang Fei*, Wei Shi*t, Yingdong Han, Lei Liao,Jinghui Lu, Binghong Wu, Qi Liu, Chunhui Lin, Jingqun Tang, Hao Liu, Can Huang ByteDanceFormula-SPE (CDM)AbstractDolphinBlock(ED)Formula-SCE (CDM)5Document image parsing is challenging due2to its complexly intertwined elements such asula-CPE (CDM)FoxBlock-ZH (ED)0text paragraphs, figures, formulas, and tables.2 Current approaches either assemble special-ized expert models or directly generate page-Mlevel content autoregressively, facing integra-tion overhead, efficiency bottlenecks, and lay-out structure degradation despite their decentoxBlock-e-PubTab20performance. To address these limitations, weDolphinPage (ED)present Dolphin (Document Image Parsing via</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a044087128f9604e | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">1epsek-3 为自研 MoE 模型，671B参数，漫DeepSeek-V3为自研MoE模型，671B参数，激DeepSeek-V3 为自研 MoE 模型,671B 参数,激活 37B，在14.8T token 上进行了预训练。活 37B，在 14.8T token 上进行了预训练。活37B，在14.8Ttoken上进行了预训练。文链接论文链接：论文链接：https://github.com/deepseek-ai/DeepSeek-nttps://github.com/deepseek-ai/DeepSeek-nttps://github.com/deepseek-ai/DeepSeek-V3/blob/main/DeeSeek. V3.pdfV3/blob/main/DeepSeek_V3.pdfV3/blob/main/DeepSeek_V3.pdf</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> f26a37a7510a65d0 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">these everyday scenarios.paragraph parsing, and table parsing) are designedElement-level. For fine-grained parsing capabil-for full-page document image parsing, while theities, we first demonstrate Dolphin's formula recog-nition in Figure 10, where we evaluate three typeslatter two (text spotting and text box query) enableadditional capabilities for flexible text recognitionof formulas: inline formulas, single-line block for-tasks. Additionally, our Dolphin can also serve asmulas, and multi-line block formulas. The resultsa formula recognition expert model using the textshow that Dolphin can accurately parse formulas paragraph parsing prompt. of varying complexity and layout formats. In Stage 2, tables are processed with a dedicatedWe further evaluate Dolphin's table parsing abil- table-specific prompt for structured HTML parsing,ity in Figure 11, where we test the model on awhile all other elements are treated as text para-challenging case containing hundreds of cells. Asgraphs and parsed using a unified prompt. This di-</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ab32a3c35af015d0 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">15Input ImageReading Order & LayoutMarkdown / Spans6h58d6:586:587oro。4 titleXDeepSeek-V3正式发布DeepSeek-V3 正式发布uh度水索DeeSeekDeepSeek-V3正式发布原创深度求索 DeepSeek224年12月26日19817京2546人2024年12月26日19:17北京62548人原创深度求索DeepSeek2024年12月26日19:17北京02548人今天，我们全新系列模型 DeepSeek-V3 首个版本今天，我们全新系列模型DeeSeek-V3首个版本上线并同步开源。上线并同步开源。今天，我们全新系列模型DeepSeek-V3首个版本上线并同步开源。登官网chatdepsek.com即可与最新版 V3</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ca857bafef8ffff4 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">images from HTML (left), LaTeX (middle), and Markdown (right) sources. Bottom: corresponding paragraph-level annotations visualized with colored regions.PromptTask Parse the reading order of this document. Page-level Layout Analysis Read text in the image.Text Paragraph/Formula ParsingTable Parsing Parse the table in the image. Detect and recognize all the text lines in the image.Text SpottingText Box Query Read the text in the image within the specified box [x1,y1,x2,y2].Table 6: Different types of prompts used in Dolphin for document parsing tasks.instruction-based framework. Specifically, given aincluding text paragraphs, tables, and formulas. Training Loss. Following standard practice in training sample, we randomly select an applicableautoregressive language models, we optimize Dol-phin using the cross-entropy loss between the pre-task from the above five tasks based on its availableannotations. This selection is used to constructdicted token distributions and ground truth ones.question-answer pairs. For instance, given a page</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 9aa578dbc0d443c9 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content"> pages with dense formulas, triple-column Englishstructures found in documents.academic papers, and double-column Chinese pa-pers with tables. The results demonstrate that Dol-phin can effectively handle documents with differ- Note that in Stage 1 (page-level layout analysis),we intentionally avoid treating formulas as inde-ent languages, layouts, and element types, main- pendent elements. This design choice allows Stagetaining high parsing quality.2 (element-level parsing) to leverage broader con- Furthermore, we showcase Dolphin's versatilitytextual information when recognizing mathemati-cal expressions, as formulas are often semanticallyin other text-rich scenarios through Figure 9, wherewe test the model on mobile phone screenshots,connected with their surrounding text.shopping receipts, and webpage captures. TheseHeterogeneous Anchor Prompting. We sum-results indicate that Dolphin can accurately capturemarize the prompts used in Dolphin in Table 6. Theboth the structural layout and textual content infirst three prompts (page-level layout analysis, text</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 0d366d515fe9e899 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">16 Inline formula imageis normalized by ≥=1 ≥k=1 Amlqk = 1. Here, we use normalized coordinates pPa. E [0, 1]2 foris normalized by $\sum_{l=1}^{L} \sum_{k=1}^{K} A_{m 1 q k}=1$. Here, we use Parsing results normalized coordinates $\hat{boldsymbol{p}}_{q}\in[0,1]{2}$ for Rendered imageh1Block formula imaget-√at∞oV1 - Qt-1- 02qo(αt-1Ct,Co)Qt-1C0+1√1-αt$$q_{\sigma}\left(boldsymbol{x}_{t-1}\mid \boldsymbol{x}_{t},\boldsymbol{x}_{0}\right)=\mathcal{N}\left(\sqrt{\alpha_{t-1}}</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> ea3ce50075395d69 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">shown, Dolphin successfully handles this large-chotomous design distinguishes structured HTMLscale structured table with precise content recogni-content from plain text, while also providing robust-ness against potential element misclassification, as tion and layout preservation.parsing accuracy remains high regardless of ele-Element DesignBment type classification errors. In this section, we elaborate on Dolphin's sup-Training DetailsC ported element types and element-specific parsing strategies through heterogeneous prompting.In this section, we provide more details about Dol-phin's training process, including multi-task train-ing strategy, model initialization, and other imple-Element Types. Our Dolphin supports 15 dif-ferent types of elements commonly found in doc-ument images. Table 5 provides a comprehensiveoverview of these elements, covering various com-adopt a dynamic task selection strategy for ourmentation considerations. Instruction Tuning. During training phase, we ponents from headers to specialized content blocks.13SENTSASFigure 7: Examples of synthetic training data generated from different source formats. Top: rendered document</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 00cd74bf26d25a44 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">\boldsymbol(x}_{0}+>sqrt (1-alpha_{t-1}-sigma_{t}^{2)} \cdot \frac {boldsymbol {x}_{t}- Parsing results\sqrt{alpha_{t}\boldsymbol{x}_{0}}{\sqrt{1-\alpha_{t}}, \sigma_(t}~{2}$$\boldsymbol (I\right).Vatco Rendered image q(ct-1|Ct,o)1√1-Qt=V[E[>2N(xθ)f(x)]+E[N(y;)-g(y)2]]E[VgL(0t)10t]Block formula image[(N(x;)-f(x)（x)dx+Ja(N(y;)-(y)²v（y)dy]=V=V[(V2N(x;θt)-f(x))²(x)dx+a(N(y;)-g(y)²v2(y)dy]=VJ(N(;0t))</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> bccb8aaa3cefb6d5 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">FT39.739.239.740.238.940.537.140.635.338.135.336.9337.238.938.337.4SFPCT37.737.543.644.3 43.6 45.544.244.142.844.140.2 43.4 42.7 42.4 43.843.143.4MPT42.939.6SPPCT40.240.640.941.741.941.741.641.040.639.241.441.438.441.341.240.9MPT42.741.943.142.342.140.842.639.440.241.7FT33.633.4333.534.133.433.333.433.433.633.633.433.533.5SP8.9PCT13.1</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 807b6c24453bb099 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">\begin{array}{r1}{\mathbb{E}[\nabla_\theta }\mathcal{L}（\theta_t}）|\theta _{t}]}＆{=\nabla_{\theta }\left[\frac {1}{M}\sum _{i=1}{M}\mathbb{E}\left[（\nabla ~{2}\mathcal{N}（\mathbf{x}_{i};\theta_t})- f（\mathbf{x}_{i}））~{2}\right]+\frac{1}{N}\sum_{j=1}~{N}\mathbb{E}\left[（\mathcal{N}（\mathbf{y}_{j};\theta_{t}）-g（\mathbf{y}_{j}）)){2}\right]\right]}\\&{=\nabla_{\theta }\left[\frac {1 }{M}\sum_{i=1 }^</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> f7fc16a5fc1dc9b9 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">MPT43.544.342.943.442.2FT86.243.442.943.339.243.537.741.7SP2.344.9PCTMPT47.147.647.947.147.147.341.441.241.540.742.641.440.841.240.240.640.741.440.541.741.041.1FTSP43.947.545.150.547.948.641.843.741.345.945.342.647.645.145.4PCT48.150.249.349.748.248.6MPT50.752.7553.152.255.453.853.150.251.046.251.550.449.153.052.351.746.246.847.653.048.549.6</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> a8537f5c606f072e | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">{M}\int _{\Omega}(\nabla ^{2 }mathcal {N}(\mathbf {x};\theta_{t})-f Parsing results （\mathbf{x}）){2}\nu_{1}（\mathbf{x}）\, d\mathbf {x}+\frac{1}{N}\sum _{j= 1 }~{N}\int _{\partial\Omega }（\mathcal {N}（\mathbf {y};\theta{t}）-g（\mathbf {y}））~{2}\nu_{2}（\mathbf {y}）\,d\mathbf{y}\right]}\&{=\nabla _{\theta }\left[\int _{\Omega }（\nabla ~{2}mathcal {N}（\mathbf {x};\theta_{t})-f（\mathbf {x}））~{2}\nu_{1}（\mathbf{x}）\, d\mathbf{x}+\int</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 60163bfbd599121b | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">Figure 10: Visualization of Dolphin's formula parsing results. From top to bottom, we show three formula types:inline formula, single-line block formula, and multi-line block formula. For each case, we visualize the complete parsing pipeline: input formula image (top), LaTeX parsing output (middle), and rendered formula (bottom). Theseresults demonstrate Dolphin's capability to accurately parse formulas of varying complexity.17BGDERUTHTRURZHAvg.AIENESFRHsWVI63332303363323333163264128256MethodRUAvgShotBCESFRHI33.3338383.3.SP34.137.535.436.436.537.637.936.136.436.035.934.735.035.536.736.6PCT35.335.736.233.884.835.35.34.6MPT</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> 03e668395df02335 | 
                    <strong>Document:</strong> 2505.14059v1.pdf
                </div>
                <div class="chunk-content">37.038.537.838.138.638.138.737.238.536.537.137.637.337.935.737.633.334.133.533.7 33.2 33.5333 33.833.6 33.5 34.033.3FT33.533.333.733.5SP36.637.938.***************.336.238.934.337.************.0PCT34.***************.940.640.537.939.936.537.236.934.737.138.0MPT43.242.542.842.840.83.21.334.1 34.1 34.5 34.0 34.3 33.7 34.0</div>
                <div class="chunk-meta">
            
                </div>
            </div>
            
            </div>
        </body>
        </html>
        