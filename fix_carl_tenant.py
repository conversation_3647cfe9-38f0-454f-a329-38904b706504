#!/usr/bin/env python3
"""
Fix <PERSON>'s tenant setup - this fixes the "Tenant not found!" error
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import LLMService, TenantLLMService
from api.db.services.file_service import FileService
from api.db import FileType, UserTenantRole
from api.utils import get_uuid
from api import settings


def check_carl_tenant_setup():
    """Check <PERSON>'s current tenant setup."""
    
    email = "Carl<PERSON><EMAIL>"
    
    print("🔍 CHECKING CARL'S TENANT SETUP")
    print("=" * 40)
    
    # Find user
    users = UserService.query(email=email)
    if not users:
        print(f"❌ User not found: {email}")
        return None
    
    user = users[0]
    user_id = user.id
    print(f"✅ User found: {user.nickname} (ID: {user_id})")
    
    # Check tenant
    tenants = TenantService.query(id=user_id)
    if tenants:
        tenant = tenants[0]
        print(f"✅ Tenant found: {tenant.name}")
    else:
        print(f"❌ No tenant found for user")
        return user
    
    # Check user-tenant relationship
    user_tenants = UserTenantService.query(user_id=user_id, tenant_id=user_id)
    if user_tenants:
        ut = user_tenants[0]
        print(f"✅ User-tenant relationship found: {ut.role}")
    else:
        print(f"❌ No user-tenant relationship found")
        return user
    
    # Check LLM configurations
    llm_configs = TenantLLMService.query(tenant_id=user_id)
    print(f"ℹ️  LLM configurations: {len(llm_configs)} found")
    
    # Check file system
    files = FileService.query(tenant_id=user_id, name="/")
    if files:
        print(f"✅ Root folder found")
    else:
        print(f"❌ No root folder found")
        return user
    
    print("✅ All tenant components exist!")
    return None  # No fix needed


def fix_carl_tenant():
    """Fix Carl's tenant setup completely."""
    
    email = "<EMAIL>"
    
    print(f"\n🔧 FIXING TENANT SETUP FOR: {email}")
    print("=" * 50)
    
    # Find user
    users = UserService.query(email=email)
    if not users:
        print(f"❌ User not found!")
        return False
    
    user = users[0]
    user_id = user.id
    nickname = user.nickname
    print(f"✅ User found: {nickname}")
    
    try:
        # 1. Create/Update Tenant
        tenants = TenantService.query(id=user_id)
        if not tenants:
            print("📝 Creating tenant...")

            # Use default parser_ids if settings.PARSERS is None
            default_parsers = "naive:General,qa:Q&A,resume:Resume,manual:Manual,table:Table,paper:Paper,book:Book,laws:Laws,presentation:Presentation,picture:Picture,one:One,audio:Audio,email:Email,tag:Tag"
            parser_ids = settings.PARSERS if settings.PARSERS else default_parsers

            tenant = {
                "id": user_id,
                "name": nickname + "'s Kingdom",
                "llm_id": settings.CHAT_MDL or "deepseek-chat",
                "embd_id": settings.EMBEDDING_MDL or "BAAI/bge-large-zh-v1.5",
                "asr_id": settings.ASR_MDL or "paraformer-zh",
                "parser_ids": parser_ids,
                "img2txt_id": settings.IMAGE2TEXT_MDL or "qwen2-vl-7b-instruct",
                "rerank_id": settings.RERANK_MDL or "BAAI/bge-reranker-v2-m3",
            }
            TenantService.insert(**tenant)
            print("✅ Tenant created")
        else:
            print("✅ Tenant already exists")
        
        # 2. Create/Update User-Tenant relationship
        user_tenants = UserTenantService.query(user_id=user_id, tenant_id=user_id)
        if not user_tenants:
            print("📝 Creating user-tenant relationship...")
            usr_tenant = {
                "tenant_id": user_id,
                "user_id": user_id,
                "invited_by": user_id,
                "role": UserTenantRole.OWNER,
            }
            UserTenantService.insert(**usr_tenant)
            print("✅ User-tenant relationship created")
        else:
            print("✅ User-tenant relationship already exists")
        
        # 3. Create LLM configurations
        existing_llms = TenantLLMService.query(tenant_id=user_id)
        if not existing_llms:
            print("📝 Creating LLM configurations...")
            tenant_llm = []
            
            # Add factory LLMs
            for llm in LLMService.query(fid=settings.LLM_FACTORY):
                tenant_llm.append({
                    "tenant_id": user_id,
                    "llm_factory": settings.LLM_FACTORY,
                    "llm_name": llm.llm_name,
                    "model_type": llm.model_type,
                    "api_key": settings.API_KEY,
                    "api_base": settings.LLM_BASE_URL,
                    "max_tokens": llm.max_tokens if llm.max_tokens else 8192,
                })
            
            # Add builtin embedding models
            if settings.LIGHTEN != 1:
                for buildin_embedding_model in settings.BUILTIN_EMBEDDING_MODELS:
                    mdlnm, fid = TenantLLMService.split_model_name_and_factory(buildin_embedding_model)
                    tenant_llm.append({
                        "tenant_id": user_id,
                        "llm_factory": fid,
                        "llm_name": mdlnm,
                        "model_type": "embedding",
                        "api_key": "",
                        "api_base": "",
                        "max_tokens": 1024 if buildin_embedding_model == "BAAI/bge-large-zh-v1.5@BAAI" else 512,
                    })
            
            if tenant_llm:
                TenantLLMService.insert_many(tenant_llm)
                print(f"✅ {len(tenant_llm)} LLM configurations created")
            else:
                print("ℹ️  No LLM configurations to create")
        else:
            print(f"✅ LLM configurations already exist ({len(existing_llms)} found)")
        
        # 4. Create root folder
        files = FileService.query(tenant_id=user_id, name="/")
        if not files:
            print("📝 Creating root folder...")
            file_id = get_uuid()
            file = {
                "id": file_id,
                "parent_id": file_id,
                "tenant_id": user_id,
                "created_by": user_id,
                "name": "/",
                "type": FileType.FOLDER.value,
                "size": 0,
                "location": "",
            }
            FileService.insert(file)
            print("✅ Root folder created")
        else:
            print("✅ Root folder already exists")
        
        print("\n🎉 TENANT SETUP COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing tenant: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_tenant_access():
    """Test if Carl can now access tenant info."""
    
    email = "<EMAIL>"
    
    print(f"\n🧪 TESTING TENANT ACCESS")
    print("=" * 30)
    
    # Find user
    users = UserService.query(email=email)
    if not users:
        print(f"❌ User not found!")
        return False
    
    user = users[0]
    user_id = user.id
    
    # Test tenant info retrieval (this is what fails with "Tenant not found!")
    try:
        tenant_info = TenantService.get_info_by(user_id)
        if tenant_info:
            print(f"✅ Tenant info retrieved: {len(tenant_info)} records")
            for info in tenant_info:
                print(f"   - Tenant: {info.get('name', 'Unknown')}")
                print(f"   - Role: {info.get('role', 'Unknown')}")
            return True
        else:
            print(f"❌ No tenant info found")
            return False
    except Exception as e:
        print(f"❌ Error getting tenant info: {e}")
        return False


def main():
    """Main function."""
    print("RAGFlow Tenant Fix for Carl")
    print("=" * 35)
    
    # Check current setup
    needs_fix = check_carl_tenant_setup()
    
    if needs_fix:
        # Fix the setup
        success = fix_carl_tenant()
        
        if success:
            # Test access
            access_works = test_tenant_access()
            
            print(f"\n" + "=" * 50)
            print("SUMMARY:")
            print(f"✅ Tenant setup: FIXED")
            print(f"{'✅' if access_works else '❌'} Tenant access: {'WORKS' if access_works else 'BROKEN'}")
            
            if access_works:
                print("\n🎉 SUCCESS! Carl can now use RAGFlow!")
                print("The 'Tenant not found!' error should be gone.")
            else:
                print("\n❌ Still having issues with tenant access.")
        else:
            print("\n❌ Failed to fix tenant setup.")
    else:
        # Test access anyway
        access_works = test_tenant_access()
        
        print(f"\n" + "=" * 50)
        print("SUMMARY:")
        print(f"✅ Tenant setup: ALREADY OK")
        print(f"{'✅' if access_works else '❌'} Tenant access: {'WORKS' if access_works else 'BROKEN'}")
        
        if access_works:
            print("\n🤔 Tenant setup looks good. The error might be elsewhere.")
        else:
            print("\n❌ Tenant access still broken despite setup looking OK.")
    
    print(f"\n" + "=" * 50)
    print("Fix completed.")


if __name__ == "__main__":
    main()
