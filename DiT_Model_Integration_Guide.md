# DiT Model Integration Guide for RAGFlow

## Overview
This document explains how to integrate the DiT (Document Image Transformer) model into RAGFlow for layout recognition, replacing the default YOLO-based model. The integration uses an API-based approach to call the DiT model service.


## Files Modified

### 1. `deepdoc/vision/layout_recognizer.py`
**Changes Made**:
- Added `CustomDiTLayoutRecognizer` class 
- Implemented API-based layout detection
- Implemented label mapping between DiT and RAGFlow
- Override `__call__` method completely to handle string class names
- Integrated OCR adjustment post-processing



### 2. `deepdoc/vision/__init__.py`
**Changes Made** (Line 24):
```python
# Original line was commented out:
#from .layout_recognizer import LayoutRecognizer4YOLOv10 as LayoutRecognizer

# Replaced with:
from .layout_recognizer import CustomDiTLayoutRecognizer as LayoutRecognizer
```
**Why This Change**:
- Makes DiT model the default layout recognizer throughout RAGFlow
- Maintains compatibility with existing code that imports `LayoutRecognizer`
- All imports of `LayoutRecognizer` in other files (like `pdf_parser.py`) automatically use DiT

### 3. `deepdoc/parser/pdf_parser.py`
**Changes Made** (around lines 1180-1191):

**Problem**: DiT model may return bounding boxes where coordinates are not properly ordered (left > right or top > bottom), causing PIL Image.crop() to fail.

**Solution**: Added coordinate sorting before image cropping:

```python
# OLD CODE (commented out):
# imgs.append(
#     self.page_images[pns[0]].crop((left * ZM, top * ZM,
#                                    right * ZM, min(
#                                        bottom, self.page_images[pns[0]].size[1])
#                                    ))
# )

# NEW CODE:
x0, x1 = sorted((left * ZM, right * ZM))
y0, y1 = sorted((top * ZM, min(bottom, self.page_images[pns[0]].size[1])))
imgs.append(self.page_images[pns[0]].crop((x0, y0, x1, y1)))
```

**Why These Changes Are Essential**:
- **Coordinate Validation**: Ensures bounding box coordinates are properly ordered (x0 < x1, y0 < y1)
- **Prevents Crop Errors**: DiT model may return invalid bounding boxes that cause PIL Image.crop() to fail
- **Table Processing Fix**: Ensures table regions are cropped correctly for TableStructureRecognizer




## Testing and Validation

### 1. End-to-End Testing
**Command**: 
```bash
python main_ragapi.py
```

**What to Monitor**:
- Docker logs: `docker logs -f ragflow-server`
- Layout detection results in processed documents
- Table extraction accuracy
- Processing time performance
