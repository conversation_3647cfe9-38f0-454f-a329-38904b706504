# RAGFlow User Creation Tools

## 📁 Final Working Files

### 1. `create_carl_user.py` - Create New Users ✅
**Purpose:** Create new user accounts directly in the database

**Usage:**
```bash
# Copy to container
docker cp create_carl_user.py CONTAINER_ID:/ragflow/create_carl_user.py

# Run it
docker exec -it CONTAINER_ID python3 /ragflow/create_carl_user.py
```

**To create different users:** Edit the `main()` function:
```python
def main():
    # MODIFY THESE VALUES:
    email = "<EMAIL>"     # ← Change this
    nickname = "Username"          # ← Change this
    password = "UserPassword123"   # ← Change this
```

### 2. `fix_carl_rsa_password.py` - Fix RSA Password Issues ✅
**Purpose:** Fix password issues when users can't login due to RSA encryption problems

**Usage:**
```bash
# Copy to container
docker cp fix_carl_rsa_password.py CONTAINER_ID:/ragflow/fix_carl_rsa_password.py

# Run it
docker exec -it CONTAINER_ID python3 /ragflow/fix_carl_rsa_password.py
```

**When to use:** When user exists but gets "Email and password do not match!" error

### 3. `fix_carl_tenant.py` - Fix Tenant Setup Issues ✅
**Purpose:** Fix "Tenant not found!" errors after successful login

**Usage:**
```bash
# Copy to container
docker cp fix_carl_tenant.py CONTAINER_ID:/ragflow/fix_carl_tenant.py

# Run it
docker exec -it CONTAINER_ID python3 /ragflow/fix_carl_tenant.py
```

**When to use:** When user can login but gets "Tenant not found!" error

## 🚀 Complete Setup Process

### Step 1: Create Carl's Account
```bash
docker cp create_carl_user.py 5c26685c2fac:/ragflow/create_carl_user.py
docker exec -it 5c26685c2fac python3 /ragflow/create_carl_user.py
```

### Step 2: Fix RSA Password (if login fails with "Email and password do not match!")
```bash
docker cp fix_carl_rsa_password.py 5c26685c2fac:/ragflow/fix_carl_rsa_password.py
docker exec -it 5c26685c2fac python3 /ragflow/fix_carl_rsa_password.py
```

### Step 3: Fix Tenant Setup (if you get "Tenant not found!" after login)
```bash
docker cp fix_carl_tenant.py 5c26685c2fac:/ragflow/fix_carl_tenant.py
docker exec -it 5c26685c2fac python3 /ragflow/fix_carl_tenant.py
```

## ✅ Carl's Account Status

**Email:** `<EMAIL>`
**Password:** `Carl20250812`
**Type:** Regular user (not admin)
**Status:** ✅ **WORKING** - Can login and use RAGFlow fully

## 🔧 How It Works

1. **`create_carl_user.py`** uses the same `user_register()` function as normal registration
2. **`fix_carl_rsa_password.py`** fixes RSA encryption/decryption issues with website login
3. **`fix_carl_tenant.py`** ensures complete tenant setup (Tenant, UserTenant, LLM configs, File system)
4. All scripts create/fix complete user setups identical to normal registration

## 🐛 Common Issues & Solutions

| Error | Script to Use | When to Use |
|-------|---------------|-------------|
| User doesn't exist | `create_carl_user.py` | Create new user account |
| "Email and password do not match!" | `fix_carl_rsa_password.py` | RSA encryption issue |
| "Tenant not found!" | `fix_carl_tenant.py` | Missing tenant setup |

## 📝 Notes

- No admin account needed
- No container rebuild needed
- Works when registration is disabled
- Handles RSA encryption/decryption properly
- Creates complete user setup identical to normal registration

## 🎉 Success Summary

Carl's account is now **fully working**:
- ✅ User account created
- ✅ RSA password encryption fixed
- ✅ Tenant setup completed
- ✅ Can login to RAGFlow website
- ✅ Can use all RAGFlow features

## 🗑️ Removed Files

These debugging files were removed as they're no longer needed:
- ❌ `create_admin_user.py` - Replaced by create_carl_user.py
- ❌ `admin_user_management.py` - Too complex, not needed
- ❌ `test_user_creation.py` - Debugging only
- ❌ `simple_test.py` - Debugging only
- ❌ `debug_carl_login.py` - Debugging only
- ❌ `test_rsa_login.py` - Debugging only
- ❌ `fix_carl_password.py` - Replaced by fix_carl_rsa_password.py
