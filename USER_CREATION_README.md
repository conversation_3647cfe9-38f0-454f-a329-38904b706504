# RAGFlow User Creation Tool


### `create_ragflow_user.py` - Complete User Creation 
**Purpose:** Creates complete RAGFlow user accounts with proper RSA password handling and tenant setup


**Usage:**
```bash
# Copy to container
docker cp create_ragflow_user.py CONTAINER_ID:/ragflow/create_ragflow_user.py

# Run it
docker exec -it CONTAINER_ID python3 /ragflow/create_ragflow_user.py
```

**To create different users:** Edit the `main()` function:
```python
def main():
    # MODIFY THESE VALUES TO CREATE ANY USER:
    email = "<EMAIL>"        # ← Change this
    nickname = "Username"             # ← Change this
    password = "UserPassword123"      # ← Change this
```

##  Simple One-Step Process

### Create Any User Account (Complete Setup)
```bash
# Copy the script to container
docker cp create_ragflow_user.py 5c26685c2fac:/ragflow/create_ragflow_user.py

# Run it to create complete user account
docker exec -it 5c26685c2fac python3 /ragflow/create_ragflow_user.py
```


##  Example Usage

To create <PERSON>'s account:
```python
# In main() function of create_ragflow_user.py:
email = "<PERSON>.<PERSON>@viridiengroup.com"
nickname = "Carl"
password = "Carl20250812"
```

To create a different user:
```python
# In main() function of create_ragflow_user.py:
email = "<EMAIL>"
nickname = "John"
password = "SecurePassword123"
```
