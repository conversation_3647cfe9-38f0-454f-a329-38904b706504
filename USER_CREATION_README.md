# RAGFlow User Creation Tools

## 📁 Essential Files

### 1. `create_carl_user.py` - Create New Users
**Purpose:** Create new user accounts directly in the database

**Usage:**
```bash
# Copy to container
docker cp create_carl_user.py CONTAINER_ID:/ragflow/create_carl_user.py

# Run it
docker exec -it CONTAINER_ID python3 /ragflow/create_carl_user.py
```

**To create different users:** Edit the `main()` function:
```python
def main():
    # MODIFY THESE VALUES:
    email = "<EMAIL>"     # ← Change this
    nickname = "Username"          # ← Change this  
    password = "UserPassword123"   # ← Change this
```

### 2. `fix_carl_password.py` - Fix Existing User Passwords
**Purpose:** Fix password issues for existing users who can't login

**Usage:**
```bash
# Copy to container
docker cp fix_carl_password.py CONTAINER_ID:/ragflow/fix_carl_password.py

# Run it
docker exec -it CONTAINER_ID python3 /ragflow/fix_carl_password.py
```

**To fix different users:** Edit the script:
```python
def fix_carl_password():
    email = "<EMAIL>"     # ← Change this
    password = "UserPassword123"   # ← Change this
```

## 🚀 Quick Steps

### Create Carl's Account:
```bash
docker cp create_carl_user.py 5c26685c2fac:/ragflow/create_carl_user.py
docker exec -it 5c26685c2fac python3 /ragflow/create_carl_user.py
```

### Fix Carl's Password (if login fails):
```bash
docker cp fix_carl_password.py 5c26685c2fac:/ragflow/fix_carl_password.py
docker exec -it 5c26685c2fac python3 /ragflow/fix_carl_password.py
```

## ✅ Default User Created

**Email:** `<EMAIL>`
**Password:** `Carl20250812`
**Type:** Regular user (not admin)

## 🔧 How It Works

1. **`create_carl_user.py`** uses the same `user_register()` function as normal registration
2. **`fix_carl_password.py`** properly hashes passwords using `generate_password_hash()`
3. Both scripts create complete user setups (User, Tenant, LLM configs, File system)
4. Users can login immediately after creation

## 📝 Notes

- No admin account needed
- No container rebuild needed  
- Works when registration is disabled
- Creates complete user setup identical to normal registration
