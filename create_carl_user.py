#!/usr/bin/env python3
"""
Quick script to create <PERSON>'s user account
"""

import sys
import os
import base64
import uuid

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import LLMService, TenantLLMService
from api.db import User<PERSON>enantRole
from api import settings


def encode_to_base64(input_string):
    """Encode string to base64."""
    base64_encoded = base64.b64encode(input_string.encode('utf-8'))
    return base64_encoded.decode('utf-8')


def create_carl():
    """Create <PERSON>'s user account."""
    
    email = "<EMAIL>"
    nickname = "Carl"
    password = "Carl20250812"
    
    print(f"Creating user account for {email}...")
    
    # Check if user already exists
    existing_users = UserService.query(email=email)
    if existing_users:
        print(f"User with email {email} already exists!")
        return False
    
    user_id = uuid.uuid1().hex
    user_info = {
        "id": user_id,
        "password": encode_to_base64(password),
        "nickname": nickname,
        "is_superuser": False,  # Regular user, not admin
        "email": email,
        "creator": "system",
        "status": "1",
    }
    
    tenant = {
        "id": user_id,
        "name": nickname + "'s Kingdom",
        "llm_id": settings.CHAT_MDL,
        "embd_id": settings.EMBEDDING_MDL,
        "asr_id": settings.ASR_MDL,
        "parser_ids": settings.PARSERS,
        "img2txt_id": settings.IMAGE2TEXT_MDL
    }
    
    usr_tenant = {
        "tenant_id": user_id,
        "user_id": user_id,
        "invited_by": user_id,
        "role": UserTenantRole.OWNER
    }
    
    # Create tenant LLM configurations
    tenant_llm = []
    try:
        for llm in LLMService.query(fid=settings.LLM_FACTORY):
            tenant_llm.append({
                "tenant_id": user_id,
                "llm_factory": settings.LLM_FACTORY,
                "llm_name": llm.llm_name,
                "model_type": llm.model_type,
                "api_key": settings.API_KEY,
                "api_base": settings.LLM_BASE_URL
            })
    except Exception as e:
        print(f"Warning: Could not set up LLM configurations: {e}")
    
    try:
        # Create user
        if not UserService.save(**user_info):
            print(f"Failed to create user {email}.")
            return False
        
        # Create tenant
        TenantService.insert(**tenant)
        
        # Create user-tenant relationship
        UserTenantService.insert(**usr_tenant)
        
        # Create tenant LLM configurations if available
        if tenant_llm:
            TenantLLMService.insert_many(tenant_llm)
        
        print("✅ SUCCESS! User created successfully!")
        print("=" * 40)
        print(f"Email: {email}")
        print(f"Nickname: {nickname}")
        print(f"Password: {password}")
        print("=" * 40)
        print("Carl can now login to RAGFlow with these credentials.")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Failed to create user: {e}")
        return False


if __name__ == "__main__":
    create_carl()
