#!/usr/bin/env python3
"""
Quick script to create <PERSON>'s user account
"""

import sys
import os
import base64

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService
from api.utils import get_uuid, get_format_time
from api.apps.user_app import user_register


def encode_to_base64(input_string):
    """Encode string to base64."""
    base64_encoded = base64.b64encode(input_string.encode('utf-8'))
    return base64_encoded.decode('utf-8')


def create_user_account(email, nickname, password):
    """Create a user account using the same process as normal registration."""

    print(f"Creating user account for {email}...")

    # Check if user already exists
    existing_users = UserService.query(email=email)
    if existing_users:
        print(f"User with email {email} already exists!")
        return False

    # Use the same user registration process as the working registration
    user_id = get_uuid()
    user_dict = {
        "access_token": get_uuid(),
        "email": email,
        "nickname": nickname,
        "password": encode_to_base64(password),
        "login_channel": "admin_created",
        "last_login_time": get_format_time(),
        "is_superuser": False,
    }

    try:
        # Use the exact same user_register function that works in normal registration
        users = user_register(user_id, user_dict)
        if not users:
            print(f"❌ ERROR: Failed to register {email}.")
            return False
        if len(users) > 1:
            print(f"❌ ERROR: Same email: {email} exists!")
            return False

        user = users[0]
        print("✅ SUCCESS! User created successfully!")
        print("=" * 40)
        print(f"Email: {email}")
        print(f"Nickname: {nickname}")
        print(f"Password: {password}")
        print(f"User ID: {user.id}")
        print("=" * 40)
        print("Carl can now login to RAGFlow with these credentials.")
        return True

    except Exception as e:
        print(f"❌ ERROR: Failed to create user: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_carl():
    """Create Carl's specific account."""
    return create_user_account(
        email="<EMAIL>",
        nickname="Carl",
        password="Carl20250812"
    )


def main():
    """Main function - you can easily modify user details here."""

    # MODIFY THESE VALUES TO CREATE DIFFERENT USERS:
    email = "<EMAIL>"
    nickname = "Carl"
    password = "Carl20250812"

    print("RAGFlow User Creator")
    print("=" * 25)
    print(f"Creating user: {email}")

    success = create_user_account(email, nickname, password)

    if success:
        print("\n🎉 User creation completed successfully!")
    else:
        print("\n❌ User creation failed!")


if __name__ == "__main__":
    main()
