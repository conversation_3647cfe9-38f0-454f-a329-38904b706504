#!/usr/bin/env python3
"""
Extract parsed document chunks from Elasticsearch for visualization.
This script connects to Elasticsearch, retrieves document chunks,
and saves them in a format suitable for visualization.
""" 
# id (from Elasticsearch document ID)
#  content or content_with_weight
# docnm_kwd or doc_name
# document_id
# positions
# important_keywords
# available
# image_id
# _score (Elasticsearch relevance score)

import argparse
import json
import os
from datetime import datetime
from elasticsearch import Elasticsearch


def connect_to_elasticsearch(host, port, username=None, password=None, api_key=None):
    """Connect to Elasticsearch instance."""
    # Ensure host has a scheme (http:// or https://)
    if not host.startswith(('http://', 'https://')):
        host = f"http://{host}"
    
    # Create the full URL
    url = f"{host}:{port}"
    
    if api_key:
        return Elasticsearch(
            url,
            api_key=api_key
        )
    elif username and password:
        return Elasticsearch(
            url,
            basic_auth=(username, password)
        )
    else:
        return Elasticsearch(url)


def extract_chunks(es_client, index_name, doc_id=None, query=None, max_chunks=1000):
    """Extract document chunks from Elasticsearch."""
    if doc_id:
        # Search for chunks of a specific document
        search_query = {
            "query": {
                "term": {
                    "doc_id": doc_id
                }
            },
            "size": max_chunks
        }
    elif query:
        # Use custom query
        search_query = query
    else:
        # Get all chunks up to max_chunks
        search_query = {
            "query": {
                "match_all": {}
            },
            "size": max_chunks
        }
    
    response = es_client.search(
        index=index_name,
        body=search_query
    )
    
    # Extract hits
    hits = response["hits"]["hits"]
    total = response["hits"]["total"]["value"] if isinstance(response["hits"]["total"], dict) else response["hits"]["total"]
    
    print(f"Found {total} chunks, retrieved {len(hits)}")
    
    # Extract chunk data
    chunks = []
    for hit in hits:
        chunk = hit["_source"]
        chunk["id"] = hit["_id"]
        chunk["_score"] = hit["_score"]
        chunks.append(chunk)
    
    return chunks


def save_chunks(chunks, output_dir, format_type="json"):
    """Save chunks to file for visualization."""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if format_type == "json":
        # Save as JSON
        output_file = os.path.join(output_dir, f"chunks_{timestamp}.json")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(chunks, f, ensure_ascii=False, indent=2)
        print(f"Saved {len(chunks)} chunks to {output_file}")
    
    elif format_type == "html":
        # Create a simple HTML visualization
        output_file = os.path.join(output_dir, f"chunks_viz_{timestamp}.html")
        
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Document Chunks Visualization</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .chunk { border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
                .chunk-header { background: #f5f5f5; padding: 5px; margin-bottom: 10px; }
                .chunk-content { white-space: pre-wrap; }
                .chunk-meta { color: #666; font-size: 0.9em; margin-top: 10px; }
                .highlight { background-color: yellow; }
                .position-box { border: 1px dashed #999; margin: 5px; padding: 3px; display: inline-block; }
            </style>
        </head>
        <body>
            <h1>Document Chunks Visualization</h1>
            <div id="chunks">
        """
        
        for chunk in chunks:
            html_content += f"""
            <div class="chunk">
                <div class="chunk-header">
                    <strong>ID:</strong> {chunk.get('id', 'N/A')} | 
                    <strong>Document:</strong> {chunk.get('docnm_kwd', chunk.get('doc_name', 'N/A'))}
                </div>
                <div class="chunk-content">{chunk.get('content_with_weight', chunk.get('content', 'No content'))}</div>
                <div class="chunk-meta">
            """
            
            # Add positions if available
            if 'positions' in chunk and chunk['positions']:
                html_content += "<div><strong>Positions:</strong> "
                for pos in chunk['positions']:
                    if isinstance(pos, list) and len(pos) >= 5:
                        html_content += f'<span class="position-box">Page {pos[0]}: ({pos[1]},{pos[2]}) - ({pos[3]},{pos[4]})</span>'
                html_content += "</div>"
            
            # Add keywords if available
            if 'important_keywords' in chunk and chunk['important_keywords']:
                keywords = chunk['important_keywords']
                if isinstance(keywords, str):
                    try:
                        keywords = json.loads(keywords)
                    except:
                        keywords = [keywords]
                html_content += f"<div><strong>Keywords:</strong> {', '.join(keywords) if isinstance(keywords, list) else keywords}</div>"
            
            html_content += """
                </div>
            </div>
            """
        
        html_content += """
            </div>
        </body>
        </html>
        """
        
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(html_content)
        print(f"Saved HTML visualization to {output_file}")


def main():
    parser = argparse.ArgumentParser(description="Extract document chunks from Elasticsearch")
    parser.add_argument("--host", default="eoaagmld007", help="Elasticsearch host")
    parser.add_argument("--port", default=1200, type=int, help="Elasticsearch port")
    parser.add_argument("--index", required=True, help="Elasticsearch index name")
    parser.add_argument("--username", help="Elasticsearch username")
    parser.add_argument("--password", help="Elasticsearch password")
    parser.add_argument("--api-key", help="Elasticsearch API key")
    parser.add_argument("--doc-id", help="Filter by document ID")
    parser.add_argument("--output-dir", default="./es_chunks", help="Output directory")
    parser.add_argument("--format", choices=["json", "html"], default="html", help="Output format")
    parser.add_argument("--max-chunks", type=int, default=1000, help="Maximum number of chunks to retrieve")
    
    args = parser.parse_args()
    
    # Connect to Elasticsearch
    es_client = connect_to_elasticsearch(
        args.host, 
        args.port, 
        args.username, 
        args.password, 
        args.api_key
    )
    
    # Extract chunks
    chunks = extract_chunks(
        es_client, 
        args.index, 
        args.doc_id, 
        None, 
        args.max_chunks
    )
    
    # Save chunks
    save_chunks(chunks, args.output_dir, args.format)


if __name__ == "__main__":
    main()
