#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix <PERSON>'s password so he can login
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService
from werkzeug.security import generate_password_hash


def fix_carl_password():
    """Fix <PERSON>'s password so he can login."""
    
    email = "<PERSON><PERSON>@viridiengroup.com"
    password = "Carl20250812"  # Plain text password
    
    print(f"Fixing password for {email}...")
    
    # Check if user exists
    users = UserService.query(email=email)
    if not users:
        print(f"❌ User with email {email} not found!")
        return False
    
    user = users[0]
    print(f"✅ User found: {user.nickname} (ID: {user.id})")
    
    # Update password with proper hashing
    try:
        # Generate proper password hash
        password_hash = generate_password_hash(password)
        
        # Update user password directly
        update_dict = {
            "password": password_hash
        }
        
        UserService.update_user(user.id, update_dict)
        
        print("✅ Password updated successfully!")
        print("=" * 40)
        print(f"Email: {email}")
        print(f"Password: {password}")
        print("=" * 40)
        print("<PERSON> can now login to RAGFlow!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating password: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_login():
    """Test if Carl can now login with the fixed password."""
    
    email = "<EMAIL>"
    password = "Carl20250812"
    
    print(f"\n🔐 Testing login for {email}...")
    
    try:
        # This is the same method used in the login endpoint
        user = UserService.query_user(email, password)
        if user:
            print("✅ Login test SUCCESSFUL!")
            print(f"   User: {user.nickname}")
            print(f"   Email: {user.email}")
            print(f"   Status: {user.status}")
            return True
        else:
            print("❌ Login test FAILED!")
            print("   Password verification failed")
            return False
    except Exception as e:
        print(f"❌ Login test ERROR: {e}")
        return False


def main():
    """Main function."""
    print("RAGFlow Password Fix for Carl")
    print("=" * 35)
    
    # Fix the password
    success = fix_carl_password()
    
    if success:
        # Test the login
        login_works = test_login()
        
        print("\n" + "=" * 35)
        print("SUMMARY:")
        print(f"✅ Password updated: YES")
        print(f"{'✅' if login_works else '❌'} Login works: {'YES' if login_works else 'NO'}")
        
        if login_works:
            print("\n🎉 SUCCESS! Carl can now login to RAGFlow!")
            print("\nLogin credentials:")
            print("Email: <EMAIL>")
            print("Password: Carl20250812")
        else:
            print("\n❌ Something is still wrong. Carl cannot login.")
    else:
        print("\n❌ Failed to fix password.")
    
    print("\n" + "=" * 35)
    print("Fix completed.")


if __name__ == "__main__":
    main()
