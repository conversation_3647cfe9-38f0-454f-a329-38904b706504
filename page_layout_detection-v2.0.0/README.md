# Page_Layout_Detection
Page Layout Detection detects 11 classes as follows:
- Caption
- Figure
- Footer
- Forms
- Header
- List
- Logo
- Table
- Text
- Title
- TOC

## Example Data
Example data to be used for inference can be found in:
- Images and csv file
`\\eoaasvm004\ml\miskanda\data\doc_layout\dit\test_data\rm_test`

Example data to be used for training and validation can be found in:
- Json files
`\\eoaasvm004\ml\miskanda\data\doc_layout\dit\annotations\2025_02_18`
- Corresponding images
`\\eoaasvm004\ml\miskanda\data\doc_layout\dit\images\all_images`


## data_utils

Contains coco_utils.py which contains functions that may be useful for when exporting annotated data from LabelStudio and preparing for use for Layout Detection.

## dit_aug

Contains the code for Layout Detection using DiT. Instructions are contained within the folder.




