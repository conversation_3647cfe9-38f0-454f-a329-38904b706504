import copy
import json
import random
from typing import List, Optional, Union

from PIL import Image, ImageDraw, ImageFont
from pycocotools.coco import COCO

# Define a list of 14 predefined colors
PREDEFINED_COLORS = [
    (255, 0, 0),  # <PERSON>
    (0, 255, 0),  # <PERSON>
    (0, 128, 0),  # <PERSON>
    (0, 0, 255),  # <PERSON>
    (0, 0, 128),  # <PERSON>
    (128, 0, 128),  # <PERSON>
    (128, 0, 0),  # <PERSON><PERSON>
    (0, 128, 128),  # <PERSON><PERSON>
    (255, 255, 0),  # <PERSON>
    (0, 255, 255),  # <PERSON><PERSON>
    (255, 0, 255),  # <PERSON><PERSON><PERSON>
    (128, 128, 0),  # <PERSON>
    (192, 192, 192),  # <PERSON>
    (128, 128, 128),  # <PERSON>
]


def adjust_category_ids(annotation_path: str, output_path: str) -> None:
    """
    Adjusts the category IDs in a COCO annotation file to ensure they start from 0 and are contiguous.

    This function will:
    1. Load a COCO annotation JSON file.
    2. Map the original category IDs to new IDs starting from 0.
    3. Update the category IDs in both the "categories" and "annotations" sections.
    4. Save the modified annotation data to a new JSON file.

    Args:
        annotation_path (str): The path to the input COCO annotation JSON file.
        output_path (str): The path where the modified COCO annotation JSON file will be saved.

    Example:
        adjust_category_ids('input_coco_annotations.json', 'output_coco_annotations.json')
    """
    # Load the original COCO annotation file
    with open(annotation_path, "r") as f:
        coco_data = json.load(f)

    # Step 1: Create a new ID mapping for categories
    id_mapping = {
        category["id"]: new_id
        for new_id, category in enumerate(coco_data["categories"])
    }

    # Step 2: Update the category IDs in the "categories" section
    for category in coco_data["categories"]:
        category["id"] = id_mapping[category["id"]]

    # Step 3: Update the category IDs in the "annotations" section
    for annotation in coco_data["annotations"]:
        annotation["category_id"] = id_mapping[annotation["category_id"]]

    # Step 4: Save the modified annotations to a new file
    with open(output_path, "w") as f:
        json.dump(coco_data, f, indent=4)

    print(f"Updated annotation file saved to {output_path}")


def split_coco_annotation(
    annotation_path: str,
    train_annotation_path: str,
    valid_annotation_path: str,
    train_ratio: float = 0.9,
    seed: int = 42,
) -> None:
    """
    Splits a COCO annotation file into training and validation datasets based on a specified ratio.

    This function will:
    1. Load a COCO annotation JSON file.
    2. Split the images into training and validation sets according to the specified ratio.
    3. Split the annotations corresponding to the images into training and validation sets.
    4. Save the training and validation datasets as separate JSON files.

    Args:
        annotation_path (str): The path to the input COCO annotation JSON file.
        train_annotation_path (str): The path where the training dataset JSON file will be saved.
        valid_annotation_path (str): The path where the validation dataset JSON file will be saved.
        train_ratio (float, optional): The ratio of the data to be used for training. Default is 0.9 (90% training, 10% validation).
        seed (int, optional): The random seed used for reproducibility when shuffling the images. Default is 42.

    Example:
        split_coco_annotation('input_coco_annotations.json', 'train_coco_annotations.json', 'valid_coco_annotations.json')
    """
    # Set random seed for reproducibility
    random.seed(seed)

    # Load the original COCO annotation file
    with open(annotation_path, "r") as f:
        coco_data = json.load(f)

    # Get all image IDs
    image_ids = [img["id"] for img in coco_data["images"]]

    # Shuffle and split image IDs
    random.shuffle(image_ids)
    split_index = int(len(image_ids) * train_ratio)
    train_image_ids = set(image_ids[:split_index])
    valid_image_ids = set(image_ids[split_index:])

    # Split images
    train_images = [img for img in coco_data["images"] if img["id"] in train_image_ids]
    valid_images = [img for img in coco_data["images"] if img["id"] in valid_image_ids]

    # Split annotations
    train_annotations = [
        ann for ann in coco_data["annotations"] if ann["image_id"] in train_image_ids
    ]
    valid_annotations = [
        ann for ann in coco_data["annotations"] if ann["image_id"] in valid_image_ids
    ]

    # Prepare train and valid COCO data
    coco_train = {
        "info": coco_data.get("info", {}),
        "licenses": coco_data.get("licenses", []),
        "categories": coco_data["categories"],
        "images": train_images,
        "annotations": train_annotations,
    }

    coco_valid = {
        "info": coco_data.get("info", {}),
        "licenses": coco_data.get("licenses", []),
        "categories": coco_data["categories"],
        "images": valid_images,
        "annotations": valid_annotations,
    }

    # Save the train and valid annotations to separate JSON files
    with open(train_annotation_path, "w") as f:
        json.dump(coco_train, f, indent=4)

    with open(valid_annotation_path, "w") as f:
        json.dump(coco_valid, f, indent=4)

    print(f"COCO train annotations saved to {train_annotation_path}")
    print(f"COCO valid annotations saved to {valid_annotation_path}")


def convert_label_studio_to_coco_format(
    input_path: str, output_path: str, prefix: str = ""
) -> None:
    """
    Converts Label Studio JSON annotations to COCO format.

    Args:
        input_path (str): Path to the Label Studio JSON file.
        output_path (str): Path to save the converted COCO format JSON file.
        prefix (str, optional): Prefix to add to the file names in the COCO images section. Default is an empty string.

    Example:
        convert_label_studio_to_coco_format('input.json', 'output.json', '/datahub/label-studio/files/well_log_data_GOM/source/')
    """
    # Load the Label Studio data
    label_studio_data = json.load(open(input_path))

    # Initialize COCO format dictionaries
    coco_format = {"images": [], "annotations": [], "categories": []}

    # Mapping for categories
    category_map = {}
    category_id = 1

    # Helper function to add a category
    def get_category_id(label_name: str) -> int:
        nonlocal category_id
        if label_name not in category_map:
            cat_id = len(coco_format["categories"]) + 1
            category_map[label_name] = cat_id
            coco_format["categories"].append(
                {"id": cat_id, "name": label_name, "supercategory": "none"}
            )
            category_id += 1
        return category_map[label_name]

    # Initialize unique IDs
    image_id_counter = 1
    annotation_id_counter = 1

    # Process the input data
    for item in label_studio_data:
        try:
            original_width = item["label"][0]["original_width"]
            original_height = item["label"][0]["original_height"]

            # Image entry
            image_info = {
                "id": image_id_counter,
                "file_name": item["image"],  # prefix + item["image"].split("/")[-1],
                "height": original_height,
                "width": original_width,
            }
            coco_format["images"].append(image_info)

            # Annotations entries
            for annotation in item["label"]:
                cur_cat = get_category_id(annotation["rectanglelabels"][0])

                # Convert normalized values to pixel values
                x = annotation["x"] * original_width / 100
                y = annotation["y"] * original_height / 100
                width = annotation["width"] * original_width / 100
                height = annotation["height"] * original_height / 100

                # COCO format expects coordinates to be in [x, y, width, height]
                bbox = [x, y, width, height]

                annotation_info = {
                    "id": annotation_id_counter,
                    "image_id": image_id_counter,
                    "category_id": cur_cat,
                    "bbox": bbox,
                    "area": width * height,
                    "iscrowd": 0,
                }
                coco_format["annotations"].append(annotation_info)
                annotation_id_counter += 1

            image_id_counter += 1
        except Exception as e:
            print(e)
    # Save to a file
    with open(output_path, "w") as f:
        json.dump(coco_format, f, indent=4)

    print("Conversion to COCO format completed.")


def draw_detections(
    coco_or_path: Union[str, COCO], image_id: int, font_size=24
) -> Image.Image:
    """
    Draws detections on the image for the given image_id using the COCO object or file path.

    This function:
    1. Loads the COCO annotations from a file or uses an existing COCO object.
    2. Retrieves the image associated with the provided image_id.
    3. Draws bounding boxes and category names on the image for all annotations.
    4. Assigns predefined colors to each category for consistent visualization.

    Args:
        coco_or_path (Union[str, COCO]): Either a path to a COCO annotation JSON file or an existing COCO object.
        image_id (int): ID of the image for which detections are drawn.

    Returns:
        PIL.Image: The image with detections drawn on it.

    Example:
        img = draw_detections('path/to/coco_annotations.json', image_id=42)
        img.show()
    """
    # Load the COCO object if a path is provided
    if isinstance(coco_or_path, str):
        coco = COCO(coco_or_path)
    else:
        coco = coco_or_path

    # Load image information and the image itself
    img_info = coco.loadImgs(image_id)[0]
    img = Image.open(img_info["file_name"])

    # Initialize drawing context
    draw = ImageDraw.Draw(img)

    # Retrieve all annotations for the given image_id
    annotations = coco.loadAnns(coco.getAnnIds(imgIds=image_id))

    # Assign a unique color to each category based on predefined colors
    category_colors = {}
    for ann in annotations:
        category_id = ann["category_id"]
        if category_id not in category_colors:
            # Use modulo operation to ensure colors cycle if there are more categories than predefined colors
            color_index = (category_id - 1) % len(PREDEFINED_COLORS)
            category_colors[category_id] = PREDEFINED_COLORS[color_index]

        # Extract bounding box and category
        bbox = ann["bbox"]
        category_name = coco.loadCats(category_id)[0]["name"]

        # Convert bbox to (x1, y1, x2, y2) format
        x1, y1, width, height = bbox
        x2, y2 = x1 + width, y1 + height

        # Draw the bounding box
        draw.rectangle([x1, y1, x2, y2], outline=category_colors[category_id], width=3)

        # Draw the category name
        font = ImageFont.load_default(
            size=font_size
        )  # Default font can be replaced with a custom font
        draw.text((x1, y1), category_name, fill=category_colors[category_id], font=font)

    return img


def merge_coco_annotations(
    ann1: str, ann2: str, output_path: Optional[str] = None
) -> dict:
    """
    Merges two COCO annotation files into a single COCO format annotation file.

    This function:
    1. Loads two COCO annotation JSON files.
    2. Combines the categories from both files, ensuring unique and contiguous category IDs.
    3. Merges the images from both files, ensuring unique and contiguous image IDs.
    4. Updates the annotations to use the new combined category and image IDs.
    5. Optionally saves the merged annotations to a file if an output_path is provided.
    6. Returns a dictionary representing the merged COCO annotations.

    Args:
        ann1 (str): Path to the first COCO annotation JSON file.
        ann2 (str): Path to the second COCO annotation JSON file.
        output_path (Optional[str]): Path to save the merged COCO annotation JSON file. If not provided, the merged data will not be saved to a file.

    Returns:
        dict: A dictionary containing the merged COCO annotations with updated categories, images, and annotations.

    Example:
        merged_coco = merge_coco_annotations('ann1.json', 'ann2.json', 'merged_annotations.json')
        # If output_path is provided, the merged annotations will be saved to the specified file.
    """
    # Load the JSON files
    with open(ann1, "r") as f:
        coco1 = json.load(f)

    with open(ann2, "r") as f:
        coco2 = json.load(f)

    # Step 1: Create a combined list of unique categories
    combined_categories = []
    category_name_to_id = {}
    combined_category_id = 1

    def add_categories(coco, mapping):
        """
        Adds categories from a COCO dataset to the combined list, ensuring unique IDs.

        Args:
            coco (dict): COCO format annotation dictionary.
            mapping (dict): Dictionary to map original category IDs to combined category IDs.
        """
        nonlocal combined_category_id
        for cat in coco["categories"]:
            cat["name"] = cat["name"].lower()
            if cat["name"] not in category_name_to_id:
                category_name_to_id[cat["name"]] = combined_category_id
                combined_categories.append(
                    {
                        "id": combined_category_id,
                        "name": cat["name"],
                        "supercategory": cat.get("supercategory", ""),
                    }
                )
                combined_category_id += 1
            # Map original category ID to combined category ID
            mapping[cat["id"]] = category_name_to_id[cat["name"]]

    # Create mappings from original category IDs to combined category IDs
    ann1_to_combined = {}
    ann2_to_combined = {}

    add_categories(coco1, ann1_to_combined)
    add_categories(coco2, ann2_to_combined)

    # Step 2: Merge images
    merged_coco = {
        "images": [],
        "annotations": [],
        "categories": combined_categories,
    }

    max_image_id = 0
    image_mapping1 = {}  # Mapping for coco1 images
    image_mapping2 = {}  # Mapping for coco2 images

    def merge_images(coco, mapping):
        """
        Merges images from a COCO dataset, ensuring unique IDs.

        Args:
            coco (dict): COCO format annotation dictionary.
            mapping (dict): Dictionary to map original image IDs to new unique IDs.
        """
        nonlocal max_image_id
        for img in coco["images"]:
            max_image_id += 1
            mapping[img["id"]] = max_image_id
            new_img = copy.deepcopy(img)
            new_img["id"] = max_image_id
            merged_coco["images"].append(new_img)

    merge_images(coco1, image_mapping1)
    merge_images(coco2, image_mapping2)

    # Step 3: Merge annotations
    max_ann_id = 0

    def merge_annotations(coco, category_mapping, image_mapping):
        """
        Merges annotations from a COCO dataset, updating category and image IDs.

        Args:
            coco (dict): COCO format annotation dictionary.
            category_mapping (dict): Dictionary to map original category IDs to combined category IDs.
            image_mapping (dict): Dictionary to map original image IDs to new unique IDs.
        """
        nonlocal max_ann_id
        for ann in coco["annotations"]:
            max_ann_id += 1
            new_ann = copy.deepcopy(ann)
            new_ann["id"] = max_ann_id
            new_ann["category_id"] = category_mapping[ann["category_id"]]
            new_ann["image_id"] = image_mapping[ann["image_id"]]
            merged_coco["annotations"].append(new_ann)

    merge_annotations(coco1, ann1_to_combined, image_mapping1)
    merge_annotations(coco2, ann2_to_combined, image_mapping2)

    # Save the merged annotations to a file if output_path is provided
    if output_path:
        with open(output_path, "w") as f:
            json.dump(merged_coco, f, indent=4)
        print(f"Merged annotation file saved to {output_path}")

    return merged_coco


def remove_categories_from_coco(
    coco_annotation_file: str,
    categories_to_remove: List[str],
    output_file: Optional[str] = None,
) -> dict:
    """
    Removes specified categories from a COCO annotation file and updates the annotations accordingly.

    This function:
    1. Loads a COCO annotation JSON file.
    2. Removes categories that are specified in `categories_to_remove`.
    3. Updates the remaining categories with new IDs, and adjusts annotations to match the new category IDs.
    4. Optionally saves the updated annotations to a file if `output_file` is provided.
    5. Returns the updated COCO annotations as a dictionary.

    Args:
        coco_annotation_file (str): Path to the input COCO annotation JSON file.
        categories_to_remove (List[str]): List of category names to remove from the COCO annotations.
        output_file (Optional[str]): Path to save the updated COCO annotation JSON file. If not provided, the updated data will not be saved to a file.

    Returns:
        dict: The updated COCO annotations with the specified categories removed.

    Example:
        updated_coco = remove_categories_from_coco('annotations.json', ['category1', 'category2'], 'updated_annotations.json')
        # If output_file is provided, the updated annotations will be saved to the specified file.
    """
    # Load the COCO annotation file
    with open(coco_annotation_file, "r") as file:
        coco_data = json.load(file)

    # Step 1: Remove specified categories from the "categories" section
    remaining_categories = [
        cat
        for cat in coco_data["categories"]
        if cat["name"] not in categories_to_remove
    ]

    # Create a mapping from old category_id to new category_id
    old_to_new_category_id = {
        cat["id"]: new_id for new_id, cat in enumerate(remaining_categories)
    }

    # Update the "categories" with new IDs
    for i, cat in enumerate(remaining_categories):
        cat["id"] = i

    # Step 2: Remove annotations corresponding to the removed categories
    remaining_annotations = []
    for annotation in coco_data["annotations"]:
        if annotation["category_id"] in old_to_new_category_id:
            annotation["category_id"] = old_to_new_category_id[
                annotation["category_id"]
            ]
            remaining_annotations.append(annotation)

    # Step 3: Update COCO data
    coco_data["categories"] = remaining_categories
    coco_data["annotations"] = remaining_annotations

    # Step 4: Save the updated COCO annotation file if output_file is provided
    if output_file:
        with open(output_file, "w") as file:
            json.dump(coco_data, file, indent=4)
        print(f"Updated COCO annotation file saved to {output_file}")

    return coco_data


def add_segmentation_to_coco(coco_json_path, output_json_path):
    """
    Adds segmentation to annotations in a COCO dataset using bounding box data.

    Args:
        coco_json_path (str): Path to the input COCO JSON file.
        output_json_path (str): Path to save the updated COCO JSON file.

    Returns:
        None
    """
    # Load COCO JSON
    with open(coco_json_path, "r") as f:
        coco_data = json.load(f)

    # Iterate over annotations and add segmentation
    for annotation in coco_data["annotations"]:
        bbox = annotation["bbox"]
        x, y, width, height = bbox
        segmentation = [x, y, x + width, y, x + width, y + height, x, y + height]
        annotation["segmentation"] = [segmentation]

    # Save updated JSON
    with open(output_json_path, "w") as f:
        json.dump(coco_data, f, indent=4)
    print(f"Updated COCO JSON saved to {output_json_path}")
