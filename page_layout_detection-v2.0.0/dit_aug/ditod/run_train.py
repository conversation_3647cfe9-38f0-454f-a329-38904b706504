from detectron2.checkpoint import Detection<PERSON>heckpointer
from detectron2.data.datasets import register_coco_instances
from detectron2.utils import comm
from ditod import MyTrainer, setup


def run_train(args):
    if comm.is_main_process():
        print("load config")
    cfg = setup(args)

    register_coco_instances(
        "publaynet_train", {}, cfg.TRAIN.DATA.COCO_FILE, cfg.TRAIN.DATA.IMAGE_DIR
    )

    register_coco_instances(
        "publaynet_val", {}, cfg.TEST.DATA.COCO_FILE, cfg.TEST.DATA.IMAGE_DIR
    )

    if args.eval_only:
        model = MyTrainer.build_model(cfg)
        DetectionCheckpointer(model, save_dir=cfg.OUTPUT_DIR).resume_or_load(
            cfg.MODEL.WEIGHTS, resume=args.resume
        )
        res = MyTrainer.test(cfg, model)
        return res

    trainer = MyTrainer(cfg)
    trainer.resume_or_load(resume=args.resume)
    return trainer.train()
