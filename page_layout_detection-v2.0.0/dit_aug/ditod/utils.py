# Imports
import torch
import numpy as np
import os
import json

import asyncio
import aiohttp
from typing import List, Dict, Optional, Union, Any, Tuple

from PIL import Image, ImageDraw, ImageFont
from torch.utils.data import Dataset
from ditod import add_vit_config
from detectron2.config import get_cfg
from detectron2.engine import default_setup
from detectron2.utils import comm
from datetime import datetime


def setup(args):
    """
    Create configs and perform basic setups.
    """
    cfg = get_cfg()
    add_vit_config(cfg)
    cfg.set_new_allowed(True)
    cfg.merge_from_file(args.config_file)
    cfg.merge_from_list(args.opts)
    
    # Create output dir
    if comm.is_main_process():
        print("Creating timestamped output directory for saving results", cfg.OUTPUT_DIR)
    now = datetime.now() # dd/mm/YY H:M:S
    dt_string = now.strftime("%d%m%Y_%H%M")
    cfg.OUTPUT_DIR = cfg.OUTPUT_DIR + f"/{dt_string}"
    os.makedirs(cfg.OUTPUT_DIR, exist_ok=True)

    cfg.freeze()
    default_setup(cfg, args)
    
    return cfg


def resize_shortest_edge(img_PIL, min_size=800, max_size=1333):
    img = np.array(img_PIL)
    h, w = np.array(img).shape[:2]

    size = min_size * 1.0
    scale = size / min(h, w)
    if h < w:
        newh, neww = size, scale * w
    else:
        newh, neww = scale * h, size
    if max(newh, neww) > max_size:
        scale = max_size * 1.0 / max(newh, neww)
        newh = newh * scale
        neww = neww * scale
    new_w = int(neww + 0.5)
    new_h = int(newh + 0.5)

    if len(img.shape) > 2 and img.shape[2] == 1:
        pil_image = Image.fromarray(img[:, :, 0], mode="L")
    else:
        pil_image = Image.fromarray(img)
        pil_image = pil_image.resize((new_w, new_h), 2)
        ret = np.asarray(pil_image)
        if len(img.shape) > 2 and img.shape[2] == 1:
            ret = np.expand_dims(ret, -1)

    return (
        ret.astype("float32").transpose(2, 0, 1),
        (w, h),
        (new_w, new_h),
    )
    
    
def visualize(outputs, save_dir=None, ret_images=False, index_to_name_json="ditod/index_to_name.json"):
    colors = [
        (0,0,0),
        (224, 29, 211), (217, 129, 242), (50, 12, 96),
        (150, 55, 0), (0, 190, 238), (155, 219, 231),
        (30, 87, 78), (148, 156, 28), (175, 112, 49),
        (183, 144, 62), (127, 118, 255), (37, 134, 173),
    ]
     
    results = []
    with open(index_to_name_json) as f:
        label_map = json.load(f)
    name2id = {v:k for k, v in label_map.items()}
    for output in outputs:
        if output:
            # Load the image
            image_path = output["file_path"]
            image = Image.open(image_path).convert("RGB")
            size = min(image.size)
            font_size = max(12, int(0.015*size))
            line_width = max(2, int(size*0.002))
            draw = ImageDraw.Draw(image)
            font = ImageFont.load_default(size=font_size)
            
            pred_bboxes = output["boxes"]
            pred_classes = output["classes"]
            pred_scores = output["scores"]
            
            # Draw ground truth boxes
            for bbox, class_name, score in list(zip(pred_bboxes, pred_classes, pred_scores)):
                class_id = int(name2id[class_name])
                draw.rectangle(bbox, outline=colors[class_id], width=line_width)
                s = str(round(score,2))
                draw.text((bbox[0], bbox[1]), f"{class_name} {s}", fill=colors[class_id], font=font)
                
            if save_dir:
                fn = image_path.split("/")[-1]
                save_path = os.path.join(save_dir, fn)
                image.save(save_path)
            if ret_images:
                results.append(image)
    return results
        
async def send_request_async(
    session: aiohttp.ClientSession,
    url: str,
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    params: Optional[Dict[str, Any]] = None,
) -> Union[Dict[str, Any], aiohttp.ClientError]:
    """
    Sends an asynchronous HTTP request to the specified URL.
    """
    try:
        if method.upper() == "POST":
            async with session.post(
                url, json=data, headers=headers, params=params
            ) as response:
                response.raise_for_status()  # Raise an error for bad status codes
                return await response.json()
        else:  # Default to GET
            async with session.get(url, headers=headers, params=params) as response:
                response.raise_for_status()  # Raise an error for bad status codes
                return await response.json()
    except Exception as e:
        print(e)
        return e
    
    
    
        
class LayoutDetectionDataset(Dataset):
    """Face Landmarks dataset."""

    def __init__(self, img_paths):
        """
        Arguments:r
            csv_file (string): Path to the csv file with annotations.
            root_dir (string): Directory with all the images.
            transform (callable, optional): Optional transform to be applied
                on a sample.
        """
        self.img_paths = img_paths

    def __len__(self):
        return len(self.img_paths)

    def __getitem__(self, idx):
        img_path = self.img_paths[idx]
        im = Image.open(img_path).convert("RGB")
        im, img_shape, new_img_shape = resize_shortest_edge(im)
        im = torch.as_tensor(im)
        return {
            "file_name": img_path,
            "image": torch.squeeze(im),
            "width": new_img_shape[0],
            "height": new_img_shape[1],
            "original_width": img_shape[0],
            "original_height": img_shape[1]
        }

