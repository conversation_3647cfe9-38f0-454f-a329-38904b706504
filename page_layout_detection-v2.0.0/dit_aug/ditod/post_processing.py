import numpy as np

from PIL import Image

# from concurrent.futures import <PERSON><PERSON>oolExecutor, ThreadPoolExecutor


def resize(outputs, original_size, resized_size):
    """Post-process model outputs including scaling and filtering"""
    boxes = outputs.pred_boxes
    scores = outputs.scores
    classes = outputs.pred_classes
    masks = outputs.pred_masks

    # Convert to numpy arrays
    boxes = boxes.tensor.detach().cpu().numpy()
    scores = scores.detach().cpu().numpy()
    classes = classes.detach().cpu().numpy()
    masks = masks.detach().cpu().numpy()

    # Scale boxes to original image dimensions
    orig_w, orig_h = original_size
    resized_w, resized_h = resized_size
    scale_x = orig_w / resized_w
    scale_y = orig_h / resized_h

    # Scale factors for inversion (same as before)
    scale_x = orig_w / resized_w
    scale_y = orig_h / resized_h

    # Scale bounding boxes (no changes here)
    pred_boxes_orig = boxes * np.array([scale_x, scale_y, scale_x, scale_y])

    return {
        "boxes": pred_boxes_orig,
        "scores": scores,
        "classes": classes,
    }


def class_thresholding(
    instances,
    label_map,
    threshold=[
        0.25,
        0.31,
        0.63,
        0.23,
        0.27,
        0.30,
        0.84,
        0.44,
        0.37,
        0.47,
        0.86,
    ],
):
    i_result = instances
    boxes = i_result["boxes"] if "boxes" in i_result else []
    scores = i_result["scores"] if "scores" in i_result else []
    pred_classes_indexes = i_result["classes"] if "classes" in i_result else []

    all_indices = []
    for idx, score_threshold in enumerate(threshold):
        class_indices = pred_classes_indexes == idx
        score_indices = scores >= score_threshold
        indices = np.array([class_indices, score_indices]).all(axis=0)
        all_indices.append(indices)
    all_indices = np.array(all_indices).any(axis=0)

    scores = [float(s) for s in scores[all_indices]]
    bboxes = boxes[all_indices]
    bboxes = [list(b) for b in bboxes]
    classes = pred_classes_indexes[all_indices]
    classes_names = [label_map[str(i)] for i in classes if 0 <= i < len(label_map)]

    return {
        "boxes": bboxes,
        "classes": classes_names,
        "scores": scores,
    }


def get_iou(bbox1, bbox2):
    """
    Calculate the Intersection over Union (IoU) between two bounding boxes.

    Args:
        bbox1 (list): The first bounding box in the format [left, top, right, bottom].
        bbox2 (list): The second bounding box in the format [left, top, right, bottom].

    Returns:
        float: The IoU value between the two bounding boxes, ranging from 0 (no overlap) to 1 (complete overlap).

    Notes:
        The IoU is calculated as the ratio of the intersection area to the union area of the two bounding boxes.
    """

    l1, t1, r1, b1 = bbox1
    l2, t2, r2, b2 = bbox2

    l_inter = max(l1, l2)
    t_inter = max(t1, t2)
    r_inter = min(r1, r2)
    b_inter = min(b1, b2)

    width_inter = max(0, r_inter - l_inter)
    height_inter = max(0, b_inter - t_inter)

    area_inter = width_inter * height_inter

    l1, t1, r1, b1 = bbox1
    l2, t2, r2, b2 = bbox2

    if area_inter > 0:
        area_box1 = (r1 - l1) * (b1 - t1)
        area_box2 = (r2 - l2) * (b2 - t2)

        area_union = area_box1 + area_box2 - area_inter
        iou = area_inter / area_union
    else:
        iou = 0
    return iou


def custom_nms(output, thresholds=[0.2, 0.5]):
    """
    Apply custom Non-Maximum Suppression (NMS) to the given output. Allows two different thresholds for supressing bounding boxes,
    depending on whether boxes are of the same class (see Args).

    Args:
        output (dict): Dictionary containing the model output with keys 'scores', 'classes', and 'boxes'.
        thresholds (list, optional): List of two thresholds for NMS. The first threshold is used for boxes of the same class,
            and the second threshold is used for boxes of different classes. Defaults to [0.2, 0.5].

    Returns:
        dict: The output dictionary with scores, classes, and boxes filtered by NMS.

    Notes:
        The function sorts the boxes by their confidence scores in descending order and then iterates through them.
        For each box, it calculates the IoU with the remaining boxes and suppresses the boxes with IoU greater than the threshold.
    """

    pred_scores = output["scores"]
    pred_classes = output["classes"]
    pred_bboxes = output["boxes"]

    # Sort by confidence score (pred_scores)
    sorted_score_bbox_class = [
        x
        for x in sorted(list(zip(pred_scores, pred_bboxes, pred_classes)), reverse=True)
    ]

    # Loop through sorted bboxes
    idx = 0
    while idx < len(sorted_score_bbox_class):
        _, bbox1, class1 = sorted_score_bbox_class[idx]
        # Loop through bboxes
        for score_bbox_class2 in sorted_score_bbox_class[idx + 1 :]:
            _, bbox2, class2 = score_bbox_class2
            # Calculate IoU
            iou = get_iou(bbox1, bbox2)
            # Supress overlapping boxes above threshold
            threshold = thresholds[0] if class1 == class2 else thresholds[1]
            if iou > threshold:
                sorted_score_bbox_class.remove(score_bbox_class2)
        idx += 1

    scores = []
    bboxes = []
    classes = []
    for score, bbox, cl in sorted_score_bbox_class:
        scores.append(score)
        bboxes.append(bbox)
        classes.append(cl)

    output["scores"] = scores
    output["classes"] = classes
    output["boxes"] = bboxes

    return output


def ocr_adjust_bbox(output, ocr_page_output, image_shape):
    """
    Apply OCR post-processing to the given output by adjusting the bounding boxes to overlap with the OCR results.

    Args:
        output (dict): Dictionary containing the model output with key 'boxes'.
        ocr_output: OCR output object containing the text and bounding box information.
        image_shape (tuple): Shape of the input image in the format (width, height).

    Returns:
        dict: The output dictionary with the adjusted bounding boxes.

    Notes:
        The function iterates through the OCR output and extracts the text and bounding box information.
        It then finds the overlapping OCR boxes for each predicted bounding box and adjusts the predicted box to overlap with the OCR box.
        The overlap is calculated as the ratio of the intersection area to the base area of the two boxes.
        If the overlap is greater than 0.2, the predicted box is adjusted to match the OCR box.
    """

    w, h = image_shape
    pred_bboxes = output["boxes"]
    final_bboxes = []
    ocr_results = []
    for block in ocr_page_output.blocks:
        for line in block.lines:
            for word in line.words:
                bbox = word.geometry  # Get the word bounding box
                text = word.value.strip()  # Get the word text

                x0 = bbox[0][0] * w
                y0 = bbox[0][1] * h
                x1 = bbox[1][0] * w
                y1 = bbox[1][1] * h
                if y1 < y0:
                    temp = y0
                    y0 = y1
                    y1 = temp
                if x1 < x0:
                    temp = x0
                    x0 = x1
                    x1 = temp
                if text:
                    ocr_results.append(
                        {"text": text, "x0": x0, "x1": x1, "top": y0, "bottom": y1}
                    )

    for bbox in pred_bboxes:
        pred_ocr = []
        # Find overlapping ocr boxes
        for ocr in ocr_results:
            x1, y1, x12, y12 = bbox
            w1 = x12 - x1
            h1 = y12 - y1
            x2, y2, w2, h2 = (
                ocr["x0"],
                ocr["top"],
                ocr["x1"] - ocr["x0"],
                ocr["bottom"] - ocr["top"],
            )

            # Calculate the coordinates of the intersection rectangle
            intersection_x1 = max(x1, x2)
            intersection_y1 = max(y1, y2)
            intersection_x2 = min(x1 + w1, x2 + w2)
            intersection_y2 = min(y1 + h1, y2 + h2)

            # Calculate the width and height of the intersection rectangle
            intersection_width = max(0, intersection_x2 - intersection_x1)
            intersection_height = max(0, intersection_y2 - intersection_y1)

            # Calculate the area of the intersection rectangle
            intersection_area = intersection_width * intersection_height

            # Calculate the area of the specified bounding box
            base_area = min(w1 * h1, w2 * h2)

            # Calculate the intersection area as a percentage of the base area
            overlap = intersection_area / base_area
            # print(overlap)
            if overlap > 0.2:
                pred_ocr.append([x2, y2, x2 + w2, y2 + h2])

        # Adjust bboxes to any overlpaping ocr bbox
        if pred_ocr:
            ocr_array = np.array(pred_ocr)
            min_x2, min_y2 = np.min(ocr_array, axis=0)[0], np.min(ocr_array, axis=0)[1]
            max_x2, max_y2 = np.max(ocr_array, axis=0)[2], np.max(ocr_array, axis=0)[3]
            x1_end = x1 + w1
            y1_end = y1 + h1
            if x1 > min_x2:
                x1 = min_x2
            if y1 > min_y2:
                y1 = min_y2
            if x1_end < max_x2:
                x1_end = max_x2
            if y1_end < max_y2:
                y1_end = max_y2

            bbox = [x1, y1, x1_end, y1_end]

        bbox = [float(b) for b in bbox]
        final_bboxes.append(bbox)

    output["boxes"] = final_bboxes 
    return output

def apply_ocr_adjustment(images, output_batch, detector):
    images_arr = []
    sizes = []
    for image in images:
        sizes.append(image.size)
        images_arr.append(np.array(image))
        
    ocr_outputs = detector(images_arr)
    
    res = []
    for idx, output in enumerate(output_batch):
        ocr_page_output = ocr_outputs.pages[idx]
        image_shape = sizes[idx]
        output = ocr_adjust_bbox(output, ocr_page_output, image_shape)
        res.append(output)
    return res

# def apply_nms_ocr(output, ocr_page_output, image_shape, thresholds):
#     output = custom_nms(output, thresholds)
#     output = ocr_adjust_bbox(output, ocr_page_output, image_shape)
#     return output

# def apply_nms_ocr_batch(input_batch, output_batch, thresholds=[0.2, 0.5], device="cuda"):
#     # Get a batch of ocr output
#     detector = ocr_predictor(pretrained=True).to(device)

#     images = [np.array(Image.open(single["file_name"]).convert("RGB")) for single in input_batch]
#     ocr_outputs = detector(images)

#     res = []
#     for idx, output in enumerate(output_batch):
#         ocr_page_output = ocr_outputs.pages[idx]
#         image_shape = (input_batch[idx]["original_width"], input_batch[idx]["original_height"])
#         output = apply_nms_ocr(output, ocr_page_output, image_shape, thresholds)
#         res.append(output)
#     return res


# def apply_th_nms_batch(outputs, thresholds=[0.2, 0.5]):
#     # with ThreadPoolExecutor() as executor:
#     #     outputs = list(executor.map(postprocess_optimised_thredsold, outputs))
#     with ThreadPoolExecutor() as executor:
#         outputs = list(executor.map(apply_th_nms, outputs, [thresholds]*len(outputs)))
#     return outputs
