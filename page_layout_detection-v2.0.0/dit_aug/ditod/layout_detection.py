# Imports
import io
import base64
import requests
import json

from PIL import Image
from torch import no_grad
from torch.utils.data import DataLoader
import sys

from detectron2.config import get_cfg
from detectron2.checkpoint import Detection<PERSON>heckpointer

from detectron2.modeling import build_model
from tqdm import tqdm

from doctr.models import ocr_predictor

from ditod.utils import *
from ditod import post_processing


class APIModelAsync:
    def __init__(self, url, device):
        print("Instantiating API model")
        self.url = url
        self.ocr_predictor = ocr_predictor(pretrained=True).to(device)

    def encode_image(self, image_rgb):
        # Encode image
        buf = io.BytesIO()
        image_rgb.save(buf, format="JPEG")
        img_bytes = buf.getvalue()
        encoded_image = base64.b64encode(img_bytes).decode("utf-8")

        return encoded_image

    async def process_image_async(
        self,
        session: aiohttp.ClientSession,
        image_path: str,
        semaphore: asyncio.Semaphore,
    ):
        async with semaphore:
            # try:
            image_rgb = Image.open(image_path).convert("RGB")
            encoded_image = self.encode_image(image_rgb)
            post_data = {"inputs": [encoded_image]}
            try:
                response = await asyncio.wait_for(
                    send_request_async(
                        session, self.url, method="POST", data=post_data
                    ),
                    timeout=1000,
                )

                output = response["outputs"]
                output["file_path"] = image_path

                output = post_processing.apply_ocr_adjustment(
                    [image_rgb], [output], self.ocr_predictor
                )[0]
                return output
            except Exception as e:
                print(f"Error processing {image_path}: {e}")
            # print("RESPONSE", response)

    async def inference_async(self, input_batch, batch_size):
        semaphore = asyncio.Semaphore(batch_size)
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.process_image_async(session, input_single["file_name"], semaphore)
                for input_single in input_batch
            ]
            results = await asyncio.gather(*tasks)
            return results

    def inference(self, input_batch):
        output_batch = []
        # API only accepts one image at a time so loop through batched input
        for input_single in input_batch:
            try:
                image_rgb = Image.open(input_single["file_name"]).convert("RGB")
                encoded_image = self.encode_image(image_rgb)
                post_data = {"inputs": [encoded_image]}
                output = requests.post(
                    self.url,
                    json=post_data,
                    headers={"Content-Type": "application/json"},
                    timeout=1000,
                )
                output = output.json()
                output = output["outputs"]
                output["file_path"] = input_single["file_name"]
                output_batch.append(output)
                output = post_processing.apply_ocr_adjustment(
                    [image_rgb], [output], self.ocr_predictor
                )
                output_batch.append(*output)
            except Exception as e:
                file_name = input_single["file_name"]
                print(f"Error processing {file_name}: {e}")

        return output_batch

    def __call__(self, input_batch, batch_size, no_async=False):
        if no_async:
            return self.inference(input_batch)
        else:
            return asyncio.run(self.inference_async(input_batch, batch_size))


class LocalModel:
    def __init__(
        self,
        cfg_path,
        model_path,
        device,
        index_to_name_json="ditod/index_to_name.json",
    ):
        print("Instantiating local model")
        # Setup model configs
        cfg = get_cfg()
        cfg.set_new_allowed(True)
        cfg.merge_from_file(cfg_path)

        with open(index_to_name_json) as f:
            self.label_map = json.load(f)

        if device:
            cfg.MODEL.DEVICE = device
        if model_path:
            cfg.MODEL.WEIGHTS = model_path
        cfg.freeze()

        self.model = build_model(cfg)
        DetectionCheckpointer(self.model).load(cfg.MODEL.WEIGHTS)
        self.model.eval()
        self.ocr_predictor = ocr_predictor(pretrained=True).to(device)

    def __call__(self, input_batch, **kwargs):
        with no_grad():
            output_batch = self.model(input_batch)
        post_output = []
        for input_single, output_single in zip(input_batch, output_batch):
            output_single = output_single["instances"]
            orig_w = input_single["original_width"]
            orig_h = input_single["original_height"]
            w = input_single["width"]
            h = input_single["height"]
            output_single = post_processing.resize(
                output_single, (orig_w, orig_h), (w, h)
            )
            output_single = post_processing.class_thresholding(
                output_single, self.label_map
            )
            output_single["file_path"] = input_single["file_name"]
            output_single = post_processing.custom_nms(output_single)
            output_single = post_processing.apply_ocr_adjustment(
                    [Image.open(input_single["file_name"].convert("RGB"))], [output_single], self.ocr_predictor
                )
            post_output.append(*output_single)
        return post_output


class LayoutDetection:
    def __init__(
        self,
        url="http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0",
        cfg_path="/ml/miskanda/data/doc_layout/dit/ckpt/maskrcnn_mix/21022025_1456/config.yaml",
        model_path="/ml/miskanda/data/doc_layout/dit/ckpt/maskrcnn_mix/21022025_1456/model_best.pth",
        run_locally=False,
        device="cuda",
    ):

        self.url = url
        if run_locally:
            self.model = LocalModel(
                cfg_path=cfg_path, model_path=model_path, device=device
            )
        else:
            self.model = APIModelAsync(url=url, device=device)

    def set_local_model(self, cfg_path, model_path, device):
        self.model = LocalModel(cfg_path=cfg_path, model_path=model_path, device=device)

    def set_api_model(self, url, device):
        self.model = APIModelAsync(url=url, device=device)

    def predict(self, image_paths, batch_size=2, num_workers=2, no_async=False):

        dataset = LayoutDetectionDataset(image_paths)

        def _collate_fn(data):
            return data

        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            num_workers=num_workers,
            shuffle=False,
            collate_fn=_collate_fn,
        )

        all_outputs = []
        for input_batch in tqdm(dataloader):
            output_batch = self.model(
                input_batch, batch_size=batch_size, no_async=no_async
            )
            all_outputs.extend(output_batch)
        return all_outputs
