import copy
import logging

import numpy as np
import torch

import os

# os.environ["NCCL_BLOCKING_WAIT"] = "1"
# os.environ["NCCL_ASYNC_ERROR_HANDLING"] = "1"
# os.environ["NCCL_DEBUG"] = "INFO"
# os.environ["NCCL_TIMEOUT"] = "600"
import cv2
from detectron2.data import detection_utils as utils
from detectron2.data import transforms as T
from PIL import Image, ImageDraw
from augraphy import *
from multiprocessing import get_context
__all__ = ["DetrDatasetMapper"]
from detectron2.data.transforms import Augmentation

from augraphy import AugraphyPipeline
import numpy as np


class To_RGB(Augmentation):
    """Convert from PIL RGB Image to numpy array BGR image."""
    def __call__(self, image):
        # convert from PIL RGB to BGR
        image_numpy = np.array(image)
        if len(image_numpy.shape)<3:
            return cv2.cvtColor(image_numpy, cv2.COLOR_GRAY2BGR)
        else:
            return image_numpy
    
        

class AugraphyTransform(Augmentation):
    def __init__(self):
        self.ink_phase1 = [
                        Dithering(p=0.5),
                        InkBleed(p=0.5)]
        self.paper_phase1 = [BrightnessTexturize(p=0.2),
                    NoiseTexturize(p=0.2)]
        self.post_phase1 = [
            OneOf([LowLightNoise(p=0.2), 
                SubtleNoise(p=0.2), 
                VoronoiTessellation(p=0.2), 
                DelaunayTessellation(p=0.2), 
                Dithering(p=0.2), 
                DotMatrix(p=0.2), 
                Faxify(p=0.2)])]
        
        self.ink_phase2 = [
                        Dithering(p=0.2),
                        OneOf([LowInkRandomLines(p=0.2), LowInkPeriodicLines(p=0.2), LinesDegradation(p=0.2), InkBleed(p=0.2)]),
                        Letterpress(p=0.2),
                        LightingGradient(p=0.1),
                        
                    ]

        self.paper_phase2 = [
            BrightnessTexturize(p=0.2),
            NoiseTexturize(p=0.2),]

        self.post_phase2 = [
                        OneOf([DirtyRollers(p=0.1), 
                            DirtyDrum(p=0.1, line_concentration=0.05, noise_intensity=0.1, noise_value=(0,5))]),
                        ColorShift(p=0.2),
                        OneOf([Squish(p=0.2), SectionShift(p=0.2)]),
                        ColorShift(p=0.2),
                        OneOf([Squish(p=0.2), SectionShift(p=0.2)]),
                        OneOf([LowLightNoise(p=0.2), 
                            SubtleNoise(p=0.2), 
                            VoronoiTessellation(p=0.2), 
                            DelaunayTessellation(p=0.2), 
                            Dithering(p=0.2), 
                            DotMatrix(p=0.2), 
                            Faxify(p=0.2)])
                        ]
        self.to_rgb = To_RGB()
        
    def __call__(self, image, bounding_boxes):
        """
        Apply AugraphyPipeline to the given image.
        """
        # Augraphy expects a numpy image, ensure input format is valid
        if isinstance(image, np.ndarray):
            if np.random.rand() > 0.5:
                pipeline = AugraphyPipeline(ink_phase=self.ink_phase1, paper_phase=self.paper_phase1, post_phase=self.post_phase1, bounding_boxes=bounding_boxes)
            else:
                pipeline = AugraphyPipeline(ink_phase=self.ink_phase2, paper_phase=self.paper_phase2, post_phase=self.post_phase2, bounding_boxes=bounding_boxes)
            res = pipeline(image)
            image = res[0]
            bbox_augmented = res[3]
            return self.to_rgb(image), bbox_augmented
        else:
            raise ValueError("Expected image to be a numpy array")
        


def build_transform_gen(cfg, is_train):
    """
    Create a list of :class:`TransformGen` from config.
    Returns:
        list[TransformGen]
    """
    if is_train:
        min_size = cfg.INPUT.MIN_SIZE_TRAIN
        max_size = cfg.INPUT.MAX_SIZE_TRAIN
        sample_style = cfg.INPUT.MIN_SIZE_TRAIN_SAMPLING
    else:
        min_size = cfg.INPUT.MIN_SIZE_TEST
        max_size = cfg.INPUT.MAX_SIZE_TEST
        sample_style = "choice"
    if sample_style == "range":
        assert len(min_size) == 2, "more than 2 ({}) min_size(s) are provided for ranges".format(len(min_size))

    logger = logging.getLogger(__name__)
    tfm_gens = []
    if is_train:
        tfm_gens.append(T.RandomApply(T.RandomRotation(angle=[-2,2]), prob=0.2))
        tfm_gens.append(T.RandomFlip(horizontal=True, vertical=False, prob=0.4))
        tfm_gens.append(T.RandomFlip(horizontal=False, vertical=True, prob=0.4))
        
    tfm_gens.append(T.ResizeShortestEdge(min_size, max_size, sample_style))
    if is_train:
        logger.info("TransformGens used in training: " + str(tfm_gens))
    
    
    return tfm_gens
    
class DetrDatasetMapper:
    """
    A callable which takes a dataset dict in Detectron2 Dataset format,
    and map it into a format used by DETR.

    The callable currently does the following:

    1. Read the image from "file_name"
    2. Applies geometric transforms to the image and annotation
    3. Find and applies suitable cropping to the image and annotation
    4. Prepare image and annotation to Tensors
    """

    def __init__(self, cfg, is_train=True):
        if cfg.INPUT.CROP.ENABLED and is_train:
            self.crop_gen = [
                T.ResizeShortestEdge([400, 500, 600], sample_style="choice"),
                T.RandomCrop(cfg.INPUT.CROP.TYPE, cfg.INPUT.CROP.SIZE),
            ]
        else:
            self.crop_gen = None

        self.mask_on = cfg.MODEL.MASK_ON
        self.tfm_gens = build_transform_gen(cfg, is_train)
        logging.getLogger(__name__).info(
            "Full TransformGens used in training: {}, crop: {}".format(str(self.tfm_gens), str(self.crop_gen))
        )
        
        self.img_format = cfg.INPUT.FORMAT
        self.is_train = is_train
        
        self.aug_dirty = AugraphyTransform()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    

    def __call__(self, dataset_dict):
        """
        Args:
            dataset_dict (dict): Metadata of one image, in Detectron2 Dataset format.

        Returns:
            dict: a format that builtin models in detectron2 accept
        """
        dataset_dict = copy.deepcopy(dataset_dict)  # it will be modified by code below
        image = utils.read_image(dataset_dict["file_name"], format=self.img_format)
        
        utils.check_image_size(dataset_dict, image)
        
        try:
        ##################
            # print(image.shape)
            if np.random.rand() > 0.5 and (image.shape[0] *image.shape[1] < 2**25 ) and self.is_train:  
                bounding_boxes = []
                segmentations = []
                for anno in  dataset_dict["annotations"]:
                    l, t, w, h = anno["bbox"]
                    r = l + w
                    b = t + h
                    bounding_boxes.append([l, t, r, b])
                    # print(anno["bbox"])
                    # print(anno["segmentation"])
                    seg = anno["segmentation"]
                    segmentations.append(seg)

                res = self.aug_dirty(image, bounding_boxes)
                # ctx = get_context("spawn")  # Use "spawn" to ensure safe multiprocessing on all platforms
                # with ctx.Pool(1) as pool:
                #     res = pool.apply(self.apply_augraphy, (image,bounding_boxes))
                    
                image = res[0]
                bbox_augmented = res[1]
                
                # pil_im = Image.fromarray(image)
                # draw = ImageDraw.Draw(pil_im)
              
                for bbox, anno in list(zip(bbox_augmented, dataset_dict["annotations"])):
                    l, t, r, b = bbox
                    anno["bbox"] = [l, t, r-l, b-t]
                    anno["segmentation"] = [[l, t, r, t, r, b, l, b]]
                    # draw.rectangle([l,t,r,b], outline="red", width=2)
        except Exception as e:
              print(e)
              dataset_dict = copy.deepcopy(dataset_dict)  # it will be modified by code below
              image = utils.read_image(dataset_dict["file_name"], format=self.img_format)
                
              utils.check_image_size(dataset_dict, image)
        # print(image.shape)
        # fn = dataset_dict['file_name'].split("/")[-1]
        # pil_im.save(f"/ml/shared/osancheti/All_data/Ojasvi/page_lay_detection/annotation_files/Test_annotation/{fn}")
        #####################
        
        if self.crop_gen is None:
            image, transforms = T.apply_transform_gens(self.tfm_gens, image)
        else:
            if np.random.rand() > 0.5:
                image, transforms = T.apply_transform_gens(self.tfm_gens, image)
            else:
                image, transforms = T.apply_transform_gens(
                    self.tfm_gens[:-1] + self.crop_gen + self.tfm_gens[-1:], image
                )

        image_shape = image.shape[:2]  # h, w
        
        # pil_im = Image.fromarray(image)
        # draw = ImageDraw.Draw(pil_im)
        # Pytorch's dataloader is efficient on torch.Tensor due to shared-memory,
        # but not efficient on large generic data structures due to the use of pickle & mp.Queue.
        # Therefore it's important to use torch.Tensor.
        # if len(image.shape) < 3:
        #     image = np.expand_dims(image, axis=-1)
        dataset_dict["image"] = torch.as_tensor(np.ascontiguousarray(image.transpose(2, 0, 1)))

        if not self.is_train:
            # USER: Modify this if you want to keep them for some reason.
            dataset_dict.pop("annotations", None)
            return dataset_dict

        if "annotations" in dataset_dict:
            # USER: Modify this if you want to keep them for some reason.
            for anno in dataset_dict["annotations"]:
                if not self.mask_on:
                    anno.pop("segmentation", None)
                anno.pop("keypoints", None)
            
            # USER: Implement additional transformations if you have other types of data
            annos = [
                utils.transform_instance_annotations(obj, transforms, image_shape)
                for obj in dataset_dict.pop("annotations")
                if obj.get("iscrowd", 0) == 0
            ]
           
            instances = utils.annotations_to_instances(annos, image_shape)
            dataset_dict["instances"] = utils.filter_empty_instances(instances)
            
        
        # for anno in annos:
        #     l, t, r, b = anno["bbox"]
        #     draw.rectangle([l,t,r,b], outline="red", width=2)
            
        # fn = dataset_dict['file_name'].split("/")[-1]
        # pil_im.save(f"/ml/miskanda/data/doc_layout/dit/test_aug/{fn}")
        
        return dataset_dict
