_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  PIXEL_MEAN: [ 127.5, 127.5, 127.5 ]
  PIXEL_STD: [ 127.5, 127.5, 127.5 ]
  WEIGHTS: "https://layoutlm.blob.core.windows.net/dit/dit-pts/dit-large-224-p16-500k-d7a2fb.pth"
  VIT:
    NAME: "dit_large_patch16"
    OUT_FEATURES: [ "layer7", "layer11", "layer15", "layer23" ]
    DROP_PATH: 0.2
  FPN:
    IN_FEATURES: [ "layer7", "layer11", "layer15", "layer23" ]
  ROI_HEADS:
    NAME: CascadeROIHeads
  ROI_BOX_HEAD:
    CLS_AGNOSTIC_BBOX_REG: True
  RPN:
    POST_NMS_TOPK_TRAIN: 2000
SOLVER:
  WARMUP_ITERS: 1000
  IMS_PER_BATCH: 16
  MAX_ITER: 60000
  CHECKPOINT_PERIOD: 2000
  BASE_LR: 0.0001
  STEPS: (40000, 53333)
  AMP:
    ENABLED: False
TEST:
  EVAL_PERIOD: 2000
