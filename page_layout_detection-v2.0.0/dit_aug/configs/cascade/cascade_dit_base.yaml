_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  PIXEL_MEAN: [ 127.5, 127.5, 127.5 ]
  PIXEL_STD: [ 127.5, 127.5, 127.5 ]
  WEIGHTS: "/ml/miskanda/data/doc_layout/dit/ckpt/cascade/20022025_1520/model_0027999.pth"
  VIT:
    NAME: "dit_base_patch16"
  ROI_HEADS:
    NAME: CascadeROIHeads
  ROI_BOX_HEAD:
    CLS_AGNOSTIC_BBOX_REG: True
  RPN:
    POST_NMS_TOPK_TRAIN: 2000
SOLVER:
  WARMUP_ITERS: 1000
  IMS_PER_BATCH: 8
  MAX_ITER: 600000
  CHECKPOINT_PERIOD: 1000
TEST:
  EVAL_PERIOD: 1000
  VAL_METRIC: "bbox/AP"
  MODE: "max"
OUTPUT_DIR: /ml/miskanda/data/doc_layout/dit/ckpt/cascade

