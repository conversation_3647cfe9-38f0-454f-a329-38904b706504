_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  PIXEL_MEAN: [ 127.5, 127.5, 127.5 ]
  PIXEL_STD: [ 127.5, 127.5, 127.5 ]
  WEIGHTS: "/ml/miskanda/data/doc_layout/dit/ckpt/maskrcnn_mix/21022025_1456/model_best.pth" # Model weights to load
  VIT:
    NAME: "dit_large_patch16"
    OUT_FEATURES: [ "layer7", "layer11", "layer15", "layer23" ]
    DROP_PATH: 0.2
  FPN:
    IN_FEATURES: [ "layer7", "layer11", "layer15", "layer23" ]
SOLVER:
  WARMUP_ITERS: 1000
  IMS_PER_BATCH: 8
  MAX_ITER: 900000
  CHECKPOINT_PERIOD: 1000
  BASE_LR: 0.00001
  AMP:
    ENABLED: False
TRAIN:
    DATA: 
      COCO_FILE: "/ml/miskanda/data/doc_layout/dit/annotations/2025_02_21_pres/train_2025_02_21_seg.json" # Training coco file
      IMAGE_DIR: "/ml/miskanda/data/doc_layout/dit/images/all_images" # Folder containing training images
TEST:
  DATA:
    COCO_FILE: "/ml/miskanda/data/doc_layout/dit/annotations/2025_02_21_pres/valid_2025_02_21_seg.json" # Validation coco file
    IMAGE_DIR: "/ml/miskanda/data/doc_layout/dit/images/all_images" # Folder containing validation images
  EVAL_PERIOD: 1000
  VAL_METRIC: "bbox/AP"
  MODE: "max"
OUTPUT_DIR: /ml/miskanda/data/doc_layout/dit/ckpt/maskrcnn_mix # Folder where to log training

