# DiT for Layout Detection

This folder contains running instructions for layout detection on top of [DiT for Object Detection](https://github.com/microsoft/unilm/tree/master/dit/object_detection)

## File structure
See the file structure below, excluding files from the original DiT repository.
```
configs/
ditod/
├─ layout_detection.py
├─ post_processing.py
├─ run_train.py
├─ utils.py
├─ ...
README.md
eval.py
example_inference.ipynb
inference.py
requirements.txt
train.py
```

## Quick Start
The fastest way to try the Layout Detection is using the jupyter notebook provided `example_inference.ipynb`. In the notebook you can provide paths to images and visualize the results directly in the notebook. All instructions are provided in the notebook.

## Inference
Inference can be run by running the inference.py file. Results will be written to a csv file and saved in the given save directory path with `--save-dir`.
The result of inference will be:
- Image Paths
- Bounding Boxes (x1, y1, x2, y2)
- Classes
- Scores


Inference can be run on either a folder of images or a csv path containg the paths to images.

Run inference on a folder of images with `--input-data`

```
python inference.py --input-data [IMAGE_DIRECTORY_PATH] --save-dir [SAVE_DIRECTORY_PATH] 
```
Run inference on csv file with `--input-data` and `--input-field` 
```
python inference.py --input-data [CSV_FILE_PATH] --input-field [NAME_OF_FIELD_CONTAINING_IMAGE_PATHS] --save-dir [SAVE_DIRECTORY_PATH] 
```

**Summary**

View all available arguments using the following command:
```
python inference.py --help
```


## Training
**Config file**

Parameters for training are all set in the config file. See the config file for example in `configs/maskrcnn_dit_large_example.yaml`. 

**Run Training Script**
```
python train.py --config [MODEL_CONFIG_PATH] --num-gpus [NUM_GPUS]
```

### Evaluation
**Config file**

This is run similarly to training. Provide the data you want to evaluate where you pass the validation data. 

**Run Evaluation Script**
```
python eval.py --config [MODEL_CONFIG_PATH] --num-gpus [NUM_GPUS]
```






