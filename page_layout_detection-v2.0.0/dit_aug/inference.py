import argparse
import os

import pandas as pd
from ditod.layout_detection import LayoutDetection
from ditod.utils import visualize


def main(args):
    # Create save directory if it doesn't exist
    os.makedirs(args.save_dir, exist_ok=True)
    
    # Instantiate model, local or using API
    ld = LayoutDetection(run_locally=args.run_locally)

    if args.model_path and args.run_locally is True:
        ld.set_local_model(args.config, args.model_path, args.device)
    if args.url and args.run_locally is False:
        ld.set_api_model(args.url)

    # Get image paths either from a folder of images or a csv (given the name of the field containing the image paths)
    if args.input_field:
        df = pd.read_csv(args.input_data)
        image_paths = df[args.input_field]
    else:
        if os.path.isdir(args.input_data):
            image_names = os.listdir(args.input_data)
            image_paths = [os.path.join(args.input_data, x) for x in image_names]
        else:
            image_paths = [args.input_data]

    # Get the results and save in the dataframe
    res = ld.predict(
        image_paths, batch_size=args.batch_size, num_workers=args.num_workers, no_async=args.no_async
    )

    # Save the results in a new csv file
    result_df = pd.DataFrame(res)
    result_df = result_df.reindex(columns=["file_path", "boxes", "classes", "scores"])
    result_df.to_csv(os.path.join(args.save_dir, "results.csv"), index=False)

    if args.visualize:
        os.makedirs(os.path.join(args.save_dir, "visualization"), exist_ok=True) 
        visualize(res, os.path.join(args.save_dir, "visualization"))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog="Layout Detection", description="Runs latest layout detection on given data, unless otherwise given. Runs API by default.")
    parser.add_argument(
        "--input-data", type=str, help="Directory containing images to run inference on or path to a csv file"
    )
    parser.add_argument(
        "--input-field",
        default=None,
        help="The name of the field containing image paths if using csv as input",
    )
    parser.add_argument("--save-dir", type=str, help="Where to save results")
    parser.add_argument("--config", type=str, help="[optional] Config file if running local inference")
    parser.add_argument("--model-path", type=str, help="[optional] Model weights if running local inference")
    parser.add_argument("--device", type=str, help="[optional] Device to run local inference on")
    parser.add_argument("--url", type=str, help="[optional] URL  if running inference with API")
    parser.add_argument("--no-async", action="store_true", help="[optional] Whether to run inference without async e.g when using jupyter. Default: False")
    parser.add_argument(
        "--run-locally",
        action="store_true",
        help="[optional] Whether to run inference locally with given files or run the API. Default: False",
    )
    parser.add_argument(
        "--visualize", action="store_true", help="[optional] Whether to visualize results. Default: False"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=2,
        help="[optional] Batch size of dataloader"
    )
    parser.add_argument(
        "--num-workers", type=int, default=2, help="[optional] Number of workers of dataloader"
    )
    args = parser.parse_args()
    print("Command Line Args:", args)
    main(args)
