{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Imports\n", "from matplotlib import pyplot as plt\n", "from ditod.layout_detection import LayoutDetection\n", "from ditod.utils import visualize"]}, {"cell_type": "markdown", "metadata": {}, "source": ["__LayoutDetection__\n", "\n", "Default models used when not given are the latest available\n", "\n", "* url [str][opt]: url to API\n", "* cfg_path [str][opt]: model config if running inference locally\n", "* model_path [str][opt]: model weights if running inference locally\n", "* device [str][opt]: device to run on when running inference locally. Default: *cuda*\n", "* run_locally [bool][opt]: whether to run inference locally or using API. Default: *False*\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Instantiating API model\n"]}], "source": ["# Instantiate model\n", "ld = LayoutDetection()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["__LayoutDetection.predict__\n", "\n", "Running API from jupyter notebook requires *no_async* to be True. Images will be processed one by one.\n", "* image_paths [list[str]]: list of image paths\n", "* batch_size [int][opt]: batchs size to run on. Default: 2\n", "* num_workers [int][opt]: number of workers for datalaoder Default: 2\n", "* no_async [bool][opt]: whether to use async when running api. Default: *False*"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:00<00:00,  1.09it/s]\n"]}], "source": ["example_image_paths = [\"/ml/miskanda/data/doc_layout/dit/test_data/rm_test/22480__00_22483__00.jpg\"] # List of image paths\n", "res = ld.predict(example_image_paths, batch_size=1, num_workers=1, no_async=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["__visualise__\n", "\n", "* image_paths [list[str]]: list of image paths\n", "* outputs [list[dict]]: output from the model with bounding boxes, classes and scores\n", "* save_dir [str][opt]: where to save the results. Default: None \n", "* ret_images [bool][opt]: whether to return the visualized PIL image. Default: False\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["im_res_list = visualize(res, ret_images=True)\n", "for im in im_res_list:\n", "    plt.imshow(im)\n", "    plt.axis(\"off\")\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "detectron_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 2}