# Quick User Creation Instructions

## 🎯 Create <PERSON>'s Account (or any user)

### Step 1: Copy the script to your container
```bash
docker cp create_carl_user.py 5c26685c2fac:/ragflow/create_carl_user.py
```

### Step 2: Run the script inside the container
```bash
docker exec -it 5c26685c2fac python3 /ragflow/create_carl_user.py
```

### Step 3: Test that it worked
```bash
# Copy test script
docker cp test_user_creation.py 5c26685c2fac:/ragflow/test_user_creation.py

# Run comprehensive test
docker exec -it 5c26685c2fac python3 /ragflow/test_user_creation.py
```

### Step 4: Restart container (if needed)
```bash
# Stop the container
docker stop 5c26685c2fac 

# Start it again
docker start 5c26685c2fac

# Check logs if needed
docker logs -f 5c26685c2fac
```

## 📝 To Create Different Users

Edit the `main()` function in `create_carl_user.py`:

```python
def main():
    """Main function - you can easily modify user details here."""
    
    # MODIFY THESE VALUES TO CREATE DIFFERENT USERS:
    email = "<EMAIL>"      # ← Change this
    nickname = "YourName"                # ← Change this  
    password = "YourPassword123"         # ← Change this
    
    # Rest of the code stays the same...
```

## 🔍 What the Scripts Do

### `create_carl_user.py`
- Uses the exact same `user_register()` function as normal registration
- Creates complete user setup: User, Tenant, LLM configs, File system
- Default creates Carl with:
  - Email: `<EMAIL>`
  - Nickname: `Carl`
  - Password: `Carl20250812`

### `test_user_creation.py`
- Checks if user exists in database
- Verifies complete setup (tenant, LLM configs, file system)
- Tests login credentials
- Provides detailed success/failure report

## ✅ Expected Output

### Successful Creation:
```
RAGFlow User Creator
=========================
Creating user: <EMAIL>
Creating user <NAME_EMAIL>...
✅ SUCCESS! User created successfully!
========================================
Email: <EMAIL>
Nickname: Carl
Password: Carl20250812
User ID: abc123def456...
========================================
Carl can now login to RAGFlow with these credentials.

🎉 User creation completed successfully!
```

### Successful Test:
```
RAGFlow User Creation & Login Test
========================================
Checking <NAME_EMAIL> exists...
✅ User found:
   Email: <EMAIL>
   Nickname: Carl
   Is Superuser: False
   Status: 1
   User ID: abc123def456...
   Login Channel: admin_created

🔍 Checking complete setup <NAME_EMAIL>...
✅ Tenant found: Carl's Kingdom
✅ User-tenant relationship found
✅ LLM configurations found: 5 configs
✅ Root folder found

🔐 Testing login <NAME_EMAIL>...
✅ Login credentials are valid!
   User can login with email: <EMAIL>
   Password verification: SUCCESS

========================================
SUMMARY:
✅ User exists: YES
✅ Complete setup: YES
✅ Login works: YES

🎉 SUCCESS! Carl can login to RAGFlow!
========================================
Test completed.
```

## 🚀 After Success

Carl can now login to RAGFlow at your server URL with:
- **Email:** `<EMAIL>`
- **Password:** `Carl20250812`

No admin account needed, no API calls needed - direct database creation that works exactly like normal registration!
