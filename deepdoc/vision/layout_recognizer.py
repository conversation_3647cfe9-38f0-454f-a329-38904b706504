#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
 
import os
import re
from collections import Counter, defaultdict
from copy import deepcopy
import logging
import numpy as np
 
# 1. Create a logger for this module
logger = logging.getLogger(__name__)  
logger.setLevel(logging.DEBUG)
 
# 2. Create a FileHandler to write DEBUG logs to disk
fh = logging.FileHandler('layout_debug.log', mode='w')
fh.setLevel(logging.DEBUG)
 
# 3. Choose a log format with timestamp, module name, level, and message
fmt = logging.Formatter('%(asctime)s %(name)s %(levelname)s %(message)s')
fh.setFormatter(fmt)
 
# 4. Attach the handler to your logger
logger.addHandler(fh)
 
import cv2
import numpy as np
from huggingface_hub import snapshot_download
 
from api.utils.file_utils import get_project_base_directory
from deepdoc.vision import Recognizer
from deepdoc.vision.operators import nms

 
class LayoutRecognizer(Recognizer):
    labels = [
        "_background_",
        "Text",
        "Title",
        "Figure",
        "Figure caption",
        "Table",
        "Table caption",
        "Header",
        "Footer",
        "Reference",
        "Equation",
    ]
 
    def __init__(self, domain):
        try:
            model_dir = os.path.join(
                get_project_base_directory(),
                "rag/res/deepdoc")
            super().__init__(self.labels, domain, model_dir)
        except Exception:
            model_dir = snapshot_download(repo_id="InfiniFlow/deepdoc",
                                          local_dir=os.path.join(get_project_base_directory(), "rag/res/deepdoc"),
                                          local_dir_use_symlinks=False)
            super().__init__(self.labels, domain, model_dir)
 
        self.garbage_layouts = ["footer", "header", "reference"]
        self.client = None
        if os.environ.get("TENSORRT_DLA_SVR"):
            from deepdoc.vision.dla_cli import DLAClient
            self.client = DLAClient(os.environ["TENSORRT_DLA_SVR"])
 
    def __call__(self, image_list, ocr_res, scale_factor=3, thr=0.2, batch_size=16, drop=True):
        def __is_garbage(b):
            patt = [r"^•+$", "^[0-9]{1,2} / ?[0-9]{1,2}$",
                    r"^[0-9]{1,2} of [0-9]{1,2}$", "^http://[^ ]{12,}",
                    "\\(cid *: *[0-9]+ *\\)"
                    ]
            return any([re.search(p, b["text"]) for p in patt])
 
        if self.client:
            layouts = self.client.predict(image_list)
        else:
            layouts = super().__call__(image_list, thr, batch_size)
        # save_results(image_list, layouts, self.labels, output_dir='output/', threshold=0.7)
        assert len(image_list) == len(ocr_res)
        # Tag layout type
        boxes = []
        assert len(image_list) == len(layouts)
        garbages = {}
        page_layout = []
        for pn, lts in enumerate(layouts):
            bxs = ocr_res[pn]
            lts = [{"type": b["type"],
                    "score": float(b["score"]),
                    "x0": b["bbox"][0] / scale_factor, "x1": b["bbox"][2] / scale_factor,
                    "top": b["bbox"][1] / scale_factor, "bottom": b["bbox"][-1] / scale_factor,
                    "page_number": pn,
                    } for b in lts if float(b["score"]) >= 0.4 or b["type"] not in self.garbage_layouts]
            lts = self.sort_Y_firstly(lts, np.mean(
                [lt["bottom"] - lt["top"] for lt in lts]) / 2)
            lts = self.layouts_cleanup(bxs, lts)
            page_layout.append(lts)
 
            # Tag layout type, layouts are ready
            def findLayout(ty):
                nonlocal bxs, lts, self
                lts_ = [lt for lt in lts if lt["type"] == ty]
                i = 0
                while i < len(bxs):
                    if bxs[i].get("layout_type"):
                        i += 1
                        continue
                    if __is_garbage(bxs[i]):
                        bxs.pop(i)
                        continue
 
                    ii = self.find_overlapped_with_threashold(bxs[i], lts_,
                                                              thr=0.4)
                    if ii is None:  # belong to nothing
                        bxs[i]["layout_type"] = ""
                        i += 1
                        continue
                    lts_[ii]["visited"] = True
                    keep_feats = [
                        lts_[
                            ii]["type"] == "footer" and bxs[i]["bottom"] < image_list[pn].size[1] * 0.9 / scale_factor,
                        lts_[
                            ii]["type"] == "header" and bxs[i]["top"] > image_list[pn].size[1] * 0.1 / scale_factor,
                    ]
                    if drop and lts_[
                            ii]["type"] in self.garbage_layouts and not any(keep_feats):
                        if lts_[ii]["type"] not in garbages:
                            garbages[lts_[ii]["type"]] = []
                        garbages[lts_[ii]["type"]].append(bxs[i]["text"])
                        bxs.pop(i)
                        continue
 
                    bxs[i]["layoutno"] = f"{ty}-{ii}"
                    bxs[i]["layout_type"] = lts_[ii]["type"] if lts_[
                        ii]["type"] != "equation" else "figure"
                    i += 1
 
            for lt in ["footer", "header", "reference", "figure caption",
                       "table caption", "title", "table", "text", "figure", "equation"]:
                findLayout(lt)
 
            # add box to figure layouts which has not text box
            for i, lt in enumerate(
                    [lt for lt in lts if lt["type"] in ["figure", "equation"]]):
                if lt.get("visited"):
                    continue
                lt = deepcopy(lt)
                del lt["type"]
                lt["text"] = ""
                lt["layout_type"] = "figure"
                lt["layoutno"] = f"figure-{i}"
                bxs.append(lt)
 
            boxes.extend(bxs)
 
        ocr_res = boxes
 
        garbag_set = set()
        for k in garbages.keys():
            garbages[k] = Counter(garbages[k])
            for g, c in garbages[k].items():
                if c > 1:
                    garbag_set.add(g)
 
        ocr_res = [b for b in ocr_res if b["text"].strip() not in garbag_set]
        return ocr_res, page_layout
 
    def forward(self, image_list, thr=0.7, batch_size=16):
        return super().__call__(image_list, thr, batch_size)
 
 
class LayoutRecognizer4YOLOv10(LayoutRecognizer):
    labels = [
        "title",
        "Text",
        "Reference",
        "Figure",
        "Figure caption",
        "Table",
        "Table caption",
        "Table caption",
        "Equation",
        "Figure caption",
    ]
 
    def __init__(self, domain):
        domain = "layout"
        super().__init__(domain)
        self.auto = False
        self.scaleFill = False
        self.scaleup = True
        self.stride = 32
        self.center = True
 
    def preprocess(self, image_list):
        inputs = []
        new_shape = self.input_shape  # height, width
        for img in image_list:
            shape = img.shape[:2]  # current shape [height, width]
            # Scale ratio (new / old)
            r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
            # Compute padding
            new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
            dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
            dw /= 2  # divide padding into 2 sides
            dh /= 2
            ww, hh = new_unpad
            img = np.array(cv2.cvtColor(img, cv2.COLOR_BGR2RGB)).astype(np.float32)
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
            top, bottom = int(round(dh - 0.1)) if self.center else 0, int(round(dh + 0.1))
            left, right = int(round(dw - 0.1)) if self.center else 0, int(round(dw + 0.1))
            img = cv2.copyMakeBorder(
                img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114)
            )  # add border
            img /= 255.0
            img = img.transpose(2, 0, 1)
            img = img[np.newaxis, :, :, :].astype(np.float32)
            inputs.append({self.input_names[0]: img, "scale_factor": [shape[1]/ww, shape[0]/hh, dw, dh]})
 
        return inputs
 
    def postprocess(self, boxes, inputs, thr):
        thr = 0.08
        boxes = np.squeeze(boxes)
        scores = boxes[:, 4]
        boxes = boxes[scores > thr, :]
        scores = scores[scores > thr]
        if len(boxes) == 0:
            return []
        class_ids = boxes[:, -1].astype(int)
        boxes = boxes[:, :4]
        boxes[:, 0] -= inputs["scale_factor"][2]
        boxes[:, 2] -= inputs["scale_factor"][2]
        boxes[:, 1] -= inputs["scale_factor"][3]
        boxes[:, 3] -= inputs["scale_factor"][3]
        input_shape = np.array([inputs["scale_factor"][0], inputs["scale_factor"][1], inputs["scale_factor"][0],
                                inputs["scale_factor"][1]])
        boxes = np.multiply(boxes, input_shape, dtype=np.float32)
 
        unique_class_ids = np.unique(class_ids)
        indices = []
        for class_id in unique_class_ids:
            class_indices = np.where(class_ids == class_id)[0]
            class_boxes = boxes[class_indices, :]
            class_scores = scores[class_indices]
            class_keep_boxes = nms(class_boxes, class_scores, 0.45)
            indices.extend(class_indices[class_keep_boxes])
 
        output = [{
            "type": self.label_list[class_ids[i]].lower(),
            "bbox": [float(t) for t in boxes[i].tolist()],
            "score": float(scores[i])
        } for i in indices]
 
        # 2. Log it once
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                "YOLO postprocess: %d boxes on page %s. First 3: %s",
                len(output),
                inputs.get("page_number", "?"),
                output[:3]  # slice keeps the file from exploding
            )


        # 3. Return it
        return output
 
 
 
def ocr_adjust_bbox(output, ocr_page_output, image_shape):

 
    w, h = image_shape
    pred_bboxes = output["boxes"]
    final_bboxes = []
    ocr_results = []
    for block in ocr_page_output:
        x0, top, x1, bottom = block["x0"], block["top"], block["x1"], block["bottom"]
        text = block["text"]
        if text:
            ocr_results.append(
                {"text": text, "x0": x0, "x1": x1, "top": top, "bottom": bottom}
            )
 
    for bbox in pred_bboxes:
        pred_ocr = []
        # Find overlapping ocr boxes
        for ocr in ocr_results:
            x1, y1, x12, y12 = bbox
            w1 = x12 - x1
            h1 = y12 - y1
            x2, y2, w2, h2 = (
                ocr["x0"],
                ocr["top"],
                ocr["x1"] - ocr["x0"],
                ocr["bottom"] - ocr["top"],
            )
 
            # Calculate the coordinates of the intersection rectangle
            intersection_x1 = max(x1, x2)
            intersection_y1 = max(y1, y2)
            intersection_x2 = min(x1 + w1, x2 + w2)
            intersection_y2 = min(y1 + h1, y2 + h2)
 
            # Calculate the width and height of the intersection rectangle
            intersection_width = max(0, intersection_x2 - intersection_x1)
            intersection_height = max(0, intersection_y2 - intersection_y1)
 
            # Calculate the area of the intersection rectangle
            intersection_area = intersection_width * intersection_height
 
            # Calculate the area of the specified bounding box
            base_area = min(w1 * h1, w2 * h2)
            if base_area == 0: continue
 
            # Calculate the intersection area as a percentage of the base area
            overlap = intersection_area / base_area
            # print(overlap)
            if overlap > 0.2:
                pred_ocr.append([x2, y2, x2 + w2, y2 + h2])
 
        # Adjust bboxes to any overlpaping ocr bbox
        if pred_ocr:
            ocr_array = np.array(pred_ocr)
            min_x2, min_y2 = np.min(ocr_array, axis=0)[0], np.min(ocr_array, axis=0)[1]
            max_x2, max_y2 = np.max(ocr_array, axis=0)[2], np.max(ocr_array, axis=0)[3]
            x1_end = x1 + w1
            y1_end = y1 + h1
            if x1 > min_x2:
                x1 = min_x2
            if y1 > min_y2:
                y1 = min_y2
            if x1_end < max_x2:
                x1_end = max_x2
            if y1_end < max_y2:
                y1_end = max_y2
 
            bbox = [x1, y1, x1_end, y1_end]
 
        bbox = [float(b) for b in bbox]
        final_bboxes.append(bbox)
 
    output["boxes"] = final_bboxes
    return output
 


class CustomDiTLayoutRecognizer(LayoutRecognizer):
    """
    API-based DiT layout recognizer that inherits from LayoutRecognizer
    """

    def __init__(self, domain="layout"):
        """
        Initialize the DiT layout recognizer using API calls with optimized configuration

        Args:
            domain: Domain parameter (for compatibility with RAGFlow interface)
        """
        # Initialize parent class but we'll override the layout detection
        super().__init__(domain)

        # API endpoint for DiT layout detection
        self.api_url = "http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0"
        self.jpeg_quality   = 85

    def map_dit_to_ragflow_label(self, dit_label, bbox=None):
        mapping = {
            "TEXT": "text",           # Main text content
            "TITLE": "title",         # Document titles
            "FIGURE": "figure",       # Images, charts, diagrams
            "CAPTION": "figure caption",  # Captions for figures/tables
            "TABLE": "table",         # Table content
            "HEADER": "header",       # Page headers
            "FOOTER": "footer",       # Page footers
            "TOC": "reference",            # Table of contents → Reference
            "LIST": "text",           # Lists → treat as text content
            "LOGO": "figure",         # Logos → treat as figures
            "FORMS": "text"           # Forms → treat as text content
        }
 
        return mapping.get(dit_label.upper(), "text")



    def call_api(self, image):
        import requests
        import base64
        import io
 
        # Optimize image encoding using instance settings
        buf = io.BytesIO()
        # Resize large images to reduce API payload - COMMENTED OUT TO PRESERVE ORIGINAL BOUNDING BOX COORDINATES
        # if image.size[0] > self.max_image_size[0] or image.size[1] > self.max_image_size[1]:
        #     image.thumbnail(self.max_image_size)
        logger.debug(f"DiT API call with original image size: {image.size}")
        image.save(buf, format="JPEG", quality=self.jpeg_quality, optimize=True)
         #image.save(buf, format="JPEG")
        img_bytes = buf.getvalue()
        encoded_image = base64.b64encode(img_bytes).decode("utf-8")
 
        # Prepare API request
        post_data = {"inputs": [encoded_image]}
        session = requests.Session()
        session.trust_env = False
        try:
            response = session.post(
                self.api_url,
                json=post_data,
                headers={"Content-Type": "application/json"},
                timeout=30,
                proxies={"http": None, "https": None},
            )
            response.raise_for_status()
            result = response.json()
 
            logger.debug(f"DiT API call successful for image size: {image.size}")
            return result.get("outputs", {})
 
        except requests.exceptions.Timeout:
            logger.warning(f"DiT API timeout for image size: {image.size}")
            return {}
        except requests.exceptions.RequestException as e:
            logger.error(f"DiT API request failed: {e}")
            return {}
        except Exception as e:
            logger.error(f"DiT API unexpected error: {e}")
            return {}

    def forward(self, image_list, thr=0.7, batch_size=16):
        """
        Override the forward method to return DiT layout detection results
        in the same format as the original LayoutRecognizer

        Returns:
            List of lists, where each inner list contains dictionaries with:
            - type: string (layout type)
            - bbox: [x0, y0, x1, y1] (bounding box coordinates)
            - score: float (confidence score)
        """
        results = []

        for pn, img in enumerate(image_list):
            # Call DiT API
            api_response = self.call_api(img)

            logger.debug(f"DiT API response for page {pn}: boxes={len(api_response.get('boxes', []))}")

            # Log first few raw API bounding boxes for debugging
            if api_response.get('boxes'):
                logger.debug(f"DiT raw API boxes for page {pn} (first 2): {api_response.get('boxes', [])[:2]}")
                logger.debug(f"DiT raw API classes for page {pn} (first 2): {api_response.get('classes', [])[:2]}")
                logger.debug(f"DiT raw API scores for page {pn} (first 2): {api_response.get('scores', [])[:2]}")

            # Apply OCR adjustment if we have valid API response
            if api_response.get("boxes") and api_response.get("classes") and api_response.get("scores"):
                # Apply OCR adjustment to improve bounding box accuracy
                try:
                    # Import OCR here to avoid circular imports
                    from deepdoc.vision.ocr import OCR

                    # Initialize OCR if not already done
                    if not hasattr(self, '_ocr_engine'):
                        self._ocr_engine = OCR()

                    # Get OCR results for this image
                    ocr_results = self._ocr_engine(np.array(img))

                    # Convert OCR results to the format expected by ocr_adjust_bbox
                    ocr_page_output = []
                    for line in ocr_results:
                        bbox, (text, score) = line
                        if text.strip():  # Only include non-empty text
                            ocr_page_output.append({
                                "x0": bbox[0][0],
                                "top": bbox[0][1],
                                "x1": bbox[1][0],
                                "bottom": bbox[-1][1],
                                "text": text
                            })

                    # Apply OCR adjustment
                    api_response = ocr_adjust_bbox(api_response, ocr_page_output, img.size)
                    logger.debug(f"Applied OCR adjustment to {len(api_response.get('boxes', []))} boxes")

                except Exception as e:
                    logger.warning(f"OCR adjustment failed: {e}, proceeding without adjustment")
                    # Continue without OCR adjustment if it fails

            # Convert DiT output to expected format
            page_results = []
            for bbox, cls, score in zip(
                api_response.get("boxes", []),
                api_response.get("classes", []),
                api_response.get("scores", [])
            ):
                if score >= thr:  # Apply threshold
                    # Ensure valid bounding box coordinates
                    x0, y0, x1, y1 = bbox
                    if x0 > x1:
                        x0, x1 = x1, x0
                    if y0 > y1:
                        y0, y1 = y1, y0

                    page_results.append({
                        "type": self.map_dit_to_ragflow_label(cls),
                        "x0": float(x0),
                        "top": float(y0),
                        "x1": float(x1),
                        "bottom": float(y1),
                        "score": float(score)
                    })

            logger.debug(f"DiT processed {len(page_results)} boxes for page {pn}, first 3: {page_results[:3]}")
            results.append(page_results)

        return results

    def __call__(self, image_list, ocr_res, scale_factor=3, thr=0.2, batch_size=16, drop=True):
        """
        Override the __call__ method to completely replace the parent class behavior
        This method integrates DiT with OCR adjustment and follows the same pattern as LayoutRecognizer
        """
        def __is_garbage(b):
            patt = [r"^•+$", "^[0-9]{1,2} / ?[0-9]{1,2}$",
                    r"^[0-9]{1,2} of [0-9]{1,2}$", "^http://[^ ]{12,}",
                    "\\(cid *: *[0-9]+ *\\)"
                    ]
            return any([re.search(p, b["text"]) for p in patt])

        # Get DiT layout results using our custom forward method
        layouts = self.forward(image_list, thr, batch_size)

        assert len(image_list) == len(ocr_res)
        # Tag layout type
        boxes = []
        assert len(image_list) == len(layouts)
        garbages = {}
        page_layout = []

        for pn, lts in enumerate(layouts):
            bxs = ocr_res[pn]

            # Log original DiT bounding boxes before scaling
            logger.debug(f"DiT original boxes for page {pn} (before scale_factor={scale_factor} division): {lts[:2] if lts else []}")

            lts = [{"type": b["type"],
                    "score": float(b["score"]),
                    "x0": b["x0"] / scale_factor, "x1": b["x1"] / scale_factor,
                    "top": b["top"] / scale_factor, "bottom": b["bottom"] / scale_factor,
                    "page_number": pn,
                    } for b in lts if float(b["score"]) >= 0.4 or b["type"] not in self.garbage_layouts]

            # Log scaled bounding boxes after division
            logger.debug(f"DiT scaled boxes for page {pn} (after scale_factor={scale_factor} division): {lts[:2] if lts else []}")

            # Log image dimensions for reference
            if pn < len(image_list):
                logger.debug(f"Image {pn} dimensions: {image_list[pn].size}")

            # Log OCR box sample for comparison
            if bxs:
                logger.debug(f"OCR boxes sample for page {pn}: {bxs[:2]}")
            lts = self.sort_Y_firstly(lts, np.mean(
                [lt["bottom"] - lt["top"] for lt in lts]) / 2) if lts else []

            def findLayout(ty):
                nonlocal lts
                lts_ = [lt for lt in lts if lt["type"] == ty]
                i = 0
                while i < len(bxs):
                    if bxs[i].get("layout_type"):
                        i += 1
                        continue
                    if __is_garbage(bxs[i]):
                        bxs.pop(i)
                        continue

                    ii = self.find_overlapped_with_threashold(bxs[i], lts_, thr=0.4)
                    if ii is None:  # belong to nothing
                        bxs[i]["layout_type"] = ""
                        i += 1
                        continue

                    lts_[ii]["visited"] = True
                    keep_feats = [
                        bxs[i]["text"].strip(),
                        bxs[i].get("image_id", ""),
                        bxs[i].get("image", "")
                    ]
                    if drop and lts_[
                            ii]["type"] in self.garbage_layouts and not any(keep_feats):
                        if lts_[ii]["type"] not in garbages:
                            garbages[lts_[ii]["type"]] = []
                        garbages[lts_[ii]["type"]].append(bxs[i]["text"])
                        bxs.pop(i)
                        continue

                    bxs[i]["layoutno"] = f"{ty}-{ii}"
                    bxs[i]["layout_type"] = lts_[ii]["type"] if lts_[
                        ii]["type"] != "equation" else "figure"
                    i += 1

            for lt in ["footer", "header", "reference", "figure caption",
                       "table caption", "title", "table", "text", "figure", "equation"]:
                findLayout(lt)

            # add box to figure layouts which has not text box
            for i, lt in enumerate(
                    [lt for lt in lts if lt["type"] in ["figure", "equation"]]):
                if lt.get("visited"):
                    continue
                lt = deepcopy(lt)
                del lt["type"]
                lt["text"] = ""
                lt["layout_type"] = "figure"
                lt["layoutno"] = f"figure-{i}"
                bxs.append(lt)

            boxes.extend(bxs)
            page_layout.append(lts)

        ocr_res = boxes

        garbag_set = set()
        for k in garbages.keys():
            garbages[k] = Counter(garbages[k])
            for g, c in garbages[k].items():
                if c > 1:
                    garbag_set.add(g)

        ocr_res = [b for b in ocr_res if b["text"].strip() not in garbag_set]
        return ocr_res, page_layout
