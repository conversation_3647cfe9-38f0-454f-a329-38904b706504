import os
import sys
import requests

def get_raw_chunks(query, chat_id="f85bd66e619511f0b83602420ae90a06"):
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")

    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    try:
        # Create session
        session_url = f"{API_BASE_URL}/api/v1/chats/{chat_id}/sessions"
        session_payload = {"name": "Retrieval"}

        session_response = requests.post(session_url, json=session_payload, headers=headers)
        session_response.raise_for_status()
        session_result = session_response.json()

        if session_result.get("code") != 0:
            print(f"Session creation failed: {session_result.get('message', 'Unknown error')}")
            return []

        session_id = session_result["data"]["id"]
        print(f"Created session: {session_id}")

        # Ask question
        chat_url = f"{API_BASE_URL}/api/v1/chats/{chat_id}/completions"
        chat_payload = {
            "question": query,
            "stream": False,
            "session_id": session_id
        }

        chat_response = requests.post(chat_url, json=chat_payload, headers=headers)
        chat_response.raise_for_status()
        chat_result = chat_response.json()

        if chat_result.get("code") != 0:
            print(f"Chat request failed: {chat_result.get('message', 'Unknown error')}")
            return []

        chunks = chat_result.get("data", {}).get('reference', {}).get('chunks', [])

        print(f"Retrieved {len(chunks)} chunks from API")

        formatted_chunks = []
        for chunk in chunks:
            chunk_with_url = chunk.copy()
            if chunk.get("document_id"):
                chunk_with_url["document_url"] = get_document_view_url(chunk["document_id"])

            formatted_chunks.append({
                "content": chunk.get("content", ""),
                "similarity": chunk.get("similarity", 0),
                "document_name": chunk.get("document_name", "Unknown"),
                # "document_id": chunk.get("document_id", ""),
                # "chunk_id": chunk.get("id", ""),
                # "positions": chunk.get("positions", []),
                # "image_id": chunk.get("image_id", ""),
                # "kb_id": chunk.get("kb_id", ""),
                # "document_keyword": chunk.get("document_keyword", ""),
                # "all_metadata": chunk_with_url
            })

        return formatted_chunks

    except Exception as e:
        print(f"Error: {str(e)}")
        return []


def get_document_view_url(document_id):
    """Get the real document viewing URL that works in browser"""
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    return f"{API_BASE_URL}/document/{document_id}?ext=pdf&prefix=document"


if __name__ == "__main__":
    if len(sys.argv) > 1:
        query = " ".join(sys.argv[1:])
        chunks = get_raw_chunks(query)
        
        if chunks:
            import json
            #raw_data = [chunk.get('all_metadata', {}) for chunk in chunks]
            print(json.dumps(chunks, indent=2, ensure_ascii=False))
        else:
            print("[]")
    else:
        print("Usage: python ragflow_retrieval_gradio.py 'your question'")
