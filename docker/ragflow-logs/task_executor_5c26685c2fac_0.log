2025-08-12 21:37:42,336 INFO     35 task_executor_5c26685c2fac_0 log path: /ragflow/logs/task_executor_5c26685c2fac_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-08-12 21:37:42,337 INFO     35 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-08-12 21:37:42,339 INFO     35 TaskExecutor: RAGFlow version: v0.19.0 full
2025-08-12 21:37:42,342 INFO     35 Use Elasticsearch http://es01:9200 as the doc engine.
2025-08-12 21:37:42,350 INFO     35 GET http://es01:9200/ [status:200 duration:0.004s]
2025-08-12 21:37:42,353 INFO     35 HEAD http://es01:9200/ [status:200 duration:0.002s]
2025-08-12 21:37:42,354 INFO     35 Elasticsearch http://es01:9200 is healthy.
2025-08-12 21:37:42,368 WARNING  35 Load term.freq FAIL!
2025-08-12 21:37:42,373 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-08-12 21:37:42,378 WARNING  35 Load term.freq FAIL!
2025-08-12 21:37:42,382 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-08-12 21:37:42,383 INFO     35 MAX_CONTENT_LENGTH: 134217728
2025-08-12 21:37:42,387 INFO     35 MAX_FILE_COUNT_PER_USER: 0
2025-08-12 21:37:42,397 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:37:42.396+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:37:42,399 WARNING  35 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-08-12 21:38:12,402 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:38:12.400+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:38:42,410 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:38:42.408+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:39:12,418 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:39:12.416+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:39:42,427 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:39:42.426+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:40:12,434 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:40:12.432+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:40:42,442 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:40:42.440+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:41:12,449 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:41:12.447+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
