2025-08-12 21:37:42,336 INFO     35 task_executor_5c26685c2fac_0 log path: /ragflow/logs/task_executor_5c26685c2fac_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-08-12 21:37:42,337 INFO     35 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-08-12 21:37:42,339 INFO     35 TaskExecutor: RAGFlow version: v0.19.0 full
2025-08-12 21:37:42,342 INFO     35 Use Elasticsearch http://es01:9200 as the doc engine.
2025-08-12 21:37:42,350 INFO     35 GET http://es01:9200/ [status:200 duration:0.004s]
2025-08-12 21:37:42,353 INFO     35 HEAD http://es01:9200/ [status:200 duration:0.002s]
2025-08-12 21:37:42,354 INFO     35 Elasticsearch http://es01:9200 is healthy.
2025-08-12 21:37:42,368 WARNING  35 Load term.freq FAIL!
2025-08-12 21:37:42,373 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-08-12 21:37:42,378 WARNING  35 Load term.freq FAIL!
2025-08-12 21:37:42,382 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-08-12 21:37:42,383 INFO     35 MAX_CONTENT_LENGTH: 134217728
2025-08-12 21:37:42,387 INFO     35 MAX_FILE_COUNT_PER_USER: 0
2025-08-12 21:37:42,397 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:37:42.396+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:37:42,399 WARNING  35 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-08-12 21:38:12,402 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:38:12.400+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:38:42,410 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:38:42.408+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:39:12,418 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:39:12.416+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:39:42,427 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:39:42.426+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:40:12,434 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:40:12.432+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:40:42,442 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:40:42.440+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:41:12,449 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:41:12.447+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:41:42,455 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:41:42.453+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:42:12,462 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:42:12.460+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:42:42,469 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:42:42.467+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:43:12,476 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:43:12.474+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:43:42,483 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:43:42.481+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:44:12,490 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:44:12.488+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:44:42,495 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:44:42.493+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:45:12,501 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:45:12.499+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:45:42,507 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:45:42.506+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:46:12,514 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:46:12.513+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:46:42,521 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:46:42.519+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:47:12,527 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:47:12.526+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:47:42,534 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:47:42.532+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:48:12,541 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:48:12.539+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:48:42,548 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:48:42.546+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:49:12,553 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:49:12.551+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:49:42,558 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:49:42.556+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:50:12,565 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:50:12.563+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:50:42,571 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:50:42.569+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:51:12,577 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:51:12.576+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:51:42,585 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:51:42.583+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:52:12,592 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:52:12.590+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:52:42,599 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:52:42.597+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:53:12,606 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:53:12.604+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:53:42,613 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:53:42.612+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:54:12,620 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:54:12.618+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:54:42,628 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:54:42.626+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:55:12,636 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:55:12.634+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:55:42,645 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:55:42.643+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:56:12,652 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:56:12.650+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:56:42,660 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:56:42.658+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:57:12,667 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:57:12.665+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:57:42,674 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:57:42.672+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:58:12,679 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:58:12.677+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:58:42,685 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:58:42.684+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:59:12,692 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:59:12.690+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 21:59:42,700 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T21:59:42.699+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:00:12,707 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:00:12.706+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:00:42,714 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:00:42.712+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:01:12,721 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:01:12.719+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:01:42,727 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:01:42.726+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:02:12,734 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:02:12.732+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:02:42,742 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:02:42.740+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:03:12,748 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:03:12.746+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:03:42,756 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:03:42.754+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:04:12,763 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:04:12.761+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:04:42,773 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:04:42.772+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:05:12,781 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:05:12.778+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:05:42,790 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:05:42.789+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:06:12,797 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:06:12.795+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:06:42,804 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:06:42.803+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:07:12,810 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:07:12.808+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:07:42,819 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:07:42.817+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:08:12,826 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:08:12.824+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:08:42,832 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:08:42.830+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:09:12,839 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:09:12.837+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:09:42,848 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:09:42.846+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:10:12,876 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:10:12.874+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:10:42,884 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:10:42.883+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:11:12,890 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:11:12.888+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:11:42,898 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:11:42.896+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:12:12,905 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:12:12.903+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:12:42,915 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:12:42.913+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:13:12,922 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:13:12.920+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:13:42,939 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:13:42.937+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:14:12,946 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:14:12.944+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:14:42,954 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:14:42.952+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:15:12,962 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:15:12.960+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:15:43,217 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:15:43.215+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:16:13,224 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:16:13.222+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:16:43,231 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:16:43.230+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:17:13,238 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:17:13.236+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:17:43,249 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:17:43.247+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:18:13,256 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:18:13.254+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:18:43,264 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:18:43.262+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:19:13,271 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:19:13.270+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:19:43,281 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:19:43.280+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:20:13,289 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:20:13.287+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:20:43,298 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:20:43.296+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:21:13,306 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:21:13.304+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:21:43,312 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:21:43.310+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:22:13,318 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:22:13.317+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:22:43,330 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:22:43.328+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:23:13,337 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:23:13.335+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:23:43,345 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:23:43.343+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:24:13,352 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:24:13.350+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:24:43,360 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:24:43.358+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:25:13,367 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:25:13.365+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:25:43,375 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:25:43.373+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:26:13,382 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:26:13.380+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:26:43,391 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:26:43.389+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:27:13,398 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:27:13.396+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:27:43,405 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:27:43.404+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:28:13,412 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:28:13.411+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:28:43,420 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:28:43.418+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:29:13,428 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:29:13.426+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:29:43,437 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:29:43.435+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:30:13,444 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:30:13.442+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:30:43,452 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:30:43.451+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:31:13,460 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:31:13.458+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:31:43,468 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:31:43.466+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:32:13,475 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:32:13.473+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:32:43,486 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:32:43.484+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:33:13,493 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:33:13.491+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:33:43,502 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:33:43.500+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:34:13,509 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:34:13.507+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:34:43,518 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:34:43.516+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:35:13,525 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:35:13.523+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:35:43,532 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:35:43.530+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:36:13,539 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:36:13.537+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:36:43,545 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:36:43.543+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:37:13,552 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:37:13.550+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:37:43,559 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:37:43.557+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:38:13,565 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:38:13.563+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:38:43,572 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:38:43.570+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:39:13,579 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:39:13.577+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:39:43,587 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:39:43.585+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:40:13,594 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:40:13.592+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:40:43,600 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:40:43.598+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:41:13,606 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:41:13.605+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:41:43,613 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:41:43.612+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:42:13,620 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:42:13.619+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:42:43,627 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:42:43.625+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:43:13,635 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:43:13.633+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:43:43,642 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:43:43.640+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:44:13,648 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:44:13.646+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:44:43,655 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:44:43.653+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:45:13,663 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:45:13.661+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:45:43,670 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:45:43.668+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:46:13,677 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:46:13.676+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:46:43,685 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:46:43.683+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:47:13,692 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:47:13.690+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:47:43,699 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:47:43.697+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:48:13,706 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:48:13.704+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:48:43,713 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:48:43.711+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:49:13,719 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:49:13.718+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:49:43,726 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:49:43.724+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:50:13,733 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:50:13.731+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:50:43,740 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:50:43.738+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:51:13,747 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:51:13.745+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:51:43,754 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:51:43.753+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:52:13,762 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:52:13.760+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:52:43,769 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:52:43.767+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:53:13,775 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:53:13.774+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:53:43,783 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:53:43.781+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:54:13,790 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:54:13.789+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:54:43,798 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:54:43.796+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:55:13,805 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:55:13.803+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:55:43,812 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:55:43.810+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:56:13,819 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:56:13.817+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:56:43,826 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:56:43.824+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:57:13,833 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:57:13.831+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:57:43,840 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:57:43.838+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:58:13,847 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:58:13.846+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:58:43,855 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:58:43.853+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:59:13,861 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:59:13.859+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 22:59:43,867 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T22:59:43.866+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:00:13,872 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:00:13.870+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:00:43,880 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:00:43.878+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:01:13,887 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:01:13.885+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:01:43,894 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:01:43.892+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:02:13,902 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:02:13.900+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:02:43,908 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:02:43.906+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:03:13,914 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:03:13.912+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:03:43,922 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:03:43.920+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:04:13,929 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:04:13.927+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:04:43,936 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:04:43.934+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:05:13,944 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:05:13.942+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:05:43,951 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:05:43.949+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:06:13,958 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:06:13.956+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:06:43,964 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:06:43.963+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:07:13,971 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:07:13.970+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:07:43,978 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:07:43.976+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:08:13,986 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:08:13.984+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:08:43,993 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:08:43.991+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:09:13,999 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:09:13.997+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:09:44,007 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:09:44.005+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:10:14,014 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:10:14.013+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:10:44,022 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:10:44.020+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:11:14,029 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:11:14.027+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:11:44,036 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:11:44.034+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:12:14,043 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:12:14.041+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:12:44,050 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:12:44.048+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:13:14,057 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:13:14.055+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:13:44,064 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:13:44.062+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:14:14,071 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:14:14.070+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:14:44,093 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:14:44.092+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:15:14,100 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:15:14.098+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:15:44,107 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:15:44.105+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:16:14,114 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:16:14.112+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:16:44,122 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:16:44.120+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:17:14,130 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:17:14.128+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:17:44,137 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:17:44.135+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:18:14,143 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:18:14.141+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:18:44,150 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:18:44.149+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:19:14,158 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:19:14.156+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:19:44,165 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:19:44.163+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:20:14,173 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:20:14.171+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:20:44,180 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:20:44.178+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:21:14,187 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:21:14.185+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:21:44,195 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:21:44.193+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:22:14,202 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:22:14.200+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:22:44,209 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:22:44.207+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:23:14,216 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:23:14.214+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:23:44,223 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:23:44.221+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:24:14,230 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:24:14.228+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:24:44,237 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:24:44.235+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:25:14,243 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:25:14.242+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:25:44,250 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:25:44.248+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:26:14,257 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:26:14.255+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:26:44,264 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:26:44.262+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:27:14,269 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:27:14.267+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:27:44,277 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:27:44.275+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:28:14,284 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:28:14.282+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:28:44,292 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:28:44.290+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:29:14,298 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:29:14.296+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:29:44,305 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:29:44.304+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:30:14,313 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:30:14.311+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:30:44,320 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:30:44.318+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:31:14,327 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:31:14.325+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:31:44,334 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:31:44.333+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:32:14,341 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:32:14.340+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:32:44,348 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:32:44.346+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:33:14,355 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:33:14.353+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:33:44,362 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:33:44.360+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:34:14,370 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:34:14.368+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:34:44,376 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:34:44.374+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:35:14,384 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:35:14.382+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:35:44,391 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:35:44.389+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:36:14,397 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:36:14.396+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-12 23:36:20,959 INFO     35 handle_task begin for task {"id": "17f96830779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 0, "to_page": 12, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979979, "task_type": ""}
2025-08-12 23:36:21,106 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-08-12 23:36:21,497 INFO     35 PyTorch version 2.6.0 available.
2025-08-12 23:36:23,587 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-08-12 23:36:23,847 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-08-12 23:36:29,206 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 1
2025-08-12 23:36:29,214 INFO     35 HEAD http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05 [status:404 duration:0.007s]
2025-08-12 23:36:29,411 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05 [status:200 duration:0.194s]
2025-08-12 23:36:29,457 INFO     35 handle_task begin for task {"id": "17f96a1a779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 12, "to_page": 24, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979980, "task_type": ""}
2025-08-12 23:36:29,598 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 1
2025-08-12 23:36:29,601 INFO     35 HEAD http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05 [status:200 duration:0.003s]
2025-08-12 23:36:29,616 INFO     35 handle_task begin for task {"id": "17f96a9c779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 24, "to_page": 36, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979981, "task_type": ""}
2025-08-12 23:36:29,681 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 1
2025-08-12 23:36:29,684 INFO     35 HEAD http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05 [status:200 duration:0.002s]
2025-08-12 23:36:29,699 INFO     35 handle_task begin for task {"id": "17f96b14779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 36, "to_page": 45, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979982, "task_type": ""}
2025-08-12 23:36:29,765 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 1
2025-08-12 23:36:29,768 INFO     35 HEAD http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05 [status:200 duration:0.002s]
2025-08-12 23:36:29,819 INFO     35 From minio(0.4063154029427096) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf
2025-08-12 23:36:29,868 INFO     35 From minio(0.2671501759905368) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf
2025-08-12 23:36:29,869 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.1, progress_msg: 23:36:29 Page(1~13): Start to parse.
2025-08-12 23:36:29,872 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:36:29,876 INFO     35 From minio(0.19169714499730617) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf
2025-08-12 23:36:29,890 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:36:29,890 INFO     35 From minio(0.12075812800321728) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf
2025-08-12 23:36:30,239 INFO     35 load_model /ragflow/rag/res/deepdoc/layout.onnx uses CPU
2025-08-12 23:36:30,320 INFO     35 load_model /ragflow/rag/res/deepdoc/tsr.onnx uses CPU
2025-08-12 23:36:30,370 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:36:30 Page(1~13): OCR started
2025-08-12 23:36:31,796 INFO     35 __images__ dedupe_chars cost 1.4249281490920112s
2025-08-12 23:36:31,798 WARNING  35 Miss outlines
2025-08-12 23:36:32,058 INFO     35 __ocr detecting boxes of a image cost (0.25699243997223675s)
2025-08-12 23:36:32,058 INFO     35 __ocr sorting 0 chars cost 0.0003258609212934971s
2025-08-12 23:36:32,667 INFO     35 __ocr recognize 17 boxes cost 0.6032213090220466s
2025-08-12 23:36:32,793 INFO     35 __ocr detecting boxes of a image cost (0.1249720579944551s)
2025-08-12 23:36:32,794 INFO     35 __ocr sorting 0 chars cost 0.00039421103429049253s
2025-08-12 23:36:33,265 INFO     35 __ocr recognize 19 boxes cost 0.46937291708309203s
2025-08-12 23:36:33,394 INFO     35 __ocr detecting boxes of a image cost (0.12817149295005947s)
2025-08-12 23:36:33,395 INFO     35 __ocr sorting 0 chars cost 0.0006127549568191171s
2025-08-12 23:36:34,084 INFO     35 __ocr recognize 39 boxes cost 0.6867481300141662s
2025-08-12 23:36:34,247 INFO     35 __ocr detecting boxes of a image cost (0.1626967730699107s)
2025-08-12 23:36:34,249 INFO     35 __ocr sorting 0 chars cost 0.001630165963433683s
2025-08-12 23:36:35,312 INFO     35 __ocr recognize 136 boxes cost 1.0612859460525215s
2025-08-12 23:36:35,457 INFO     35 __ocr detecting boxes of a image cost (0.1447149079758674s)
2025-08-12 23:36:35,459 INFO     35 __ocr sorting 0 chars cost 0.0010213979985564947s
2025-08-12 23:36:36,306 INFO     35 __ocr recognize 85 boxes cost 0.8454937820788473s
2025-08-12 23:36:36,427 INFO     35 __ocr detecting boxes of a image cost (0.12021080893464386s)
2025-08-12 23:36:36,428 INFO     35 __ocr sorting 0 chars cost 0.0002130080247297883s
2025-08-12 23:36:36,874 INFO     35 __ocr recognize 19 boxes cost 0.44582376698963344s
2025-08-12 23:36:36,881 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.3, progress_msg: 
2025-08-12 23:36:37,027 INFO     35 __ocr detecting boxes of a image cost (0.14532783103641123s)
2025-08-12 23:36:37,027 INFO     35 __ocr sorting 0 chars cost 0.000600907951593399s
2025-08-12 23:36:38,109 INFO     35 __ocr recognize 37 boxes cost 1.078056826023385s
2025-08-12 23:36:38,244 INFO     35 __ocr detecting boxes of a image cost (0.13261881296057254s)
2025-08-12 23:36:38,244 INFO     35 __ocr sorting 0 chars cost 0.00024266703985631466s
2025-08-12 23:36:38,632 INFO     35 __ocr recognize 12 boxes cost 0.3866489139618352s
2025-08-12 23:36:38,780 INFO     35 __ocr detecting boxes of a image cost (0.1466430729487911s)
2025-08-12 23:36:38,781 INFO     35 __ocr sorting 0 chars cost 0.0006642290391027927s
2025-08-12 23:36:39,097 INFO     35 __ocr recognize 40 boxes cost 0.31043862097430974s
2025-08-12 23:36:39,248 INFO     35 __ocr detecting boxes of a image cost (0.15062076202593744s)
2025-08-12 23:36:39,249 INFO     35 __ocr sorting 0 chars cost 0.0007502699736505747s
2025-08-12 23:36:39,700 INFO     35 __ocr recognize 45 boxes cost 0.44981964002363384s
2025-08-12 23:36:39,871 INFO     35 __ocr detecting boxes of a image cost (0.16957506397739053s)
2025-08-12 23:36:39,873 INFO     35 __ocr sorting 0 chars cost 0.0016467910027131438s
2025-08-12 23:36:40,543 INFO     35 __ocr recognize 49 boxes cost 0.6677530790911987s
2025-08-12 23:36:40,688 INFO     35 __ocr detecting boxes of a image cost (0.14408007892780006s)
2025-08-12 23:36:40,689 INFO     35 __ocr sorting 0 chars cost 0.0006683729588985443s
2025-08-12 23:36:41,064 INFO     35 __ocr recognize 39 boxes cost 0.37156826606951654s
2025-08-12 23:36:41,071 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.6, progress_msg: 
2025-08-12 23:36:41,072 INFO     35 __images__ 12 pages cost 9.272335588932037s
2025-08-12 23:36:41,079 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:36:41 Page(1~13): OCR finished (10.74s)
2025-08-12 23:36:41,079 INFO     35 OCR(0~12): 10.74s
2025-08-12 23:36:41,083 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:41,558 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:41,559 DEBUG    35 DiT API response for page 0: boxes=6
2025-08-12 23:36:41,562 DEBUG    35 DiT raw API boxes for page 0 (first 2): [[260.061322479248, 515.1993109520446, 1634.9172103881835, 817.1651429521278], [225.29624492645263, 263.84720627805024, 947.4478289794921, 324.4113912379488]]
2025-08-12 23:36:41,564 DEBUG    35 DiT raw API classes for page 0 (first 2): ['LIST', 'TITLE']
2025-08-12 23:36:41,567 DEBUG    35 DiT raw API scores for page 0 (first 2): [0.9993115663528442, 0.9977299571037292]
2025-08-12 23:36:41,570 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:36:41,587 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:36:42,297 DEBUG    35 Applied OCR adjustment to 6 boxes
2025-08-12 23:36:42,297 DEBUG    35 DiT processed 6 boxes for page 0, first 3: [{'type': 'text', 'x0': 260.061322479248, 'top': 515.1993109520446, 'x1': 1634.9172103881835, 'bottom': 817.1651429521278, 'score': 0.9993115663528442}, {'type': 'title', 'x0': 210.0, 'top': 263.84720627805024, 'x1': 947.447828979492, 'bottom': 324.4113912379488, 'score': 0.9977299571037292}, {'type': 'text', 'x0': 212.0, 'top': 328.8918145362367, 'x1': 1613.0, 'bottom': 493.0, 'score': 0.9935467839241028}]
2025-08-12 23:36:42,300 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:42,715 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:42,716 DEBUG    35 DiT API response for page 1: boxes=9
2025-08-12 23:36:42,719 DEBUG    35 DiT raw API boxes for page 1 (first 2): [[312.4769676208496, 713.1389445852727, 1522.5918408203124, 2061.8646941489365], [218.74539358139037, 519.7340113863032, 467.7236869812011, 567.0422856756982]]
2025-08-12 23:36:42,721 DEBUG    35 DiT raw API classes for page 1 (first 2): ['FIGURE', 'TITLE']
2025-08-12 23:36:42,723 DEBUG    35 DiT raw API scores for page 1 (first 2): [0.9990780353546143, 0.9985679388046265]
2025-08-12 23:36:43,403 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:36:43,403 DEBUG    35 DiT processed 9 boxes for page 1, first 3: [{'type': 'figure', 'x0': 312.4769676208496, 'top': 713.1389445852727, 'x1': 1522.5918408203124, 'bottom': 2061.8646941489365, 'score': 0.9990780353546143}, {'type': 'title', 'x0': 212.0, 'top': 519.7340113863032, 'x1': 467.7236869812011, 'bottom': 567.0422856756982, 'score': 0.9985679388046265}, {'type': 'title', 'x0': 210.57059368133542, 'top': 265.0, 'x1': 1160.3975004577635, 'bottom': 323.9513329039229, 'score': 0.9976363182067871}]
2025-08-12 23:36:43,406 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:43,811 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:43,813 DEBUG    35 DiT API response for page 2: boxes=9
2025-08-12 23:36:43,817 DEBUG    35 DiT raw API boxes for page 2 (first 2): [[209.73718200683592, 1028.3801996758646, 1625.8117680358887, 1845.9809622257314], [221.24298179626464, 249.2853484458112, 1071.9304988861084, 298.3886601874169]]
2025-08-12 23:36:43,820 DEBUG    35 DiT raw API classes for page 2 (first 2): ['FIGURE', 'TITLE']
2025-08-12 23:36:43,822 DEBUG    35 DiT raw API scores for page 2 (first 2): [0.9991686344146729, 0.9988189339637756]
2025-08-12 23:36:44,406 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:36:44.402+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 4, "lag": 0, "done": 0, "failed": 0, "current": {"17f96830779211f0ac8e02420ae90a05": {"id": "17f96830779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 0, "to_page": 12, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979979, "task_type": ""}, "17f96a1a779211f0ac8e02420ae90a05": {"id": "17f96a1a779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 12, "to_page": 24, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979980, "task_type": ""}, "17f96a9c779211f0ac8e02420ae90a05": {"id": "17f96a9c779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 24, "to_page": 36, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979981, "task_type": ""}, "17f96b14779211f0ac8e02420ae90a05": {"id": "17f96b14779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 36, "to_page": 45, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979982, "task_type": ""}}}
2025-08-12 23:36:44,636 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:36:44,637 DEBUG    35 DiT processed 9 boxes for page 2, first 3: [{'type': 'figure', 'x0': 209.73718200683592, 'top': 1028.3801996758646, 'x1': 1625.8117680358887, 'bottom': 1845.9809622257314, 'score': 0.9991686344146729}, {'type': 'title', 'x0': 212.0, 'top': 249.2853484458112, 'x1': 1071.9304988861084, 'bottom': 298.3886601874169, 'score': 0.9988189339637756}, {'type': 'text', 'x0': 209.61104667663574, 'top': 634.0, 'x1': 1623.0, 'bottom': 839.6178549908578, 'score': 0.998297393321991}]
2025-08-12 23:36:44,639 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:45,043 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:45,044 DEBUG    35 DiT API response for page 3: boxes=8
2025-08-12 23:36:45,047 DEBUG    35 DiT raw API boxes for page 3 (first 2): [[225.45628044128418, 292.43581179355056, 1617.2355471801757, 1082.6435910488697], [936.107498550415, 1176.4591661818486, 1623.5458193969725, 2137.5148198553857]]
2025-08-12 23:36:45,050 DEBUG    35 DiT raw API classes for page 3 (first 2): ['TABLE', 'TABLE']
2025-08-12 23:36:45,053 DEBUG    35 DiT raw API scores for page 3 (first 2): [0.9994533658027649, 0.9993484616279602]
2025-08-12 23:36:46,348 DEBUG    35 Applied OCR adjustment to 8 boxes
2025-08-12 23:36:46,348 DEBUG    35 DiT processed 8 boxes for page 3, first 3: [{'type': 'table', 'x0': 225.45628044128418, 'top': 292.43581179355056, 'x1': 1617.2355471801757, 'bottom': 1082.6435910488697, 'score': 0.9994533658027649}, {'type': 'table', 'x0': 936.107498550415, 'top': 1176.4591661818486, 'x1': 1623.5458193969725, 'bottom': 2137.5148198553857, 'score': 0.9993484616279602}, {'type': 'table', 'x0': 213.68835895538328, 'top': 1190.0642141788564, 'x1': 879.5107371520995, 'bottom': 2126.0, 'score': 0.9968633651733398}]
2025-08-12 23:36:46,350 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:46,753 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:46,754 DEBUG    35 DiT API response for page 4: boxes=11
2025-08-12 23:36:46,761 DEBUG    35 DiT raw API boxes for page 4 (first 2): [[216.40587768554687, 1400.2961114112368, 476.0719667816162, 1448.1441416638963], [216.9397267150879, 247.24792220744683, 1609.8122831726073, 897.933308053524]]
2025-08-12 23:36:46,765 DEBUG    35 DiT raw API classes for page 4 (first 2): ['TITLE', 'TABLE']
2025-08-12 23:36:46,769 DEBUG    35 DiT raw API scores for page 4 (first 2): [0.9989734888076782, 0.9989350438117981]
2025-08-12 23:36:47,889 DEBUG    35 Applied OCR adjustment to 11 boxes
2025-08-12 23:36:47,889 DEBUG    35 DiT processed 11 boxes for page 4, first 3: [{'type': 'title', 'x0': 213.0, 'top': 1400.2961114112368, 'x1': 476.0719667816162, 'bottom': 1448.1441416638963, 'score': 0.9989734888076782}, {'type': 'table', 'x0': 216.9397267150879, 'top': 247.24792220744683, 'x1': 1609.8122831726073, 'bottom': 913.0, 'score': 0.9989350438117981}, {'type': 'title', 'x0': 210.0, 'top': 1175.0, 'x1': 499.0612700653076, 'bottom': 1221.85757251496, 'score': 0.9988129138946533}]
2025-08-12 23:36:47,892 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:48,298 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:48,299 DEBUG    35 DiT API response for page 5: boxes=10
2025-08-12 23:36:48,305 DEBUG    35 DiT raw API boxes for page 5 (first 2): [[447.2778791427612, 830.8635981133644, 1510.3559425354003, 2051.466905959109], [191.4241410064697, 682.7606486868352, 1629.2890084838866, 801.8488925365692]]
2025-08-12 23:36:48,308 DEBUG    35 DiT raw API classes for page 5 (first 2): ['FIGURE', 'TEXT']
2025-08-12 23:36:48,310 DEBUG    35 DiT raw API scores for page 5 (first 2): [0.9994725584983826, 0.9937408566474915]
2025-08-12 23:36:49,047 DEBUG    35 Applied OCR adjustment to 10 boxes
2025-08-12 23:36:49,048 DEBUG    35 DiT processed 10 boxes for page 5, first 3: [{'type': 'figure', 'x0': 447.2778791427612, 'top': 830.8635981133644, 'x1': 1510.3559425354006, 'bottom': 2051.466905959109, 'score': 0.9994725584983826}, {'type': 'text', 'x0': 175.0, 'top': 681.0, 'x1': 1629.2890084838866, 'bottom': 801.8488925365692, 'score': 0.9937408566474915}, {'type': 'text', 'x0': 162.1946095275879, 'top': 572.0, 'x1': 1653.4300065612792, 'bottom': 653.9003594581118, 'score': 0.9932331442832947}]
2025-08-12 23:36:49,051 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:49,474 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:49,475 DEBUG    35 DiT API response for page 6: boxes=13
2025-08-12 23:36:49,478 DEBUG    35 DiT raw API boxes for page 6 (first 2): [[216.65132974624632, 1115.2795072514962, 571.6070341491699, 1161.9038475523605], [221.1711148071289, 250.12988735767124, 786.0556757354736, 300.1649390687334]]
2025-08-12 23:36:49,479 DEBUG    35 DiT raw API classes for page 6 (first 2): ['TITLE', 'TITLE']
2025-08-12 23:36:49,481 DEBUG    35 DiT raw API scores for page 6 (first 2): [0.9990631937980652, 0.9985080361366272]
2025-08-12 23:36:50,733 DEBUG    35 Applied OCR adjustment to 13 boxes
2025-08-12 23:36:50,734 DEBUG    35 DiT processed 13 boxes for page 6, first 3: [{'type': 'title', 'x0': 213.0, 'top': 1115.2795072514962, 'x1': 571.6070341491699, 'bottom': 1161.9038475523605, 'score': 0.9990631937980652}, {'type': 'title', 'x0': 213.0, 'top': 250.12988735767124, 'x1': 786.0556757354736, 'bottom': 300.1649390687334, 'score': 0.9985080361366272}, {'type': 'title', 'x0': 211.2896842575073, 'top': 768.4354949301862, 'x1': 1013.3050550842286, 'bottom': 815.4598310754654, 'score': 0.998469889163971}]
2025-08-12 23:36:50,737 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:51,129 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:51,130 DEBUG    35 DiT API response for page 7: boxes=5
2025-08-12 23:36:51,135 DEBUG    35 DiT raw API boxes for page 7 (first 2): [[220.6717969894409, 250.07573294132317, 874.7039924621581, 299.1030637051197], [224.41473495483396, 321.0968043550532, 1598.5541966247558, 531.6317839926862]]
2025-08-12 23:36:51,139 DEBUG    35 DiT raw API classes for page 7 (first 2): ['TITLE', 'TEXT']
2025-08-12 23:36:51,143 DEBUG    35 DiT raw API scores for page 7 (first 2): [0.9985296726226807, 0.9949821829795837]
2025-08-12 23:36:51,717 DEBUG    35 Applied OCR adjustment to 5 boxes
2025-08-12 23:36:51,717 DEBUG    35 DiT processed 5 boxes for page 7, first 3: [{'type': 'title', 'x0': 210.0, 'top': 250.0, 'x1': 874.7039924621581, 'bottom': 299.1030637051197, 'score': 0.9985296726226807}, {'type': 'text', 'x0': 210.0, 'top': 321.0968043550532, 'x1': 1603.0, 'bottom': 531.6317839926862, 'score': 0.9949821829795837}, {'type': 'figure', 'x0': 206.69338714599607, 'top': 98.36830658608295, 'x1': 611.0763845825195, 'bottom': 219.2751062271443, 'score': 0.9904375672340393}]
2025-08-12 23:36:51,720 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:52,135 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:52,136 DEBUG    35 DiT API response for page 8: boxes=7
2025-08-12 23:36:52,139 DEBUG    35 DiT raw API boxes for page 8 (first 2): [[210.05297607421875, 240.171488011137, 1521.2219508361816, 2049.4939951795213], [228.89299491882323, 2042.0563445395615, 1475.208146972656, 2119.9256617769283]]
2025-08-12 23:36:52,142 DEBUG    35 DiT raw API classes for page 8 (first 2): ['FIGURE', 'CAPTION']
2025-08-12 23:36:52,147 DEBUG    35 DiT raw API scores for page 8 (first 2): [0.9994809031486511, 0.9924514889717102]
2025-08-12 23:36:52,689 DEBUG    35 Applied OCR adjustment to 7 boxes
2025-08-12 23:36:52,689 DEBUG    35 DiT processed 7 boxes for page 8, first 3: [{'type': 'figure', 'x0': 210.05297607421875, 'top': 240.171488011137, 'x1': 1521.2219508361816, 'bottom': 2049.4939951795213, 'score': 0.9994809031486511}, {'type': 'figure caption', 'x0': 210.0, 'top': 2042.0, 'x1': 1481.0, 'bottom': 2119.9256617769283, 'score': 0.9924514889717102}, {'type': 'figure', 'x0': 214.06087535858154, 'top': 95.56420703644449, 'x1': 613.0, 'bottom': 220.60512396629824, 'score': 0.9867979884147644}]
2025-08-12 23:36:52,691 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:53,102 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:53,103 DEBUG    35 DiT API response for page 9: boxes=6
2025-08-12 23:36:53,106 DEBUG    35 DiT raw API boxes for page 9 (first 2): [[213.4677448272705, 1996.6443338597076, 1545.175043029785, 2073.082529920213], [233.37847663879393, 226.77113634474736, 1420.1032040405273, 1999.5911475648272]]
2025-08-12 23:36:53,110 DEBUG    35 DiT raw API classes for page 9 (first 2): ['CAPTION', 'FIGURE']
2025-08-12 23:36:53,113 DEBUG    35 DiT raw API scores for page 9 (first 2): [0.999170184135437, 0.9990966320037842]
2025-08-12 23:36:53,803 DEBUG    35 Applied OCR adjustment to 6 boxes
2025-08-12 23:36:53,803 DEBUG    35 DiT processed 6 boxes for page 9, first 3: [{'type': 'figure caption', 'x0': 212.0, 'top': 1996.6443338597076, 'x1': 1551.0, 'bottom': 2073.082529920213, 'score': 0.999170184135437}, {'type': 'figure', 'x0': 233.37847663879393, 'top': 226.77113634474736, 'x1': 1420.1032040405273, 'bottom': 1999.5911475648272, 'score': 0.9990966320037842}, {'type': 'figure', 'x0': 212.04050148010253, 'top': 98.40379885409742, 'x1': 615.3961865997314, 'bottom': 219.04819764482215, 'score': 0.9915518760681152}]
2025-08-12 23:36:53,805 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:54,210 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:54,211 DEBUG    35 DiT API response for page 10: boxes=8
2025-08-12 23:36:54,214 DEBUG    35 DiT raw API boxes for page 10 (first 2): [[226.0197176361084, 352.15847323803195, 1612.9928005981444, 1535.6200912150932], [224.92004753112792, 1666.487315076463, 1618.8812661743164, 1922.1236494348407]]
2025-08-12 23:36:54,216 DEBUG    35 DiT raw API classes for page 10 (first 2): ['FIGURE', 'TEXT']
2025-08-12 23:36:54,218 DEBUG    35 DiT raw API scores for page 10 (first 2): [0.9995262622833252, 0.9969670176506042]
2025-08-12 23:36:55,094 DEBUG    35 Applied OCR adjustment to 8 boxes
2025-08-12 23:36:55,094 DEBUG    35 DiT processed 8 boxes for page 10, first 3: [{'type': 'figure', 'x0': 226.0197176361084, 'top': 352.15847323803195, 'x1': 1612.9928005981444, 'bottom': 1535.6200912150932, 'score': 0.9995262622833252}, {'type': 'text', 'x0': 212.0, 'top': 1666.487315076463, 'x1': 1623.0, 'bottom': 1922.1236494348407, 'score': 0.9969670176506042}, {'type': 'footer', 'x0': 1475.0, 'top': 2190.0, 'x1': 1629.0, 'bottom': 2235.532034366689, 'score': 0.9953102469444275}]
2025-08-12 23:36:55,099 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:36:55,519 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:36:55,520 DEBUG    35 DiT API response for page 11: boxes=7
2025-08-12 23:36:55,525 DEBUG    35 DiT raw API boxes for page 11 (first 2): [[313.3293451690674, 331.6312671417886, 1533.1605651855468, 2034.710137549867], [221.37900321960447, 2036.0437842835772, 1549.9228918457031, 2075.2543166140294]]
2025-08-12 23:36:55,528 DEBUG    35 DiT raw API classes for page 11 (first 2): ['FIGURE', 'CAPTION']
2025-08-12 23:36:55,530 DEBUG    35 DiT raw API scores for page 11 (first 2): [0.9995912909507751, 0.9948595762252808]
2025-08-12 23:36:56,070 DEBUG    35 Applied OCR adjustment to 7 boxes
2025-08-12 23:36:56,070 DEBUG    35 DiT processed 7 boxes for page 11, first 3: [{'type': 'figure', 'x0': 313.3293451690674, 'top': 331.6312671417886, 'x1': 1533.1605651855468, 'bottom': 2034.710137549867, 'score': 0.9995912909507751}, {'type': 'figure caption', 'x0': 215.0, 'top': 2036.0437842835772, 'x1': 1553.0, 'bottom': 2075.2543166140294, 'score': 0.9948595762252808}, {'type': 'footer', 'x0': 1476.0, 'top': 2195.0, 'x1': 1628.0, 'bottom': 2237.0, 'score': 0.9938282370567322}]
2025-08-12 23:36:56,072 DEBUG    35 DiT original boxes for page 0 (before scale_factor=3 division): [{'type': 'text', 'x0': 260.061322479248, 'top': 515.1993109520446, 'x1': 1634.9172103881835, 'bottom': 817.1651429521278, 'score': 0.9993115663528442}, {'type': 'title', 'x0': 210.0, 'top': 263.84720627805024, 'x1': 947.447828979492, 'bottom': 324.4113912379488, 'score': 0.9977299571037292}]
2025-08-12 23:36:56,074 DEBUG    35 DiT scaled boxes for page 0 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9993115663528442, 'x0': 86.68710749308268, 'x1': 544.9724034627278, 'top': 171.73310365068153, 'bottom': 272.3883809840426, 'page_number': 0}, {'type': 'title', 'score': 0.9977299571037292, 'x0': 70.0, 'x1': 315.815942993164, 'top': 87.94906875935008, 'bottom': 108.1371304126496, 'page_number': 0}]
2025-08-12 23:36:56,075 DEBUG    35 Image 0 dimensions: (1838, 2376)
2025-08-12 23:36:56,076 DEBUG    35 OCR boxes sample for page 0: [{'x0': 453.0, 'x1': 541.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 1}, {'x0': 95.66666666666667, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 1}]
2025-08-12 23:36:56,081 DEBUG    35 DiT original boxes for page 1 (before scale_factor=3 division): [{'type': 'figure', 'x0': 312.4769676208496, 'top': 713.1389445852727, 'x1': 1522.5918408203124, 'bottom': 2061.8646941489365, 'score': 0.9990780353546143}, {'type': 'title', 'x0': 212.0, 'top': 519.7340113863032, 'x1': 467.7236869812011, 'bottom': 567.0422856756982, 'score': 0.9985679388046265}]
2025-08-12 23:36:56,081 DEBUG    35 DiT scaled boxes for page 1 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9990780353546143, 'x0': 104.15898920694987, 'x1': 507.5306136067708, 'top': 237.71298152842425, 'bottom': 687.2882313829788, 'page_number': 1}, {'type': 'title', 'score': 0.9985679388046265, 'x0': 70.66666666666667, 'x1': 155.90789566040038, 'top': 173.24467046210108, 'bottom': 189.01409522523272, 'page_number': 1}]
2025-08-12 23:36:56,083 DEBUG    35 Image 1 dimensions: (1838, 2376)
2025-08-12 23:36:56,085 DEBUG    35 OCR boxes sample for page 1: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 34.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.333333333333336, 'page_number': 2}, {'x0': 95.33333333333333, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 2}]
2025-08-12 23:36:56,088 DEBUG    35 DiT original boxes for page 2 (before scale_factor=3 division): [{'type': 'figure', 'x0': 209.73718200683592, 'top': 1028.3801996758646, 'x1': 1625.8117680358887, 'bottom': 1845.9809622257314, 'score': 0.9991686344146729}, {'type': 'title', 'x0': 212.0, 'top': 249.2853484458112, 'x1': 1071.9304988861084, 'bottom': 298.3886601874169, 'score': 0.9988189339637756}]
2025-08-12 23:36:56,088 DEBUG    35 DiT scaled boxes for page 2 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9991686344146729, 'x0': 69.91239400227865, 'x1': 541.9372560119629, 'top': 342.79339989195483, 'bottom': 615.3269874085771, 'page_number': 2}, {'type': 'title', 'score': 0.9988189339637756, 'x0': 70.66666666666667, 'x1': 357.31016629536947, 'top': 83.09511614860374, 'bottom': 99.46288672913897, 'page_number': 2}]
2025-08-12 23:36:56,090 DEBUG    35 Image 2 dimensions: (1838, 2376)
2025-08-12 23:36:56,092 DEBUG    35 OCR boxes sample for page 2: [{'x0': 453.0, 'x1': 541.3333333333334, 'top': 32.0, 'text': 'PintoValley Mine', 'bottom': 46.0, 'page_number': 3}, {'x0': 95.66666666666667, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 3}]
2025-08-12 23:36:56,098 DEBUG    35 DiT original boxes for page 3 (before scale_factor=3 division): [{'type': 'table', 'x0': 225.45628044128418, 'top': 292.43581179355056, 'x1': 1617.2355471801757, 'bottom': 1082.6435910488697, 'score': 0.9994533658027649}, {'type': 'table', 'x0': 936.107498550415, 'top': 1176.4591661818486, 'x1': 1623.5458193969725, 'bottom': 2137.5148198553857, 'score': 0.9993484616279602}]
2025-08-12 23:36:56,098 DEBUG    35 DiT scaled boxes for page 3 (after scale_factor=3 division): [{'type': 'table', 'score': 0.9994533658027649, 'x0': 75.15209348042806, 'x1': 539.0785157267252, 'top': 97.47860393118351, 'bottom': 360.8811970162899, 'page_number': 3}, {'type': 'table', 'score': 0.9993484616279602, 'x0': 312.0358328501383, 'x1': 541.1819397989908, 'top': 392.1530553939495, 'bottom': 712.5049399517952, 'page_number': 3}]
2025-08-12 23:36:56,100 DEBUG    35 Image 3 dimensions: (1838, 2376)
2025-08-12 23:36:56,102 DEBUG    35 OCR boxes sample for page 3: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 4}, {'x0': 117.33333333333333, 'x1': 204.66666666666666, 'top': 43.0, 'text': 'apstone', 'bottom': 62.666666666666664, 'page_number': 4}]
2025-08-12 23:36:56,116 DEBUG    35 DiT original boxes for page 4 (before scale_factor=3 division): [{'type': 'title', 'x0': 213.0, 'top': 1400.2961114112368, 'x1': 476.0719667816162, 'bottom': 1448.1441416638963, 'score': 0.9989734888076782}, {'type': 'table', 'x0': 216.9397267150879, 'top': 247.24792220744683, 'x1': 1609.8122831726073, 'bottom': 913.0, 'score': 0.9989350438117981}]
2025-08-12 23:36:56,116 DEBUG    35 DiT scaled boxes for page 4 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9989734888076782, 'x0': 71.0, 'x1': 158.69065559387207, 'top': 466.7653704704123, 'bottom': 482.71471388796544, 'page_number': 4}, {'type': 'table', 'score': 0.9989350438117981, 'x0': 72.31324223836263, 'x1': 536.6040943908691, 'top': 82.41597406914894, 'bottom': 304.3333333333333, 'page_number': 4}]
2025-08-12 23:36:56,119 DEBUG    35 Image 4 dimensions: (1838, 2376)
2025-08-12 23:36:56,120 DEBUG    35 OCR boxes sample for page 4: [{'x0': 454.0, 'x1': 540.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 5}, {'x0': 97.33333333333333, 'x1': 203.66666666666666, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 5}]
2025-08-12 23:36:56,129 DEBUG    35 DiT original boxes for page 5 (before scale_factor=3 division): [{'type': 'figure', 'x0': 447.2778791427612, 'top': 830.8635981133644, 'x1': 1510.3559425354006, 'bottom': 2051.466905959109, 'score': 0.9994725584983826}, {'type': 'text', 'x0': 175.0, 'top': 681.0, 'x1': 1629.2890084838866, 'bottom': 801.8488925365692, 'score': 0.9937408566474915}]
2025-08-12 23:36:56,129 DEBUG    35 DiT scaled boxes for page 5 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9994725584983826, 'x0': 149.0926263809204, 'x1': 503.4519808451335, 'top': 276.95453270445483, 'bottom': 683.8223019863698, 'page_number': 5}, {'type': 'text', 'score': 0.9937408566474915, 'x0': 58.333333333333336, 'x1': 543.0963361612955, 'top': 227.0, 'bottom': 267.2829641788564, 'page_number': 5}]
2025-08-12 23:36:56,132 DEBUG    35 Image 5 dimensions: (1838, 2376)
2025-08-12 23:36:56,135 DEBUG    35 OCR boxes sample for page 5: [{'x0': 440.6666666666667, 'x1': 529.6666666666666, 'top': 31.333333333333332, 'text': 'PintoValleyMine', 'bottom': 45.333333333333336, 'page_number': 6}, {'x0': 83.0, 'x1': 192.0, 'top': 43.0, 'text': 'capstone', 'bottom': 63.0, 'page_number': 6}]
2025-08-12 23:36:56,139 DEBUG    35 DiT original boxes for page 6 (before scale_factor=3 division): [{'type': 'title', 'x0': 213.0, 'top': 1115.2795072514962, 'x1': 571.6070341491699, 'bottom': 1161.9038475523605, 'score': 0.9990631937980652}, {'type': 'title', 'x0': 213.0, 'top': 250.12988735767124, 'x1': 786.0556757354736, 'bottom': 300.1649390687334, 'score': 0.9985080361366272}]
2025-08-12 23:36:56,139 DEBUG    35 DiT scaled boxes for page 6 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9990631937980652, 'x0': 71.0, 'x1': 190.5356780497233, 'top': 371.7598357504987, 'bottom': 387.3012825174535, 'page_number': 6}, {'type': 'title', 'score': 0.9985080361366272, 'x0': 71.0, 'x1': 262.0185585784912, 'top': 83.37662911922375, 'bottom': 100.0549796895778, 'page_number': 6}]
2025-08-12 23:36:56,141 DEBUG    35 Image 6 dimensions: (1838, 2376)
2025-08-12 23:36:56,142 DEBUG    35 OCR boxes sample for page 6: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 36.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 7}, {'x0': 94.66666666666667, 'x1': 203.66666666666666, 'top': 44.666666666666664, 'text': 'capstone', 'bottom': 64.66666666666667, 'page_number': 7}]
2025-08-12 23:36:56,149 DEBUG    35 DiT original boxes for page 7 (before scale_factor=3 division): [{'type': 'title', 'x0': 210.0, 'top': 250.0, 'x1': 874.7039924621581, 'bottom': 299.1030637051197, 'score': 0.9985296726226807}, {'type': 'text', 'x0': 210.0, 'top': 321.0968043550532, 'x1': 1603.0, 'bottom': 531.6317839926862, 'score': 0.9949821829795837}]
2025-08-12 23:36:56,150 DEBUG    35 DiT scaled boxes for page 7 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9985296726226807, 'x0': 70.0, 'x1': 291.56799748738604, 'top': 83.33333333333333, 'bottom': 99.7010212350399, 'page_number': 7}, {'type': 'text', 'score': 0.9949821829795837, 'x0': 70.0, 'x1': 534.3333333333334, 'top': 107.03226811835107, 'bottom': 177.21059466422875, 'page_number': 7}]
2025-08-12 23:36:56,152 DEBUG    35 Image 7 dimensions: (1838, 2376)
2025-08-12 23:36:56,155 DEBUG    35 OCR boxes sample for page 7: [{'x0': 453.0, 'x1': 541.3333333333334, 'top': 32.0, 'text': 'PintoValleyMine', 'bottom': 46.0, 'page_number': 8}, {'x0': 96.33333333333333, 'x1': 203.33333333333334, 'top': 44.0, 'text': 'capstone', 'bottom': 64.66666666666667, 'page_number': 8}]
2025-08-12 23:36:56,159 DEBUG    35 DiT original boxes for page 8 (before scale_factor=3 division): [{'type': 'figure', 'x0': 210.05297607421875, 'top': 240.171488011137, 'x1': 1521.2219508361816, 'bottom': 2049.4939951795213, 'score': 0.9994809031486511}, {'type': 'figure caption', 'x0': 210.0, 'top': 2042.0, 'x1': 1481.0, 'bottom': 2119.9256617769283, 'score': 0.9924514889717102}]
2025-08-12 23:36:56,159 DEBUG    35 DiT scaled boxes for page 8 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9994809031486511, 'x0': 70.01765869140625, 'x1': 507.07398361206054, 'top': 80.057162670379, 'bottom': 683.1646650598404, 'page_number': 8}, {'type': 'figure caption', 'score': 0.9924514889717102, 'x0': 70.0, 'x1': 493.6666666666667, 'top': 680.6666666666666, 'bottom': 706.6418872589761, 'page_number': 8}]
2025-08-12 23:36:56,161 DEBUG    35 Image 8 dimensions: (1838, 2376)
2025-08-12 23:36:56,163 DEBUG    35 OCR boxes sample for page 8: [{'x0': 453.0, 'x1': 540.3333333333334, 'top': 33.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.0, 'page_number': 9}, {'x0': 97.33333333333333, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 9}]
2025-08-12 23:36:56,174 DEBUG    35 DiT original boxes for page 9 (before scale_factor=3 division): [{'type': 'figure caption', 'x0': 212.0, 'top': 1996.6443338597076, 'x1': 1551.0, 'bottom': 2073.082529920213, 'score': 0.999170184135437}, {'type': 'figure', 'x0': 233.37847663879393, 'top': 226.77113634474736, 'x1': 1420.1032040405273, 'bottom': 1999.5911475648272, 'score': 0.9990966320037842}]
2025-08-12 23:36:56,174 DEBUG    35 DiT scaled boxes for page 9 (after scale_factor=3 division): [{'type': 'figure caption', 'score': 0.999170184135437, 'x0': 70.66666666666667, 'x1': 517.0, 'top': 665.5481112865692, 'bottom': 691.0275099734043, 'page_number': 9}, {'type': 'figure', 'score': 0.9990966320037842, 'x0': 77.79282554626464, 'x1': 473.3677346801758, 'top': 75.59037878158246, 'bottom': 666.5303825216091, 'page_number': 9}]
2025-08-12 23:36:56,177 DEBUG    35 Image 9 dimensions: (1838, 2376)
2025-08-12 23:36:56,180 DEBUG    35 OCR boxes sample for page 9: [{'x0': 453.6666666666667, 'x1': 540.3333333333334, 'top': 33.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.0, 'page_number': 10}, {'x0': 102.33333333333333, 'x1': 204.33333333333334, 'top': 42.333333333333336, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 10}]
2025-08-12 23:36:56,191 DEBUG    35 DiT original boxes for page 10 (before scale_factor=3 division): [{'type': 'figure', 'x0': 226.0197176361084, 'top': 352.15847323803195, 'x1': 1612.9928005981444, 'bottom': 1535.6200912150932, 'score': 0.9995262622833252}, {'type': 'text', 'x0': 212.0, 'top': 1666.487315076463, 'x1': 1623.0, 'bottom': 1922.1236494348407, 'score': 0.9969670176506042}]
2025-08-12 23:36:56,191 DEBUG    35 DiT scaled boxes for page 10 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9995262622833252, 'x0': 75.3399058787028, 'x1': 537.6642668660481, 'top': 117.38615774601065, 'bottom': 511.8733637383644, 'page_number': 10}, {'type': 'text', 'score': 0.9969670176506042, 'x0': 70.66666666666667, 'x1': 541.0, 'top': 555.4957716921543, 'bottom': 640.7078831449469, 'page_number': 10}]
2025-08-12 23:36:56,194 DEBUG    35 Image 10 dimensions: (1838, 2376)
2025-08-12 23:36:56,196 DEBUG    35 OCR boxes sample for page 10: [{'x0': 453.6666666666667, 'x1': 539.3333333333334, 'top': 33.666666666666664, 'text': 'PintoValleyMine', 'bottom': 45.333333333333336, 'page_number': 11}, {'x0': 99.0, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 11}]
2025-08-12 23:36:56,209 DEBUG    35 DiT original boxes for page 11 (before scale_factor=3 division): [{'type': 'figure', 'x0': 313.3293451690674, 'top': 331.6312671417886, 'x1': 1533.1605651855468, 'bottom': 2034.710137549867, 'score': 0.9995912909507751}, {'type': 'figure caption', 'x0': 215.0, 'top': 2036.0437842835772, 'x1': 1553.0, 'bottom': 2075.2543166140294, 'score': 0.9948595762252808}]
2025-08-12 23:36:56,210 DEBUG    35 DiT scaled boxes for page 11 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9995912909507751, 'x0': 104.4431150563558, 'x1': 511.0535217285156, 'top': 110.54375571392954, 'bottom': 678.2367125166223, 'page_number': 11}, {'type': 'figure caption', 'score': 0.9948595762252808, 'x0': 71.66666666666667, 'x1': 517.6666666666666, 'top': 678.6812614278591, 'bottom': 691.7514388713431, 'page_number': 11}]
2025-08-12 23:36:56,211 DEBUG    35 Image 11 dimensions: (1838, 2376)
2025-08-12 23:36:56,213 DEBUG    35 OCR boxes sample for page 11: [{'x0': 453.0, 'x1': 540.3333333333334, 'top': 33.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.0, 'page_number': 12}, {'x0': 97.33333333333333, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 12}]
2025-08-12 23:36:56,248 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.63, progress_msg: 23:36:56 Page(1~13): Layout analysis (15.14s)
2025-08-12 23:36:56,658 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.65, progress_msg: 23:36:56 Page(1~13): Table analysis (0.40s)
2025-08-12 23:36:56,671 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.67, progress_msg: 23:36:56 Page(1~13): Text merged (0.00s)
2025-08-12 23:36:57,793 INFO     35 layouts cost: 27.45774519292172s
2025-08-12 23:36:57,879 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.8, progress_msg: 23:36:57 Page(1~13): Finish parsing.
2025-08-12 23:36:58,072 INFO     35 naive_merge(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf): 0.19281132391188294
2025-08-12 23:36:58,074 INFO     35 Chunking(28.662127861985937) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf done
2025-08-12 23:36:58,348 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.1, progress_msg: 23:36:58 Page(13~25): Start to parse.
2025-08-12 23:36:58,364 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:36:58,395 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:36:58,416 INFO     35 load_model /ragflow/rag/res/deepdoc/layout.onnx reuses cached model
2025-08-12 23:36:58,453 INFO     35 load_model /ragflow/rag/res/deepdoc/tsr.onnx reuses cached model
2025-08-12 23:36:58,549 INFO     35 MINIO PUT(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf) cost 0.473 s
2025-08-12 23:36:58,566 INFO     35 Build document Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf: 29.15s
2025-08-12 23:36:58,599 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:36:58 Page(13~25): OCR started
2025-08-12 23:36:58,642 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:36:58 Page(1~13): Generate 27 chunks
2025-08-12 23:36:59,339 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 33
2025-08-12 23:37:00,483 INFO     35 __images__ dedupe_chars cost 1.8827470729593188s
2025-08-12 23:37:00,487 WARNING  35 Miss outlines
2025-08-12 23:37:00,683 INFO     35 __ocr detecting boxes of a image cost (0.19281364302150905s)
2025-08-12 23:37:00,683 INFO     35 __ocr sorting 0 chars cost 0.00029469002038240433s
2025-08-12 23:37:00,892 INFO     35 __ocr recognize 8 boxes cost 0.20760775392409414s
2025-08-12 23:37:01,075 INFO     35 __ocr detecting boxes of a image cost (0.18154728901572526s)
2025-08-12 23:37:01,077 INFO     35 __ocr sorting 0 chars cost 0.0010945090325549245s
2025-08-12 23:37:02,256 INFO     35 __ocr recognize 76 boxes cost 1.178274794947356s
2025-08-12 23:37:02,436 INFO     35 __ocr detecting boxes of a image cost (0.17815030191559345s)
2025-08-12 23:37:02,437 INFO     35 __ocr sorting 0 chars cost 0.0006118830060586333s
2025-08-12 23:37:03,005 INFO     35 __ocr recognize 27 boxes cost 0.567010005004704s
2025-08-12 23:37:03,168 INFO     35 __ocr detecting boxes of a image cost (0.16253591398708522s)
2025-08-12 23:37:03,169 INFO     35 __ocr sorting 0 chars cost 0.0005487049929797649s
2025-08-12 23:37:03,241 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 378
2025-08-12 23:37:03,257 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.7074074074074074, progress_msg: 
2025-08-12 23:37:03,919 INFO     35 __ocr recognize 48 boxes cost 0.7492260009748861s
2025-08-12 23:37:04,079 INFO     35 __ocr detecting boxes of a image cost (0.15899473894387484s)
2025-08-12 23:37:04,080 INFO     35 __ocr sorting 0 chars cost 0.0004909810377284884s
2025-08-12 23:37:04,738 INFO     35 __ocr recognize 30 boxes cost 0.6564632969675586s
2025-08-12 23:37:04,909 INFO     35 __ocr detecting boxes of a image cost (0.17033344693481922s)
2025-08-12 23:37:04,910 INFO     35 __ocr sorting 0 chars cost 0.0006131150294095278s
2025-08-12 23:37:05,892 INFO     35 __ocr recognize 40 boxes cost 0.9810311860637739s
2025-08-12 23:37:05,899 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.3, progress_msg: 
2025-08-12 23:37:06,080 INFO     35 __ocr detecting boxes of a image cost (0.18029655294958502s)
2025-08-12 23:37:06,081 INFO     35 __ocr sorting 0 chars cost 0.0007352590328082442s
2025-08-12 23:37:06,822 INFO     35 __ocr recognize 43 boxes cost 0.7369491119170561s
2025-08-12 23:37:06,979 INFO     35 __ocr detecting boxes of a image cost (0.1556043280288577s)
2025-08-12 23:37:06,980 INFO     35 __ocr sorting 0 chars cost 0.0004595440113916993s
2025-08-12 23:37:07,321 INFO     35 __ocr recognize 25 boxes cost 0.3395661599934101s
2025-08-12 23:37:07,492 INFO     35 __ocr detecting boxes of a image cost (0.16971698496490717s)
2025-08-12 23:37:07,493 INFO     35 __ocr sorting 0 chars cost 0.0006960290484130383s
2025-08-12 23:37:08,149 INFO     35 __ocr recognize 32 boxes cost 0.6530781449982896s
2025-08-12 23:37:08,325 INFO     35 __ocr detecting boxes of a image cost (0.17439451103564352s)
2025-08-12 23:37:08,327 INFO     35 __ocr sorting 0 chars cost 0.0007522109663113952s
2025-08-12 23:37:09,142 INFO     35 __ocr recognize 25 boxes cost 0.8134642828954384s
2025-08-12 23:37:09,337 INFO     35 __ocr detecting boxes of a image cost (0.19420661695767194s)
2025-08-12 23:37:09,339 INFO     35 __ocr sorting 0 chars cost 0.0013059839839115739s
2025-08-12 23:37:09,612 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 3945
2025-08-12 23:37:09,623 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.825925925925926, progress_msg: 
2025-08-12 23:37:09,626 INFO     35 Embedding chunks (10.97s)
2025-08-12 23:37:09,638 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:37:09 Page(1~13): Embedding chunks (10.97s)
2025-08-12 23:37:09,705 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.058s]
2025-08-12 23:37:09,721 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 0.8037037037037037, progress_msg: 
2025-08-12 23:37:09,739 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.009s]
2025-08-12 23:37:09,766 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.012s]
2025-08-12 23:37:09,788 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.007s]
2025-08-12 23:37:09,810 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.007s]
2025-08-12 23:37:09,830 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.008s]
2025-08-12 23:37:09,848 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.007s]
2025-08-12 23:37:09,853 INFO     35 Indexing doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(0-12), chunks(27), elapsed: 0.21
2025-08-12 23:37:09,889 INFO     35 set_progress(17f96830779211f0ac8e02420ae90a05), progress: 1.0, progress_msg: 23:37:09 Page(1~13): Indexing done (0.23s). Task done (48.91s)
2025-08-12 23:37:09,889 INFO     35 Chunk doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(0-12), chunks(27), token(4356), elapsed:48.91
2025-08-12 23:37:09,895 INFO     35 handle_task done for task {"id": "17f96830779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 0, "to_page": 12, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979979, "task_type": ""}
2025-08-12 23:37:10,534 INFO     35 __ocr recognize 105 boxes cost 1.1929162089945748s
2025-08-12 23:37:10,702 INFO     35 __ocr detecting boxes of a image cost (0.16730059497058392s)
2025-08-12 23:37:10,703 INFO     35 __ocr sorting 0 chars cost 0.0005188339855521917s
2025-08-12 23:37:12,044 INFO     35 __ocr recognize 45 boxes cost 1.3372877679066733s
2025-08-12 23:37:12,053 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.6, progress_msg: 
2025-08-12 23:37:12,054 INFO     35 __images__ 12 pages cost 11.565847796970047s
2025-08-12 23:37:12,063 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:37:12 Page(13~25): OCR finished (13.49s)
2025-08-12 23:37:12,063 INFO     35 OCR(12~24): 13.50s
2025-08-12 23:37:12,065 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:12,454 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:12,455 DEBUG    35 DiT API response for page 0: boxes=5
2025-08-12 23:37:12,456 DEBUG    35 DiT raw API boxes for page 0 (first 2): [[186.59930686950682, 317.28748207903925, 1313.1334326171875, 351.81061570187836], [218.38383251190186, 248.77266092503328, 570.032656288147, 306.34948211020617]]
2025-08-12 23:37:12,458 DEBUG    35 DiT raw API classes for page 0 (first 2): ['TEXT', 'TITLE']
2025-08-12 23:37:12,460 DEBUG    35 DiT raw API scores for page 0 (first 2): [0.9971700310707092, 0.9811182618141174]
2025-08-12 23:37:12,463 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:37:12,481 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:37:12,843 DEBUG    35 Applied OCR adjustment to 5 boxes
2025-08-12 23:37:12,843 DEBUG    35 DiT processed 5 boxes for page 0, first 3: [{'type': 'text', 'x0': 186.59930686950682, 'top': 314.0, 'x1': 1316.0, 'bottom': 356.0, 'score': 0.9971700310707092}, {'type': 'title', 'x0': 212.0, 'top': 248.77266092503328, 'x1': 570.032656288147, 'bottom': 307.0, 'score': 0.9811182618141174}, {'type': 'figure', 'x0': 203.31251857757567, 'top': 104.47175825403093, 'x1': 613.0, 'bottom': 217.8002092077377, 'score': 0.9794432520866394}]
2025-08-12 23:37:12,844 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:13,251 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:13,252 DEBUG    35 DiT API response for page 1: boxes=12
2025-08-12 23:37:13,254 DEBUG    35 DiT raw API boxes for page 1 (first 2): [[218.51331579208372, 249.07242990047376, 631.3596327972411, 299.08135986328125], [216.64903350830076, 1118.9657242145945, 1608.4010258483886, 1717.9311003989362]]
2025-08-12 23:37:13,256 DEBUG    35 DiT raw API classes for page 1 (first 2): ['TITLE', 'TABLE']
2025-08-12 23:37:13,258 DEBUG    35 DiT raw API scores for page 1 (first 2): [0.9988934397697449, 0.9982345104217529]
2025-08-12 23:37:14,412 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:37:14.409+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 3, "lag": 0, "done": 1, "failed": 0, "current": {"17f96a1a779211f0ac8e02420ae90a05": {"id": "17f96a1a779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 12, "to_page": 24, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979980, "task_type": ""}, "17f96a9c779211f0ac8e02420ae90a05": {"id": "17f96a9c779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 24, "to_page": 36, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979981, "task_type": ""}, "17f96b14779211f0ac8e02420ae90a05": {"id": "17f96b14779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 36, "to_page": 45, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979982, "task_type": ""}}}
2025-08-12 23:37:14,496 DEBUG    35 Applied OCR adjustment to 12 boxes
2025-08-12 23:37:14,497 DEBUG    35 DiT processed 12 boxes for page 1, first 3: [{'type': 'title', 'x0': 210.0, 'top': 249.07242990047376, 'x1': 631.3596327972411, 'bottom': 299.08135986328125, 'score': 0.9988934397697449}, {'type': 'table', 'x0': 216.64903350830076, 'top': 1118.9657242145945, 'x1': 1608.4010258483886, 'bottom': 1717.9311003989362, 'score': 0.9982345104217529}, {'type': 'text', 'x0': 210.0, 'top': 807.9535275723073, 'x1': 1622.815510559082, 'bottom': 1056.9552885014962, 'score': 0.9967425465583801}]
2025-08-12 23:37:14,500 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:14,907 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:14,908 DEBUG    35 DiT API response for page 2: boxes=10
2025-08-12 23:37:14,911 DEBUG    35 DiT raw API boxes for page 2 (first 2): [[271.80642181396485, 321.8576309528757, 1685.3518507385254, 1246.9016321060506], [216.99700996398926, 1395.6857806266623, 902.5440019989013, 1444.34866917387]]
2025-08-12 23:37:14,915 DEBUG    35 DiT raw API classes for page 2 (first 2): ['FIGURE', 'TITLE']
2025-08-12 23:37:14,919 DEBUG    35 DiT raw API scores for page 2 (first 2): [0.9990501999855042, 0.9987032413482666]
2025-08-12 23:37:15,593 DEBUG    35 Applied OCR adjustment to 10 boxes
2025-08-12 23:37:15,593 DEBUG    35 DiT processed 10 boxes for page 2, first 3: [{'type': 'figure', 'x0': 271.80642181396485, 'top': 321.8576309528757, 'x1': 1685.3518507385254, 'bottom': 1246.9016321060506, 'score': 0.9990501999855042}, {'type': 'title', 'x0': 215.0, 'top': 1395.6857806266623, 'x1': 902.5440019989013, 'bottom': 1444.34866917387, 'score': 0.9987032413482666}, {'type': 'text', 'x0': 210.0, 'top': 1471.426648728391, 'x1': 1618.0, 'bottom': 1589.4624490940826, 'score': 0.9981459379196167}]
2025-08-12 23:37:15,595 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:16,004 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:16,005 DEBUG    35 DiT API response for page 3: boxes=9
2025-08-12 23:37:16,008 DEBUG    35 DiT raw API boxes for page 3 (first 2): [[291.6719300079346, 904.0141705452129, 1608.9278634643554, 1434.744421126995], [215.04655740737914, 830.254908784907, 767.3002846527099, 868.5887321309841]]
2025-08-12 23:37:16,010 DEBUG    35 DiT raw API classes for page 3 (first 2): ['LIST', 'TEXT']
2025-08-12 23:37:16,013 DEBUG    35 DiT raw API scores for page 3 (first 2): [0.9984556436538696, 0.9968084692955017]
2025-08-12 23:37:16,834 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:37:16,835 DEBUG    35 DiT processed 9 boxes for page 3, first 3: [{'type': 'text', 'x0': 262.0, 'top': 904.0141705452129, 'x1': 1608.9278634643554, 'bottom': 1434.744421126995, 'score': 0.9984556436538696}, {'type': 'text', 'x0': 215.0, 'top': 830.254908784907, 'x1': 767.3002846527099, 'bottom': 869.0, 'score': 0.9968084692955017}, {'type': 'text', 'x0': 200.59640213012693, 'top': 1606.0, 'x1': 1601.5866329956054, 'bottom': 1685.0, 'score': 0.9958723187446594}]
2025-08-12 23:37:16,839 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:17,249 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:17,250 DEBUG    35 DiT API response for page 4: boxes=9
2025-08-12 23:37:17,252 DEBUG    35 DiT raw API boxes for page 4 (first 2): [[920.8758335876464, 249.6146577875665, 1625.7736259460448, 716.2601604055852], [217.00002487182616, 1712.198621384641, 1565.1561694335937, 1788.2665548121677]]
2025-08-12 23:37:17,255 DEBUG    35 DiT raw API classes for page 4 (first 2): ['FIGURE', 'CAPTION']
2025-08-12 23:37:17,257 DEBUG    35 DiT raw API scores for page 4 (first 2): [0.9989168643951416, 0.998507559299469]
2025-08-12 23:37:17,997 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:37:17,997 DEBUG    35 DiT processed 9 boxes for page 4, first 3: [{'type': 'figure', 'x0': 920.8758335876464, 'top': 249.6146577875665, 'x1': 1625.7736259460448, 'bottom': 716.2601604055852, 'score': 0.9989168643951416}, {'type': 'figure caption', 'x0': 210.0, 'top': 1710.0, 'x1': 1566.0, 'bottom': 1789.0, 'score': 0.998507559299469}, {'type': 'figure', 'x0': 224.2826048660278, 'top': 243.5900359458112, 'x1': 923.0188020324706, 'bottom': 713.3177646068817, 'score': 0.9982151985168457}]
2025-08-12 23:37:17,998 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:18,420 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:18,421 DEBUG    35 DiT API response for page 5: boxes=8
2025-08-12 23:37:18,424 DEBUG    35 DiT raw API boxes for page 5 (first 2): [[227.77839050292968, 1128.7979502576463, 1601.3938191223144, 1335.2662119763963], [224.80583160400388, 1355.5396338929522, 1604.717509613037, 1608.975991107048]]
2025-08-12 23:37:18,427 DEBUG    35 DiT raw API classes for page 5 (first 2): ['TEXT', 'TEXT']
2025-08-12 23:37:18,430 DEBUG    35 DiT raw API scores for page 5 (first 2): [0.9982072114944458, 0.9980539083480835]
2025-08-12 23:37:19,426 DEBUG    35 Applied OCR adjustment to 8 boxes
2025-08-12 23:37:19,427 DEBUG    35 DiT processed 8 boxes for page 5, first 3: [{'type': 'text', 'x0': 212.0, 'top': 1128.7979502576463, 'x1': 1606.0, 'bottom': 1336.0, 'score': 0.9982072114944458}, {'type': 'text', 'x0': 207.0, 'top': 1355.5396338929522, 'x1': 1611.0, 'bottom': 1608.975991107048, 'score': 0.9980539083480835}, {'type': 'figure', 'x0': 237.7183312988281, 'top': 275.318002254405, 'x1': 1508.3844732666014, 'bottom': 1009.5601988447474, 'score': 0.9976111650466919}]
2025-08-12 23:37:19,429 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:19,848 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:19,849 DEBUG    35 DiT API response for page 6: boxes=10
2025-08-12 23:37:19,855 DEBUG    35 DiT raw API boxes for page 6 (first 2): [[217.79953636169432, 1537.887949842088, 1629.4185794067382, 1836.0652167137634], [1002.3788187408446, 241.32231756981386, 1566.2656555175781, 1278.3071964345081]]
2025-08-12 23:37:19,860 DEBUG    35 DiT raw API classes for page 6 (first 2): ['TEXT', 'FIGURE']
2025-08-12 23:37:19,863 DEBUG    35 DiT raw API scores for page 6 (first 2): [0.9986768364906311, 0.9985401630401611]
2025-08-12 23:37:20,664 DEBUG    35 Applied OCR adjustment to 10 boxes
2025-08-12 23:37:20,665 DEBUG    35 DiT processed 10 boxes for page 6, first 3: [{'type': 'text', 'x0': 212.0, 'top': 1534.0, 'x1': 1629.418579406738, 'bottom': 1836.0652167137634, 'score': 0.9986768364906311}, {'type': 'figure', 'x0': 999.0, 'top': 241.32231756981386, 'x1': 1566.2656555175781, 'bottom': 1278.3071964345081, 'score': 0.9985401630401611}, {'type': 'figure', 'x0': 225.73379219055175, 'top': 239.66981019365028, 'x1': 966.989409866333, 'bottom': 1274.0302422706118, 'score': 0.9984715580940247}]
2025-08-12 23:37:20,670 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:21,070 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:21,071 DEBUG    35 DiT API response for page 7: boxes=5
2025-08-12 23:37:21,074 DEBUG    35 DiT raw API boxes for page 7 (first 2): [[208.53872108459473, 285.74741704413236, 1695.4119671630858, 1416.2915974069151], [1459.7481202697752, 2194.9317549035904, 1623.753217010498, 2235.478879238697]]
2025-08-12 23:37:21,078 DEBUG    35 DiT raw API classes for page 7 (first 2): ['FIGURE', 'FOOTER']
2025-08-12 23:37:21,083 DEBUG    35 DiT raw API scores for page 7 (first 2): [0.9985477328300476, 0.9959463477134705]
2025-08-12 23:37:21,588 DEBUG    35 Applied OCR adjustment to 5 boxes
2025-08-12 23:37:21,588 DEBUG    35 DiT processed 5 boxes for page 7, first 3: [{'type': 'figure', 'x0': 208.53872108459473, 'top': 285.74741704413236, 'x1': 1695.4119671630858, 'bottom': 1416.2915974069151, 'score': 0.9985477328300476}, {'type': 'footer', 'x0': 1456.0, 'top': 2194.9317549035904, 'x1': 1626.0, 'bottom': 2237.0, 'score': 0.9959463477134705}, {'type': 'figure', 'x0': 204.0211270904541, 'top': 102.33184359936006, 'x1': 612.1923912811279, 'bottom': 219.00384326691326, 'score': 0.9947068095207214}]
2025-08-12 23:37:21,593 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:22,006 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:22,007 DEBUG    35 DiT API response for page 8: boxes=6
2025-08-12 23:37:22,009 DEBUG    35 DiT raw API boxes for page 8 (first 2): [[213.54599220275878, 267.731790745512, 1695.8447116088867, 1140.46998888381], [210.97220745086668, 99.773332311752, 615.20947265625, 217.9003308884641]]
2025-08-12 23:37:22,013 DEBUG    35 DiT raw API classes for page 8 (first 2): ['FIGURE', 'LOGO']
2025-08-12 23:37:22,018 DEBUG    35 DiT raw API scores for page 8 (first 2): [0.9982872605323792, 0.9939536452293396]
2025-08-12 23:37:22,768 DEBUG    35 Applied OCR adjustment to 6 boxes
2025-08-12 23:37:22,769 DEBUG    35 DiT processed 6 boxes for page 8, first 3: [{'type': 'figure', 'x0': 213.54599220275878, 'top': 267.731790745512, 'x1': 1695.8447116088867, 'bottom': 1148.0, 'score': 0.9982872605323792}, {'type': 'figure', 'x0': 210.97220745086668, 'top': 99.773332311752, 'x1': 615.20947265625, 'bottom': 217.9003308884641, 'score': 0.9939536452293396}, {'type': 'text', 'x0': 210.0, 'top': 1290.3987959192154, 'x1': 1558.0, 'bottom': 1371.0, 'score': 0.9923181533813477}]
2025-08-12 23:37:22,772 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:23,190 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:23,191 DEBUG    35 DiT API response for page 9: boxes=8
2025-08-12 23:37:23,192 DEBUG    35 DiT raw API boxes for page 9 (first 2): [[227.2030339050293, 1770.690860829455, 1614.1558538818358, 2124.206122215758], [220.17472282409668, 242.13423059341758, 1710.254007873535, 1316.8668238863033]]
2025-08-12 23:37:23,195 DEBUG    35 DiT raw API classes for page 9 (first 2): ['TEXT', 'FIGURE']
2025-08-12 23:37:23,197 DEBUG    35 DiT raw API scores for page 9 (first 2): [0.998759388923645, 0.9986159801483154]
2025-08-12 23:37:24,051 DEBUG    35 Applied OCR adjustment to 8 boxes
2025-08-12 23:37:24,052 DEBUG    35 DiT processed 8 boxes for page 9, first 3: [{'type': 'text', 'x0': 210.0, 'top': 1770.690860829455, 'x1': 1614.1558538818358, 'bottom': 2124.206122215758, 'score': 0.998759388923645}, {'type': 'figure', 'x0': 220.17472282409668, 'top': 242.13423059341758, 'x1': 1710.254007873535, 'bottom': 1316.8668238863033, 'score': 0.9986159801483154}, {'type': 'text', 'x0': 207.0, 'top': 1473.0, 'x1': 1611.5624722290038, 'bottom': 1688.0, 'score': 0.9974738955497742}]
2025-08-12 23:37:24,053 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:24,475 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:24,476 DEBUG    35 DiT API response for page 10: boxes=14
2025-08-12 23:37:24,479 DEBUG    35 DiT raw API boxes for page 10 (first 2): [[215.4374261856079, 1003.1162265209442, 554.8352070236206, 1050.965939785572], [235.107561340332, 1624.02997215758, 1633.050070953369, 2139.6521048038567]]
2025-08-12 23:37:24,482 DEBUG    35 DiT raw API classes for page 10 (first 2): ['TITLE', 'TABLE']
2025-08-12 23:37:24,484 DEBUG    35 DiT raw API scores for page 10 (first 2): [0.9990160465240479, 0.9983855485916138]
2025-08-12 23:37:25,800 DEBUG    35 Applied OCR adjustment to 14 boxes
2025-08-12 23:37:25,800 DEBUG    35 DiT processed 14 boxes for page 10, first 3: [{'type': 'title', 'x0': 213.0, 'top': 1003.1162265209442, 'x1': 554.8352070236206, 'bottom': 1050.965939785572, 'score': 0.9990160465240479}, {'type': 'table', 'x0': 215.0, 'top': 1604.0, 'x1': 1633.050070953369, 'bottom': 2139.6521048038567, 'score': 0.9983855485916138}, {'type': 'text', 'x0': 210.0, 'top': 1078.8957337724403, 'x1': 1586.0, 'bottom': 1203.0, 'score': 0.9982944130897522}]
2025-08-12 23:37:25,801 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:26,225 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:26,226 DEBUG    35 DiT API response for page 11: boxes=16
2025-08-12 23:37:26,229 DEBUG    35 DiT raw API boxes for page 11 (first 2): [[212.88935579299925, 1847.313486951463, 649.1963183593749, 1883.2996488530587], [215.96077562332152, 1335.3311481881651, 684.9761928558349, 1373.1641352227396]]
2025-08-12 23:37:26,231 DEBUG    35 DiT raw API classes for page 11 (first 2): ['TITLE', 'TITLE']
2025-08-12 23:37:26,233 DEBUG    35 DiT raw API scores for page 11 (first 2): [0.9986844658851624, 0.9982643723487854]
2025-08-12 23:37:27,758 DEBUG    35 Applied OCR adjustment to 16 boxes
2025-08-12 23:37:27,759 DEBUG    35 DiT processed 16 boxes for page 11, first 3: [{'type': 'title', 'x0': 212.88935579299925, 'top': 1847.313486951463, 'x1': 649.1963183593749, 'bottom': 1888.0, 'score': 0.9986844658851624}, {'type': 'title', 'x0': 212.0, 'top': 1335.3311481881651, 'x1': 684.9761928558349, 'bottom': 1374.0, 'score': 0.9982643723487854}, {'type': 'title', 'x0': 212.0, 'top': 1677.921558136636, 'x1': 670.4359233856201, 'bottom': 1720.0, 'score': 0.9982289671897888}]
2025-08-12 23:37:27,761 DEBUG    35 DiT original boxes for page 0 (before scale_factor=3 division): [{'type': 'text', 'x0': 186.59930686950682, 'top': 314.0, 'x1': 1316.0, 'bottom': 356.0, 'score': 0.9971700310707092}, {'type': 'title', 'x0': 212.0, 'top': 248.77266092503328, 'x1': 570.032656288147, 'bottom': 307.0, 'score': 0.9811182618141174}]
2025-08-12 23:37:27,766 DEBUG    35 DiT scaled boxes for page 0 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9971700310707092, 'x0': 62.199768956502275, 'x1': 438.6666666666667, 'top': 104.66666666666667, 'bottom': 118.66666666666667, 'page_number': 0}, {'type': 'title', 'score': 0.9811182618141174, 'x0': 70.66666666666667, 'x1': 190.01088542938234, 'top': 82.92422030834443, 'bottom': 102.33333333333333, 'page_number': 0}]
2025-08-12 23:37:27,767 DEBUG    35 Image 0 dimensions: (1838, 2376)
2025-08-12 23:37:27,771 DEBUG    35 OCR boxes sample for page 0: [{'x0': 453.0, 'x1': 541.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 1}, {'x0': 98.66666666666667, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 1}]
2025-08-12 23:37:27,776 DEBUG    35 DiT original boxes for page 1 (before scale_factor=3 division): [{'type': 'title', 'x0': 210.0, 'top': 249.07242990047376, 'x1': 631.3596327972411, 'bottom': 299.08135986328125, 'score': 0.9988934397697449}, {'type': 'table', 'x0': 216.64903350830076, 'top': 1118.9657242145945, 'x1': 1608.4010258483886, 'bottom': 1717.9311003989362, 'score': 0.9982345104217529}]
2025-08-12 23:37:27,777 DEBUG    35 DiT scaled boxes for page 1 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9988934397697449, 'x0': 70.0, 'x1': 210.45321093241373, 'top': 83.02414330015792, 'bottom': 99.69378662109375, 'page_number': 1}, {'type': 'table', 'score': 0.9982345104217529, 'x0': 72.21634450276692, 'x1': 536.1336752827962, 'top': 372.98857473819817, 'bottom': 572.6437001329788, 'page_number': 1}]
2025-08-12 23:37:27,783 DEBUG    35 Image 1 dimensions: (1838, 2376)
2025-08-12 23:37:27,788 DEBUG    35 OCR boxes sample for page 1: [{'x0': 453.0, 'x1': 541.3333333333334, 'top': 33.0, 'text': 'PintoValley Mine', 'bottom': 46.0, 'page_number': 2}, {'x0': 121.66666666666667, 'x1': 203.0, 'top': 43.666666666666664, 'text': 'pstone', 'bottom': 61.0, 'page_number': 2}]
2025-08-12 23:37:27,800 DEBUG    35 DiT original boxes for page 2 (before scale_factor=3 division): [{'type': 'figure', 'x0': 271.80642181396485, 'top': 321.8576309528757, 'x1': 1685.3518507385254, 'bottom': 1246.9016321060506, 'score': 0.9990501999855042}, {'type': 'title', 'x0': 215.0, 'top': 1395.6857806266623, 'x1': 902.5440019989013, 'bottom': 1444.34866917387, 'score': 0.9987032413482666}]
2025-08-12 23:37:27,801 DEBUG    35 DiT scaled boxes for page 2 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9990501999855042, 'x0': 90.60214060465495, 'x1': 561.7839502461751, 'top': 107.2858769842919, 'bottom': 415.6338773686835, 'page_number': 2}, {'type': 'title', 'score': 0.9987032413482666, 'x0': 71.66666666666667, 'x1': 300.84800066630044, 'top': 465.2285935422208, 'bottom': 481.44955639128995, 'page_number': 2}]
2025-08-12 23:37:27,804 DEBUG    35 Image 2 dimensions: (1838, 2376)
2025-08-12 23:37:27,807 DEBUG    35 OCR boxes sample for page 2: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 34.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.333333333333336, 'page_number': 3}, {'x0': 97.33333333333333, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 3}]
2025-08-12 23:37:27,813 DEBUG    35 DiT original boxes for page 3 (before scale_factor=3 division): [{'type': 'text', 'x0': 262.0, 'top': 904.0141705452129, 'x1': 1608.9278634643554, 'bottom': 1434.744421126995, 'score': 0.9984556436538696}, {'type': 'text', 'x0': 215.0, 'top': 830.254908784907, 'x1': 767.3002846527099, 'bottom': 869.0, 'score': 0.9968084692955017}]
2025-08-12 23:37:27,817 DEBUG    35 DiT scaled boxes for page 3 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9984556436538696, 'x0': 87.33333333333333, 'x1': 536.3092878214518, 'top': 301.3380568484043, 'bottom': 478.24814037566495, 'page_number': 3}, {'type': 'text', 'score': 0.9968084692955017, 'x0': 71.66666666666667, 'x1': 255.7667615509033, 'top': 276.75163626163567, 'bottom': 289.6666666666667, 'page_number': 3}]
2025-08-12 23:37:27,821 DEBUG    35 Image 3 dimensions: (1838, 2376)
2025-08-12 23:37:27,827 DEBUG    35 OCR boxes sample for page 3: [{'x0': 454.0, 'x1': 540.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 4}, {'x0': 115.66666666666667, 'x1': 203.0, 'top': 43.666666666666664, 'text': 'apstone', 'bottom': 61.0, 'page_number': 4}]
2025-08-12 23:37:27,842 DEBUG    35 DiT original boxes for page 4 (before scale_factor=3 division): [{'type': 'figure', 'x0': 920.8758335876464, 'top': 249.6146577875665, 'x1': 1625.7736259460448, 'bottom': 716.2601604055852, 'score': 0.9989168643951416}, {'type': 'figure caption', 'x0': 210.0, 'top': 1710.0, 'x1': 1566.0, 'bottom': 1789.0, 'score': 0.998507559299469}]
2025-08-12 23:37:27,843 DEBUG    35 DiT scaled boxes for page 4 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9989168643951416, 'x0': 306.95861119588216, 'x1': 541.924541982015, 'top': 83.20488592918883, 'bottom': 238.75338680186175, 'page_number': 4}, {'type': 'figure caption', 'score': 0.998507559299469, 'x0': 70.0, 'x1': 522.0, 'top': 570.0, 'bottom': 596.3333333333334, 'page_number': 4}]
2025-08-12 23:37:27,847 DEBUG    35 Image 4 dimensions: (1838, 2376)
2025-08-12 23:37:27,849 DEBUG    35 OCR boxes sample for page 4: [{'x0': 453.6666666666667, 'x1': 541.0, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 5}, {'x0': 95.66666666666667, 'x1': 204.33333333333334, 'top': 44.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 5}]
2025-08-12 23:37:27,858 DEBUG    35 DiT original boxes for page 5 (before scale_factor=3 division): [{'type': 'text', 'x0': 212.0, 'top': 1128.7979502576463, 'x1': 1606.0, 'bottom': 1336.0, 'score': 0.9982072114944458}, {'type': 'text', 'x0': 207.0, 'top': 1355.5396338929522, 'x1': 1611.0, 'bottom': 1608.975991107048, 'score': 0.9980539083480835}]
2025-08-12 23:37:27,858 DEBUG    35 DiT scaled boxes for page 5 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9982072114944458, 'x0': 70.66666666666667, 'x1': 535.3333333333334, 'top': 376.26598341921544, 'bottom': 445.3333333333333, 'page_number': 5}, {'type': 'text', 'score': 0.9980539083480835, 'x0': 69.0, 'x1': 537.0, 'top': 451.84654463098406, 'bottom': 536.325330369016, 'page_number': 5}]
2025-08-12 23:37:27,861 DEBUG    35 Image 5 dimensions: (1838, 2376)
2025-08-12 23:37:27,865 DEBUG    35 OCR boxes sample for page 5: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 6}, {'x0': 94.66666666666667, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 6}]
2025-08-12 23:37:27,877 DEBUG    35 DiT original boxes for page 6 (before scale_factor=3 division): [{'type': 'text', 'x0': 212.0, 'top': 1534.0, 'x1': 1629.418579406738, 'bottom': 1836.0652167137634, 'score': 0.9986768364906311}, {'type': 'figure', 'x0': 999.0, 'top': 241.32231756981386, 'x1': 1566.2656555175781, 'bottom': 1278.3071964345081, 'score': 0.9985401630401611}]
2025-08-12 23:37:27,878 DEBUG    35 DiT scaled boxes for page 6 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9986768364906311, 'x0': 70.66666666666667, 'x1': 543.1395264689127, 'top': 511.3333333333333, 'bottom': 612.0217389045878, 'page_number': 6}, {'type': 'figure', 'score': 0.9985401630401611, 'x0': 333.0, 'x1': 522.0885518391927, 'top': 80.44077252327129, 'bottom': 426.1023988115027, 'page_number': 6}]
2025-08-12 23:37:27,882 DEBUG    35 Image 6 dimensions: (1838, 2376)
2025-08-12 23:37:27,887 DEBUG    35 OCR boxes sample for page 6: [{'x0': 453.0, 'x1': 540.3333333333334, 'top': 33.666666666666664, 'text': 'PintoValleyMine', 'bottom': 45.333333333333336, 'page_number': 7}, {'x0': 96.33333333333333, 'x1': 204.33333333333334, 'top': 44.666666666666664, 'text': 'capstone', 'bottom': 64.66666666666667, 'page_number': 7}]
2025-08-12 23:37:27,901 DEBUG    35 DiT original boxes for page 7 (before scale_factor=3 division): [{'type': 'figure', 'x0': 208.53872108459473, 'top': 285.74741704413236, 'x1': 1695.4119671630858, 'bottom': 1416.2915974069151, 'score': 0.9985477328300476}, {'type': 'footer', 'x0': 1456.0, 'top': 2194.9317549035904, 'x1': 1626.0, 'bottom': 2237.0, 'score': 0.9959463477134705}]
2025-08-12 23:37:27,902 DEBUG    35 DiT scaled boxes for page 7 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9985477328300476, 'x0': 69.51290702819824, 'x1': 565.1373223876952, 'top': 95.24913901471079, 'bottom': 472.0971991356384, 'page_number': 7}, {'type': 'footer', 'score': 0.9959463477134705, 'x0': 485.3333333333333, 'x1': 542.0, 'top': 731.6439183011968, 'bottom': 745.6666666666666, 'page_number': 7}]
2025-08-12 23:37:27,905 DEBUG    35 Image 7 dimensions: (1838, 2376)
2025-08-12 23:37:27,910 DEBUG    35 OCR boxes sample for page 7: [{'x0': 453.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 8}, {'x0': 96.33333333333333, 'x1': 203.33333333333334, 'top': 44.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 8}]
2025-08-12 23:37:27,919 DEBUG    35 DiT original boxes for page 8 (before scale_factor=3 division): [{'type': 'figure', 'x0': 213.54599220275878, 'top': 267.731790745512, 'x1': 1695.8447116088867, 'bottom': 1148.0, 'score': 0.9982872605323792}, {'type': 'figure', 'x0': 210.97220745086668, 'top': 99.773332311752, 'x1': 615.20947265625, 'bottom': 217.9003308884641, 'score': 0.9939536452293396}]
2025-08-12 23:37:27,919 DEBUG    35 DiT scaled boxes for page 8 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9982872605323792, 'x0': 71.1819974009196, 'x1': 565.2815705362956, 'top': 89.243930248504, 'bottom': 382.6666666666667, 'page_number': 8}, {'type': 'figure', 'score': 0.9939536452293396, 'x0': 70.32406915028889, 'x1': 205.06982421875, 'top': 33.25777743725067, 'bottom': 72.63344362948804, 'page_number': 8}]
2025-08-12 23:37:27,921 DEBUG    35 Image 8 dimensions: (1838, 2376)
2025-08-12 23:37:27,925 DEBUG    35 OCR boxes sample for page 8: [{'x0': 453.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 9}, {'x0': 95.66666666666667, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 9}]
2025-08-12 23:37:27,935 DEBUG    35 DiT original boxes for page 9 (before scale_factor=3 division): [{'type': 'text', 'x0': 210.0, 'top': 1770.690860829455, 'x1': 1614.1558538818358, 'bottom': 2124.206122215758, 'score': 0.998759388923645}, {'type': 'figure', 'x0': 220.17472282409668, 'top': 242.13423059341758, 'x1': 1710.254007873535, 'bottom': 1316.8668238863033, 'score': 0.9986159801483154}]
2025-08-12 23:37:27,936 DEBUG    35 DiT scaled boxes for page 9 (after scale_factor=3 division): [{'type': 'text', 'score': 0.998759388923645, 'x0': 70.0, 'x1': 538.0519512939453, 'top': 590.2302869431517, 'bottom': 708.0687074052527, 'page_number': 9}, {'type': 'figure', 'score': 0.9986159801483154, 'x0': 73.3915742746989, 'x1': 570.0846692911783, 'top': 80.71141019780586, 'bottom': 438.9556079621011, 'page_number': 9}]
2025-08-12 23:37:27,940 DEBUG    35 Image 9 dimensions: (1838, 2376)
2025-08-12 23:37:27,945 DEBUG    35 OCR boxes sample for page 9: [{'x0': 454.0, 'x1': 540.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 10}, {'x0': 94.0, 'x1': 204.33333333333334, 'top': 44.666666666666664, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 10}]
2025-08-12 23:37:27,954 DEBUG    35 DiT original boxes for page 10 (before scale_factor=3 division): [{'type': 'title', 'x0': 213.0, 'top': 1003.1162265209442, 'x1': 554.8352070236206, 'bottom': 1050.965939785572, 'score': 0.9990160465240479}, {'type': 'table', 'x0': 215.0, 'top': 1604.0, 'x1': 1633.050070953369, 'bottom': 2139.6521048038567, 'score': 0.9983855485916138}]
2025-08-12 23:37:27,954 DEBUG    35 DiT scaled boxes for page 10 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9990160465240479, 'x0': 71.0, 'x1': 184.9450690078735, 'top': 334.3720755069814, 'bottom': 350.32197992852394, 'page_number': 10}, {'type': 'table', 'score': 0.9983855485916138, 'x0': 71.66666666666667, 'x1': 544.350023651123, 'top': 534.6666666666666, 'bottom': 713.2173682679522, 'page_number': 10}]
2025-08-12 23:37:27,957 DEBUG    35 Image 10 dimensions: (1838, 2376)
2025-08-12 23:37:27,961 DEBUG    35 OCR boxes sample for page 10: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 11}, {'x0': 121.0, 'x1': 203.33333333333334, 'top': 42.0, 'text': 'pstone', 'bottom': 60.0, 'page_number': 11}]
2025-08-12 23:37:27,982 DEBUG    35 DiT original boxes for page 11 (before scale_factor=3 division): [{'type': 'title', 'x0': 212.88935579299925, 'top': 1847.313486951463, 'x1': 649.1963183593749, 'bottom': 1888.0, 'score': 0.9986844658851624}, {'type': 'title', 'x0': 212.0, 'top': 1335.3311481881651, 'x1': 684.9761928558349, 'bottom': 1374.0, 'score': 0.9982643723487854}]
2025-08-12 23:37:27,983 DEBUG    35 DiT scaled boxes for page 11 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9986844658851624, 'x0': 70.96311859766642, 'x1': 216.3987727864583, 'top': 615.7711623171543, 'bottom': 629.3333333333334, 'page_number': 11}, {'type': 'title', 'score': 0.9982643723487854, 'x0': 70.66666666666667, 'x1': 228.32539761861165, 'top': 445.1103827293884, 'bottom': 458.0, 'page_number': 11}]
2025-08-12 23:37:27,986 DEBUG    35 Image 11 dimensions: (1838, 2376)
2025-08-12 23:37:27,993 DEBUG    35 OCR boxes sample for page 11: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 36.333333333333336, 'text': 'PintoValleyMine', 'bottom': 48.0, 'page_number': 12}, {'x0': 94.0, 'x1': 203.33333333333334, 'top': 45.666666666666664, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 12}]
2025-08-12 23:37:28,028 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.63, progress_msg: 23:37:28 Page(13~25): Layout analysis (15.94s)
2025-08-12 23:37:28,587 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.65, progress_msg: 23:37:28 Page(13~25): Table analysis (0.54s)
2025-08-12 23:37:28,603 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.67, progress_msg: 23:37:28 Page(13~25): Text merged (0.00s)
2025-08-12 23:37:29,473 INFO     35 layouts cost: 30.90576928295195s
2025-08-12 23:37:29,543 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.8, progress_msg: 23:37:29 Page(13~25): Finish parsing.
2025-08-12 23:37:29,758 INFO     35 naive_merge(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf): 0.2146635929821059
2025-08-12 23:37:29,759 INFO     35 Chunking(60.15804202796426) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf done
2025-08-12 23:37:29,966 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.1, progress_msg: 23:37:29 Page(25~37): Start to parse.
2025-08-12 23:37:29,991 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:37:30,048 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:37:30,054 INFO     35 load_model /ragflow/rag/res/deepdoc/layout.onnx reuses cached model
2025-08-12 23:37:30,055 INFO     35 load_model /ragflow/rag/res/deepdoc/tsr.onnx reuses cached model
2025-08-12 23:37:30,102 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:37:30 Page(25~37): OCR started
2025-08-12 23:37:30,433 INFO     35 MINIO PUT(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf) cost 0.669 s
2025-08-12 23:37:30,441 INFO     35 Build document Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf: 60.84s
2025-08-12 23:37:30,460 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:37:30 Page(13~25): Generate 32 chunks
2025-08-12 23:37:31,092 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 33
2025-08-12 23:37:31,960 INFO     35 __images__ dedupe_chars cost 1.8577151760691777s
2025-08-12 23:37:31,962 WARNING  35 Miss outlines
2025-08-12 23:37:32,149 INFO     35 __ocr detecting boxes of a image cost (0.18344874493777752s)
2025-08-12 23:37:32,150 INFO     35 __ocr sorting 0 chars cost 0.0005911820335313678s
2025-08-12 23:37:32,902 INFO     35 __ocr recognize 33 boxes cost 0.7477935089264065s
2025-08-12 23:37:33,059 INFO     35 __ocr detecting boxes of a image cost (0.15613443893380463s)
2025-08-12 23:37:33,060 INFO     35 __ocr sorting 0 chars cost 0.0006263488903641701s
2025-08-12 23:37:34,017 INFO     35 __ocr recognize 38 boxes cost 0.9534092049580067s
2025-08-12 23:37:34,039 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 574
2025-08-12 23:37:34,047 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.7062499999999999, progress_msg: 
2025-08-12 23:37:34,189 INFO     35 __ocr detecting boxes of a image cost (0.17167762201279402s)
2025-08-12 23:37:34,190 INFO     35 __ocr sorting 0 chars cost 0.0006826380267739296s
2025-08-12 23:37:35,353 INFO     35 __ocr recognize 60 boxes cost 1.161574008059688s
2025-08-12 23:37:35,532 INFO     35 __ocr detecting boxes of a image cost (0.17845495499204844s)
2025-08-12 23:37:35,533 INFO     35 __ocr sorting 0 chars cost 0.0009401249699294567s
2025-08-12 23:37:36,443 INFO     35 __ocr recognize 35 boxes cost 0.9085412640124559s
2025-08-12 23:37:36,636 INFO     35 __ocr detecting boxes of a image cost (0.19126955803949386s)
2025-08-12 23:37:36,637 INFO     35 __ocr sorting 0 chars cost 0.0008205979829654098s
2025-08-12 23:37:38,340 INFO     35 __ocr recognize 75 boxes cost 1.6965732909739017s
2025-08-12 23:37:38,538 INFO     35 __ocr detecting boxes of a image cost (0.19646895304322243s)
2025-08-12 23:37:38,540 INFO     35 __ocr sorting 0 chars cost 0.0008679419988766313s
2025-08-12 23:37:39,255 INFO     35 __ocr recognize 59 boxes cost 0.7144618819002062s
2025-08-12 23:37:39,266 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.3, progress_msg: 
2025-08-12 23:37:39,449 INFO     35 __ocr detecting boxes of a image cost (0.1828311119461432s)
2025-08-12 23:37:39,450 INFO     35 __ocr sorting 0 chars cost 0.0002457199152559042s
2025-08-12 23:37:39,822 INFO     35 __ocr recognize 12 boxes cost 0.3709771198919043s
2025-08-12 23:37:40,011 INFO     35 __ocr detecting boxes of a image cost (0.18766492791473866s)
2025-08-12 23:37:40,012 INFO     35 __ocr sorting 0 chars cost 0.0005425800336524844s
2025-08-12 23:37:40,895 INFO     35 __ocr recognize 30 boxes cost 0.8673245389945805s
2025-08-12 23:37:41,090 INFO     35 __ocr detecting boxes of a image cost (0.19365969207137823s)
2025-08-12 23:37:41,091 INFO     35 __ocr sorting 0 chars cost 0.0010738959535956383s
2025-08-12 23:37:42,535 INFO     35 __ocr recognize 68 boxes cost 1.4360380589496344s
2025-08-12 23:37:42,748 INFO     35 __ocr detecting boxes of a image cost (0.21138726698700339s)
2025-08-12 23:37:42,749 INFO     35 __ocr sorting 0 chars cost 0.0006860370049253106s
2025-08-12 23:37:43,098 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 3715
2025-08-12 23:37:43,115 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.8062499999999999, progress_msg: 
2025-08-12 23:37:43,120 INFO     35 Embedding chunks (12.66s)
2025-08-12 23:37:43,136 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:37:43 Page(13~25): Embedding chunks (12.66s)
2025-08-12 23:37:43,170 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.018s]
2025-08-12 23:37:43,187 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 0.8031250000000001, progress_msg: 
2025-08-12 23:37:43,226 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.013s]
2025-08-12 23:37:43,264 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.012s]
2025-08-12 23:37:43,298 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.009s]
2025-08-12 23:37:43,326 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.007s]
2025-08-12 23:37:43,350 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.006s]
2025-08-12 23:37:43,376 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.007s]
2025-08-12 23:37:43,400 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.007s]
2025-08-12 23:37:43,404 INFO     35 Indexing doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(12-24), chunks(32), elapsed: 0.27
2025-08-12 23:37:43,440 INFO     35 set_progress(17f96a1a779211f0ac8e02420ae90a05), progress: 1.0, progress_msg: 23:37:43 Page(13~25): Indexing done (0.28s). Task done (73.95s)
2025-08-12 23:37:43,441 INFO     35 Chunk doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(12-24), chunks(32), token(4322), elapsed:73.95
2025-08-12 23:37:43,447 INFO     35 handle_task done for task {"id": "17f96a1a779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 12, "to_page": 24, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979980, "task_type": ""}
2025-08-12 23:37:43,662 INFO     35 __ocr recognize 42 boxes cost 0.9101069360040128s
2025-08-12 23:37:43,794 INFO     35 __ocr detecting boxes of a image cost (0.13164538203272969s)
2025-08-12 23:37:43,795 INFO     35 __ocr sorting 0 chars cost 0.00025397504214197397s
2025-08-12 23:37:44,239 INFO     35 __ocr recognize 20 boxes cost 0.4416993949562311s
2025-08-12 23:37:44,367 INFO     35 __ocr detecting boxes of a image cost (0.12643587996717542s)
2025-08-12 23:37:44,367 INFO     35 __ocr sorting 0 chars cost 0.00018861598800867796s
2025-08-12 23:37:44,421 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:37:44.419+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 2, "lag": 0, "done": 2, "failed": 0, "current": {"17f96a9c779211f0ac8e02420ae90a05": {"id": "17f96a9c779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 24, "to_page": 36, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979981, "task_type": ""}, "17f96b14779211f0ac8e02420ae90a05": {"id": "17f96b14779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 36, "to_page": 45, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979982, "task_type": ""}}}
2025-08-12 23:37:44,609 INFO     35 __ocr recognize 14 boxes cost 0.23803843697533011s
2025-08-12 23:37:44,615 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.6, progress_msg: 
2025-08-12 23:37:44,616 INFO     35 __images__ 12 pages cost 12.651176373939961s
2025-08-12 23:37:44,625 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:37:44 Page(25~37): OCR finished (14.53s)
2025-08-12 23:37:44,625 INFO     35 OCR(24~36): 14.54s
2025-08-12 23:37:44,629 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:45,153 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:45,154 DEBUG    35 DiT API response for page 0: boxes=9
2025-08-12 23:37:45,156 DEBUG    35 DiT raw API boxes for page 0 (first 2): [[216.84016464233397, 671.7860782704455, 1625.0177955627441, 1765.224297706117], [217.09532751083373, 1856.327418550532, 672.4393647003174, 1906.6208651928193]]
2025-08-12 23:37:45,161 DEBUG    35 DiT raw API classes for page 0 (first 2): ['FIGURE', 'TITLE']
2025-08-12 23:37:45,163 DEBUG    35 DiT raw API scores for page 0 (first 2): [0.9992607235908508, 0.9987460374832153]
2025-08-12 23:37:45,166 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:37:45,181 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:37:45,996 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:37:45,996 DEBUG    35 DiT processed 9 boxes for page 0, first 3: [{'type': 'figure', 'x0': 216.84016464233397, 'top': 671.7860782704455, 'x1': 1625.0177955627441, 'bottom': 1765.2242977061173, 'score': 0.9992607235908508}, {'type': 'title', 'x0': 212.0, 'top': 1856.327418550532, 'x1': 672.4393647003174, 'bottom': 1906.6208651928193, 'score': 0.9987460374832153}, {'type': 'text', 'x0': 207.0, 'top': 245.21165303981053, 'x1': 1633.9190655517577, 'bottom': 657.0466100814496, 'score': 0.9978495836257935}]
2025-08-12 23:37:45,999 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:46,416 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:46,417 DEBUG    35 DiT API response for page 1: boxes=11
2025-08-12 23:37:46,422 DEBUG    35 DiT raw API boxes for page 1 (first 2): [[287.7132859039306, 247.16571759163068, 1600.6830020141601, 691.0593001994681], [278.2712255859375, 1111.334036319814, 1593.0799653625488, 1691.4996779421544]]
2025-08-12 23:37:46,426 DEBUG    35 DiT raw API classes for page 1 (first 2): ['LIST', 'LIST']
2025-08-12 23:37:46,429 DEBUG    35 DiT raw API scores for page 1 (first 2): [0.9994256496429443, 0.9992989301681519]
2025-08-12 23:37:47,422 DEBUG    35 Applied OCR adjustment to 11 boxes
2025-08-12 23:37:47,422 DEBUG    35 DiT processed 11 boxes for page 1, first 3: [{'type': 'text', 'x0': 267.0, 'top': 247.16571759163068, 'x1': 1600.6830020141601, 'bottom': 691.0593001994681, 'score': 0.9994256496429443}, {'type': 'text', 'x0': 265.0, 'top': 1111.334036319814, 'x1': 1593.0799653625486, 'bottom': 1691.4996779421544, 'score': 0.9992989301681519}, {'type': 'title', 'x0': 213.0, 'top': 1780.3319948886306, 'x1': 689.4551539611816, 'bottom': 1828.5822650016623, 'score': 0.9989643096923828}]
2025-08-12 23:37:47,426 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:47,848 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:47,849 DEBUG    35 DiT API response for page 2: boxes=17
2025-08-12 23:37:47,853 DEBUG    35 DiT raw API boxes for page 2 (first 2): [[238.73666900634765, 729.8629643866357, 1620.7443389892578, 889.9915953291224], [233.61518196105956, 920.2012394032581, 1618.9892419433593, 1075.697660405585]]
2025-08-12 23:37:47,857 DEBUG    35 DiT raw API classes for page 2 (first 2): ['TEXT', 'TEXT']
2025-08-12 23:37:47,859 DEBUG    35 DiT raw API scores for page 2 (first 2): [0.9966837763786316, 0.996677041053772]
2025-08-12 23:37:49,017 DEBUG    35 Applied OCR adjustment to 17 boxes
2025-08-12 23:37:49,017 DEBUG    35 DiT processed 17 boxes for page 2, first 3: [{'type': 'text', 'x0': 210.0, 'top': 729.8629643866357, 'x1': 1620.7443389892578, 'bottom': 896.0, 'score': 0.9966837763786316}, {'type': 'text', 'x0': 210.0, 'top': 920.2012394032581, 'x1': 1621.0, 'bottom': 1082.0, 'score': 0.996677041053772}, {'type': 'text', 'x0': 215.0, 'top': 541.5613792906416, 'x1': 1622.440960845947, 'bottom': 709.1678778465758, 'score': 0.9962860345840454}]
2025-08-12 23:37:49,020 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:49,446 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:49,447 DEBUG    35 DiT API response for page 3: boxes=9
2025-08-12 23:37:49,448 DEBUG    35 DiT raw API boxes for page 3 (first 2): [[224.00554885864256, 650.9078239278591, 1626.9714558410644, 1723.7914883228061], [226.60615226745605, 350.06108969830456, 1600.073289489746, 644.3157465508644]]
2025-08-12 23:37:49,453 DEBUG    35 DiT raw API classes for page 3 (first 2): ['FIGURE', 'TEXT']
2025-08-12 23:37:49,457 DEBUG    35 DiT raw API scores for page 3 (first 2): [0.9985474944114685, 0.9977632761001587]
2025-08-12 23:37:50,271 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:37:50,272 DEBUG    35 DiT processed 9 boxes for page 3, first 3: [{'type': 'figure', 'x0': 224.00554885864256, 'top': 650.9078239278591, 'x1': 1626.9714558410647, 'bottom': 1723.7914883228063, 'score': 0.9985474944114685}, {'type': 'text', 'x0': 205.0, 'top': 350.06108969830456, 'x1': 1611.0, 'bottom': 644.3157465508644, 'score': 0.9977632761001587}, {'type': 'text', 'x0': 215.0, 'top': 1838.3248005319151, 'x1': 1618.0, 'bottom': 1916.0, 'score': 0.997305154800415}]
2025-08-12 23:37:50,274 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:50,686 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:50,687 DEBUG    35 DiT API response for page 4: boxes=10
2025-08-12 23:37:50,689 DEBUG    35 DiT raw API boxes for page 4 (first 2): [[232.17125144958496, 1098.1423080119682, 1621.9118795776367, 1347.812645445479], [229.0441609954834, 1560.4011801861704, 1618.184051208496, 2130.117281000665]]
2025-08-12 23:37:50,692 DEBUG    35 DiT raw API classes for page 4 (first 2): ['TEXT', 'TEXT']
2025-08-12 23:37:50,696 DEBUG    35 DiT raw API scores for page 4 (first 2): [0.9988792538642883, 0.9987419247627258]
2025-08-12 23:37:52,217 DEBUG    35 Applied OCR adjustment to 10 boxes
2025-08-12 23:37:52,217 DEBUG    35 DiT processed 10 boxes for page 4, first 3: [{'type': 'text', 'x0': 210.0, 'top': 1098.1423080119682, 'x1': 1621.9118795776367, 'bottom': 1347.812645445479, 'score': 0.9988792538642883}, {'type': 'text', 'x0': 212.0, 'top': 1560.4011801861704, 'x1': 1618.184051208496, 'bottom': 2130.117281000665, 'score': 0.9987419247627258}, {'type': 'text', 'x0': 212.0, 'top': 954.2874911693817, 'x1': 1609.084638671875, 'bottom': 1072.0, 'score': 0.997190535068512}]
2025-08-12 23:37:52,222 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:52,628 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:52,629 DEBUG    35 DiT API response for page 5: boxes=11
2025-08-12 23:37:52,631 DEBUG    35 DiT raw API boxes for page 5 (first 2): [[246.15257102966308, 849.6258155335772, 1616.6297610473632, 1428.693291846742], [701.7661444854735, 683.705659906915, 1138.350040359497, 718.7312427277261]]
2025-08-12 23:37:52,634 DEBUG    35 DiT raw API classes for page 5 (first 2): ['TABLE', 'TITLE']
2025-08-12 23:37:52,638 DEBUG    35 DiT raw API scores for page 5 (first 2): [0.9966614246368408, 0.9959595799446106]
2025-08-12 23:37:53,389 DEBUG    35 Applied OCR adjustment to 11 boxes
2025-08-12 23:37:53,390 DEBUG    35 DiT processed 11 boxes for page 5, first 3: [{'type': 'table', 'x0': 223.0, 'top': 849.6258155335772, 'x1': 1616.6297610473632, 'bottom': 1448.0, 'score': 0.9966614246368408}, {'type': 'title', 'x0': 701.7661444854735, 'top': 683.705659906915, 'x1': 1138.350040359497, 'bottom': 720.0, 'score': 0.9959595799446106}, {'type': 'footer', 'x0': 1456.0, 'top': 2195.0, 'x1': 1628.0, 'bottom': 2237.0, 'score': 0.9919143915176392}]
2025-08-12 23:37:53,391 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:53,787 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:53,788 DEBUG    35 DiT API response for page 6: boxes=5
2025-08-12 23:37:53,792 DEBUG    35 DiT raw API boxes for page 6 (first 2): [[203.31409614562986, 252.76574707031253, 1619.2949395751953, 1261.005412649601], [214.0275360870361, 1260.1913958610373, 1598.942769165039, 1326.2117478390958]]
2025-08-12 23:37:53,795 DEBUG    35 DiT raw API classes for page 6 (first 2): ['FIGURE', 'CAPTION']
2025-08-12 23:37:53,801 DEBUG    35 DiT raw API scores for page 6 (first 2): [0.9995813965797424, 0.9984515905380249]
2025-08-12 23:37:54,280 DEBUG    35 Applied OCR adjustment to 5 boxes
2025-08-12 23:37:54,281 DEBUG    35 DiT processed 5 boxes for page 6, first 3: [{'type': 'figure', 'x0': 203.31409614562986, 'top': 252.76574707031253, 'x1': 1619.2949395751953, 'bottom': 1261.005412649601, 'score': 0.9995813965797424}, {'type': 'figure caption', 'x0': 207.0, 'top': 1255.0, 'x1': 1601.0, 'bottom': 1334.0, 'score': 0.9984515905380249}, {'type': 'footer', 'x0': 1456.0, 'top': 2195.0, 'x1': 1628.0, 'bottom': 2237.0, 'score': 0.9936815500259399}]
2025-08-12 23:37:54,282 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:54,694 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:54,695 DEBUG    35 DiT API response for page 7: boxes=9
2025-08-12 23:37:54,698 DEBUG    35 DiT raw API boxes for page 7 (first 2): [[219.28798934936523, 253.4906343500665, 1624.5553227233886, 1269.9789519614362], [214.44930767059324, 1822.478541597407, 904.2489674377441, 1869.7206875415561]]
2025-08-12 23:37:54,702 DEBUG    35 DiT raw API classes for page 7 (first 2): ['FIGURE', 'TITLE']
2025-08-12 23:37:54,707 DEBUG    35 DiT raw API scores for page 7 (first 2): [0.999072790145874, 0.9987383484840393]
2025-08-12 23:37:55,566 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:37:55,567 DEBUG    35 DiT processed 9 boxes for page 7, first 3: [{'type': 'figure', 'x0': 219.28798934936523, 'top': 253.4906343500665, 'x1': 1624.5553227233886, 'bottom': 1269.9789519614362, 'score': 0.999072790145874}, {'type': 'title', 'x0': 214.44930767059324, 'top': 1822.478541597407, 'x1': 904.2489674377441, 'bottom': 1869.7206875415561, 'score': 0.9987383484840393}, {'type': 'text', 'x0': 200.2688990020752, 'top': 1403.0, 'x1': 1606.0, 'bottom': 1651.0, 'score': 0.99863201379776}]
2025-08-12 23:37:55,570 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:55,985 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:55,986 DEBUG    35 DiT API response for page 8: boxes=13
2025-08-12 23:37:55,991 DEBUG    35 DiT raw API boxes for page 8 (first 2): [[216.6745901107788, 1436.1685297539896, 427.71989154815674, 1484.9099588597076], [284.5027596282959, 1651.3501651845081, 1604.1446771240232, 2027.5633674783912]]
2025-08-12 23:37:55,999 DEBUG    35 DiT raw API classes for page 8 (first 2): ['TITLE', 'LIST']
2025-08-12 23:37:56,003 DEBUG    35 DiT raw API scores for page 8 (first 2): [0.9985558390617371, 0.998097836971283]
2025-08-12 23:37:57,328 DEBUG    35 Applied OCR adjustment to 13 boxes
2025-08-12 23:37:57,328 DEBUG    35 DiT processed 13 boxes for page 8, first 3: [{'type': 'title', 'x0': 213.0, 'top': 1435.0, 'x1': 427.71989154815674, 'bottom': 1484.9099588597076, 'score': 0.9985558390617371}, {'type': 'text', 'x0': 265.0, 'top': 1651.3501651845081, 'x1': 1604.1446771240232, 'bottom': 2027.5633674783912, 'score': 0.998097836971283}, {'type': 'table', 'x0': 245.0, 'top': 791.4369389960107, 'x1': 1589.8878089904783, 'bottom': 1038.5833194813831, 'score': 0.997632622718811}]
2025-08-12 23:37:57,330 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:57,745 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:57,746 DEBUG    35 DiT API response for page 9: boxes=10
2025-08-12 23:37:57,749 DEBUG    35 DiT raw API boxes for page 9 (first 2): [[212.33243921279907, 1214.9676020196146, 745.2226053619385, 1263.174113821476], [234.10002120971677, 1476.8221045960772, 1588.298321533203, 1596.0977237782581]]
2025-08-12 23:37:57,752 DEBUG    35 DiT raw API classes for page 9 (first 2): ['TITLE', 'TEXT']
2025-08-12 23:37:57,755 DEBUG    35 DiT raw API scores for page 9 (first 2): [0.9990567564964294, 0.9987744688987732]
2025-08-12 23:37:58,721 DEBUG    35 Applied OCR adjustment to 10 boxes
2025-08-12 23:37:58,721 DEBUG    35 DiT processed 10 boxes for page 9, first 3: [{'type': 'title', 'x0': 212.33243921279907, 'top': 1213.0, 'x1': 747.0, 'bottom': 1263.174113821476, 'score': 0.9990567564964294}, {'type': 'text', 'x0': 212.0, 'top': 1476.8221045960772, 'x1': 1598.0, 'bottom': 1599.0, 'score': 0.9987744688987732}, {'type': 'text', 'x0': 212.0, 'top': 1288.6392069065826, 'x1': 1613.0, 'bottom': 1453.0, 'score': 0.9984686970710754}]
2025-08-12 23:37:58,723 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:37:59,131 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:37:59,132 DEBUG    35 DiT API response for page 10: boxes=9
2025-08-12 23:37:59,143 DEBUG    35 DiT raw API boxes for page 10 (first 2): [[206.8818539428711, 659.8786984707448, 1517.0055671691894, 1471.9147221991357], [214.21158569335935, 397.73552427900603, 892.8609593963622, 445.57492909532914]]
2025-08-12 23:37:59,152 DEBUG    35 DiT raw API classes for page 10 (first 2): ['FIGURE', 'TITLE']
2025-08-12 23:37:59,161 DEBUG    35 DiT raw API scores for page 10 (first 2): [0.9987344145774841, 0.9987090826034546]
2025-08-12 23:37:59,830 DEBUG    35 Applied OCR adjustment to 9 boxes
2025-08-12 23:37:59,830 DEBUG    35 DiT processed 9 boxes for page 10, first 3: [{'type': 'figure', 'x0': 206.8818539428711, 'top': 659.8786984707448, 'x1': 1517.0055671691894, 'bottom': 1471.9147221991357, 'score': 0.9987344145774841}, {'type': 'title', 'x0': 212.0, 'top': 396.0, 'x1': 892.8609593963622, 'bottom': 445.57492909532914, 'score': 0.9987090826034546}, {'type': 'figure caption', 'x0': 210.0, 'top': 1468.0, 'x1': 1056.0, 'bottom': 1510.0, 'score': 0.9965029954910278}]
2025-08-12 23:37:59,833 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:00,246 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:00,247 DEBUG    35 DiT API response for page 11: boxes=8
2025-08-12 23:38:00,251 DEBUG    35 DiT raw API boxes for page 11 (first 2): [[213.88555496215818, 246.04621659948472, 1529.9229748535156, 1061.6151980136303], [212.10781105041502, 1162.945702086104, 1508.2879962158202, 1967.640567860705]]
2025-08-12 23:38:00,255 DEBUG    35 DiT raw API classes for page 11 (first 2): ['FIGURE', 'FIGURE']
2025-08-12 23:38:00,263 DEBUG    35 DiT raw API scores for page 11 (first 2): [0.9993543028831482, 0.9985131621360779]
2025-08-12 23:38:00,748 DEBUG    35 Applied OCR adjustment to 8 boxes
2025-08-12 23:38:00,748 DEBUG    35 DiT processed 8 boxes for page 11, first 3: [{'type': 'figure', 'x0': 213.88555496215818, 'top': 246.04621659948472, 'x1': 1529.9229748535156, 'bottom': 1063.0, 'score': 0.9993543028831482}, {'type': 'figure', 'x0': 212.10781105041502, 'top': 1162.945702086104, 'x1': 1508.2879962158202, 'bottom': 1975.0, 'score': 0.9985131621360779}, {'type': 'figure', 'x0': 201.26513322830198, 'top': 100.02508285197807, 'x1': 616.0, 'bottom': 222.74151481466092, 'score': 0.9955506324768066}]
2025-08-12 23:38:00,756 DEBUG    35 DiT original boxes for page 0 (before scale_factor=3 division): [{'type': 'figure', 'x0': 216.84016464233397, 'top': 671.7860782704455, 'x1': 1625.0177955627441, 'bottom': 1765.2242977061173, 'score': 0.9992607235908508}, {'type': 'title', 'x0': 212.0, 'top': 1856.327418550532, 'x1': 672.4393647003174, 'bottom': 1906.6208651928193, 'score': 0.9987460374832153}]
2025-08-12 23:38:00,783 DEBUG    35 DiT scaled boxes for page 0 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9992607235908508, 'x0': 72.280054880778, 'x1': 541.6725985209147, 'top': 223.92869275681517, 'bottom': 588.4080992353724, 'page_number': 0}, {'type': 'title', 'score': 0.9987460374832153, 'x0': 70.66666666666667, 'x1': 224.1464549001058, 'top': 618.7758061835107, 'bottom': 635.5402883976064, 'page_number': 0}]
2025-08-12 23:38:00,792 DEBUG    35 Image 0 dimensions: (1838, 2376)
2025-08-12 23:38:00,800 DEBUG    35 OCR boxes sample for page 0: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 34.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.333333333333336, 'page_number': 1}, {'x0': 98.0, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 1}]
2025-08-12 23:38:00,816 DEBUG    35 DiT original boxes for page 1 (before scale_factor=3 division): [{'type': 'text', 'x0': 267.0, 'top': 247.16571759163068, 'x1': 1600.6830020141601, 'bottom': 691.0593001994681, 'score': 0.9994256496429443}, {'type': 'text', 'x0': 265.0, 'top': 1111.334036319814, 'x1': 1593.0799653625486, 'bottom': 1691.4996779421544, 'score': 0.9992989301681519}]
2025-08-12 23:38:00,817 DEBUG    35 DiT scaled boxes for page 1 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9994256496429443, 'x0': 89.0, 'x1': 533.5610006713868, 'top': 82.38857253054356, 'bottom': 230.35310006648936, 'page_number': 1}, {'type': 'text', 'score': 0.9992989301681519, 'x0': 88.33333333333333, 'x1': 531.0266551208496, 'top': 370.44467877327133, 'bottom': 563.8332259807181, 'page_number': 1}]
2025-08-12 23:38:00,821 DEBUG    35 Image 1 dimensions: (1838, 2376)
2025-08-12 23:38:00,826 DEBUG    35 OCR boxes sample for page 1: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 36.333333333333336, 'text': 'PintoValleyMine', 'bottom': 48.0, 'page_number': 2}, {'x0': 95.66666666666667, 'x1': 202.66666666666666, 'top': 46.333333333333336, 'text': 'capstone', 'bottom': 63.666666666666664, 'page_number': 2}]
2025-08-12 23:38:00,845 DEBUG    35 DiT original boxes for page 2 (before scale_factor=3 division): [{'type': 'text', 'x0': 210.0, 'top': 729.8629643866357, 'x1': 1620.7443389892578, 'bottom': 896.0, 'score': 0.9966837763786316}, {'type': 'text', 'x0': 210.0, 'top': 920.2012394032581, 'x1': 1621.0, 'bottom': 1082.0, 'score': 0.996677041053772}]
2025-08-12 23:38:00,845 DEBUG    35 DiT scaled boxes for page 2 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9966837763786316, 'x0': 70.0, 'x1': 540.2481129964193, 'top': 243.28765479554522, 'bottom': 298.6666666666667, 'page_number': 2}, {'type': 'text', 'score': 0.996677041053772, 'x0': 70.0, 'x1': 540.3333333333334, 'top': 306.7337464677527, 'bottom': 360.6666666666667, 'page_number': 2}]
2025-08-12 23:38:00,854 DEBUG    35 Image 2 dimensions: (1838, 2376)
2025-08-12 23:38:00,864 DEBUG    35 OCR boxes sample for page 2: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 3}, {'x0': 124.0, 'x1': 202.33333333333334, 'top': 45.333333333333336, 'text': 'pstone', 'bottom': 60.333333333333336, 'page_number': 3}]
2025-08-12 23:38:00,889 DEBUG    35 DiT original boxes for page 3 (before scale_factor=3 division): [{'type': 'figure', 'x0': 224.00554885864256, 'top': 650.9078239278591, 'x1': 1626.9714558410647, 'bottom': 1723.7914883228063, 'score': 0.9985474944114685}, {'type': 'text', 'x0': 205.0, 'top': 350.06108969830456, 'x1': 1611.0, 'bottom': 644.3157465508644, 'score': 0.9977632761001587}]
2025-08-12 23:38:00,889 DEBUG    35 DiT scaled boxes for page 3 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9985474944114685, 'x0': 74.66851628621419, 'x1': 542.3238186136882, 'top': 216.9692746426197, 'bottom': 574.5971627742688, 'page_number': 3}, {'type': 'text', 'score': 0.9977632761001587, 'x0': 68.33333333333333, 'x1': 537.0, 'top': 116.68702989943485, 'bottom': 214.7719155169548, 'page_number': 3}]
2025-08-12 23:38:00,920 DEBUG    35 Image 3 dimensions: (1838, 2376)
2025-08-12 23:38:00,973 DEBUG    35 OCR boxes sample for page 3: [{'x0': 453.6666666666667, 'x1': 541.0, 'top': 34.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.333333333333336, 'page_number': 4}, {'x0': 95.66666666666667, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 4}]
2025-08-12 23:38:01,018 DEBUG    35 DiT original boxes for page 4 (before scale_factor=3 division): [{'type': 'text', 'x0': 210.0, 'top': 1098.1423080119682, 'x1': 1621.9118795776367, 'bottom': 1347.812645445479, 'score': 0.9988792538642883}, {'type': 'text', 'x0': 212.0, 'top': 1560.4011801861704, 'x1': 1618.184051208496, 'bottom': 2130.117281000665, 'score': 0.9987419247627258}]
2025-08-12 23:38:01,047 DEBUG    35 DiT scaled boxes for page 4 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9988792538642883, 'x0': 70.0, 'x1': 540.6372931925456, 'top': 366.0474360039894, 'bottom': 449.2708818151596, 'page_number': 4}, {'type': 'text', 'score': 0.9987419247627258, 'x0': 70.66666666666667, 'x1': 539.3946837361653, 'top': 520.1337267287234, 'bottom': 710.0390936668883, 'page_number': 4}]
2025-08-12 23:38:01,081 DEBUG    35 Image 4 dimensions: (1838, 2376)
2025-08-12 23:38:01,085 DEBUG    35 OCR boxes sample for page 4: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 5}, {'x0': 104.0, 'x1': 203.66666666666666, 'top': 44.666666666666664, 'text': 'capstone', 'bottom': 63.0, 'page_number': 5}]
2025-08-12 23:38:01,146 DEBUG    35 DiT original boxes for page 5 (before scale_factor=3 division): [{'type': 'table', 'x0': 223.0, 'top': 849.6258155335772, 'x1': 1616.6297610473632, 'bottom': 1448.0, 'score': 0.9966614246368408}, {'type': 'title', 'x0': 701.7661444854735, 'top': 683.705659906915, 'x1': 1138.350040359497, 'bottom': 720.0, 'score': 0.9959595799446106}]
2025-08-12 23:38:01,167 DEBUG    35 DiT scaled boxes for page 5 (after scale_factor=3 division): [{'type': 'table', 'score': 0.9966614246368408, 'x0': 74.33333333333333, 'x1': 538.8765870157878, 'top': 283.20860517785906, 'bottom': 482.6666666666667, 'page_number': 5}, {'type': 'title', 'score': 0.9959595799446106, 'x0': 233.9220481618245, 'x1': 379.45001345316564, 'top': 227.90188663563833, 'bottom': 240.0, 'page_number': 5}]
2025-08-12 23:38:01,188 DEBUG    35 Image 5 dimensions: (1838, 2376)
2025-08-12 23:38:01,213 DEBUG    35 OCR boxes sample for page 5: [{'x0': 454.0, 'x1': 540.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 6}, {'x0': 96.33333333333333, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 6}]
2025-08-12 23:38:01,265 DEBUG    35 DiT original boxes for page 6 (before scale_factor=3 division): [{'type': 'figure', 'x0': 203.31409614562986, 'top': 252.76574707031253, 'x1': 1619.2949395751953, 'bottom': 1261.005412649601, 'score': 0.9995813965797424}, {'type': 'figure caption', 'x0': 207.0, 'top': 1255.0, 'x1': 1601.0, 'bottom': 1334.0, 'score': 0.9984515905380249}]
2025-08-12 23:38:01,266 DEBUG    35 DiT scaled boxes for page 6 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9995813965797424, 'x0': 67.77136538187662, 'x1': 539.7649798583984, 'top': 84.25524902343751, 'bottom': 420.33513754986706, 'page_number': 6}, {'type': 'figure caption', 'score': 0.9984515905380249, 'x0': 69.0, 'x1': 533.6666666666666, 'top': 418.3333333333333, 'bottom': 444.6666666666667, 'page_number': 6}]
2025-08-12 23:38:01,295 DEBUG    35 Image 6 dimensions: (1838, 2376)
2025-08-12 23:38:01,318 DEBUG    35 OCR boxes sample for page 6: [{'x0': 453.6666666666667, 'x1': 541.0, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 7}, {'x0': 96.33333333333333, 'x1': 204.33333333333334, 'top': 44.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 7}]
2025-08-12 23:38:01,329 DEBUG    35 DiT original boxes for page 7 (before scale_factor=3 division): [{'type': 'figure', 'x0': 219.28798934936523, 'top': 253.4906343500665, 'x1': 1624.5553227233886, 'bottom': 1269.9789519614362, 'score': 0.999072790145874}, {'type': 'title', 'x0': 214.44930767059324, 'top': 1822.478541597407, 'x1': 904.2489674377441, 'bottom': 1869.7206875415561, 'score': 0.9987383484840393}]
2025-08-12 23:38:01,344 DEBUG    35 DiT scaled boxes for page 7 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.999072790145874, 'x0': 73.09599644978842, 'x1': 541.5184409077962, 'top': 84.49687811668883, 'bottom': 423.3263173204787, 'page_number': 7}, {'type': 'title', 'score': 0.9987383484840393, 'x0': 71.48310255686441, 'x1': 301.41632247924804, 'top': 607.4928471991357, 'bottom': 623.2402291805187, 'page_number': 7}]
2025-08-12 23:38:01,357 DEBUG    35 Image 7 dimensions: (1838, 2376)
2025-08-12 23:38:01,378 DEBUG    35 OCR boxes sample for page 7: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 8}, {'x0': 93.66666666666667, 'x1': 205.0, 'top': 43.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 8}]
2025-08-12 23:38:01,401 DEBUG    35 DiT original boxes for page 8 (before scale_factor=3 division): [{'type': 'title', 'x0': 213.0, 'top': 1435.0, 'x1': 427.71989154815674, 'bottom': 1484.9099588597076, 'score': 0.9985558390617371}, {'type': 'text', 'x0': 265.0, 'top': 1651.3501651845081, 'x1': 1604.1446771240232, 'bottom': 2027.5633674783912, 'score': 0.998097836971283}]
2025-08-12 23:38:01,436 DEBUG    35 DiT scaled boxes for page 8 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9985558390617371, 'x0': 71.0, 'x1': 142.5732971827189, 'top': 478.3333333333333, 'bottom': 494.96998628656917, 'page_number': 8}, {'type': 'text', 'score': 0.998097836971283, 'x0': 88.33333333333333, 'x1': 534.7148923746744, 'top': 550.4500550615027, 'bottom': 675.8544558261304, 'page_number': 8}]
2025-08-12 23:38:01,455 DEBUG    35 Image 8 dimensions: (1838, 2376)
2025-08-12 23:38:01,508 DEBUG    35 OCR boxes sample for page 8: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 9}, {'x0': 104.0, 'x1': 202.66666666666666, 'top': 44.666666666666664, 'text': 'capstone', 'bottom': 63.0, 'page_number': 9}]
2025-08-12 23:38:01,556 DEBUG    35 DiT original boxes for page 9 (before scale_factor=3 division): [{'type': 'title', 'x0': 212.33243921279907, 'top': 1213.0, 'x1': 747.0, 'bottom': 1263.174113821476, 'score': 0.9990567564964294}, {'type': 'text', 'x0': 212.0, 'top': 1476.8221045960772, 'x1': 1598.0, 'bottom': 1599.0, 'score': 0.9987744688987732}]
2025-08-12 23:38:01,557 DEBUG    35 DiT scaled boxes for page 9 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9990567564964294, 'x0': 70.77747973759969, 'x1': 249.0, 'top': 404.3333333333333, 'bottom': 421.05803794049206, 'page_number': 9}, {'type': 'text', 'score': 0.9987744688987732, 'x0': 70.66666666666667, 'x1': 532.6666666666666, 'top': 492.27403486535906, 'bottom': 533.0, 'page_number': 9}]
2025-08-12 23:38:01,572 DEBUG    35 Image 9 dimensions: (1838, 2376)
2025-08-12 23:38:01,587 DEBUG    35 OCR boxes sample for page 9: [{'x0': 453.0, 'x1': 540.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 10}, {'x0': 93.0, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 10}]
2025-08-12 23:38:01,672 DEBUG    35 DiT original boxes for page 10 (before scale_factor=3 division): [{'type': 'figure', 'x0': 206.8818539428711, 'top': 659.8786984707448, 'x1': 1517.0055671691894, 'bottom': 1471.9147221991357, 'score': 0.9987344145774841}, {'type': 'title', 'x0': 212.0, 'top': 396.0, 'x1': 892.8609593963622, 'bottom': 445.57492909532914, 'score': 0.9987090826034546}]
2025-08-12 23:38:01,683 DEBUG    35 DiT scaled boxes for page 10 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9987344145774841, 'x0': 68.96061798095702, 'x1': 505.6685223897298, 'top': 219.95956615691492, 'bottom': 490.6382407330452, 'page_number': 10}, {'type': 'title', 'score': 0.9987090826034546, 'x0': 70.66666666666667, 'x1': 297.6203197987874, 'top': 132.0, 'bottom': 148.52497636510972, 'page_number': 10}]
2025-08-12 23:38:01,738 DEBUG    35 Image 10 dimensions: (1838, 2376)
2025-08-12 23:38:01,761 DEBUG    35 OCR boxes sample for page 10: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 11}, {'x0': 95.66666666666667, 'x1': 204.33333333333334, 'top': 44.666666666666664, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 11}]
2025-08-12 23:38:01,809 DEBUG    35 DiT original boxes for page 11 (before scale_factor=3 division): [{'type': 'figure', 'x0': 213.88555496215818, 'top': 246.04621659948472, 'x1': 1529.9229748535156, 'bottom': 1063.0, 'score': 0.9993543028831482}, {'type': 'figure', 'x0': 212.10781105041502, 'top': 1162.945702086104, 'x1': 1508.2879962158202, 'bottom': 1975.0, 'score': 0.9985131621360779}]
2025-08-12 23:38:01,823 DEBUG    35 DiT scaled boxes for page 11 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9993543028831482, 'x0': 71.29518498738607, 'x1': 509.97432495117187, 'top': 82.01540553316157, 'bottom': 354.3333333333333, 'page_number': 11}, {'type': 'figure', 'score': 0.9985131621360779, 'x0': 70.70260368347168, 'x1': 502.76266540527337, 'top': 387.6485673620346, 'bottom': 658.3333333333334, 'page_number': 11}]
2025-08-12 23:38:01,840 DEBUG    35 Image 11 dimensions: (1838, 2376)
2025-08-12 23:38:01,859 DEBUG    35 OCR boxes sample for page 11: [{'x0': 453.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 12}, {'x0': 95.66666666666667, 'x1': 205.33333333333334, 'top': 44.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 12}]
2025-08-12 23:38:01,914 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.63, progress_msg: 23:38:01 Page(25~37): Layout analysis (17.26s)
2025-08-12 23:38:02,698 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.65, progress_msg: 23:38:02 Page(25~37): Table analysis (0.77s)
2025-08-12 23:38:02,709 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.67, progress_msg: 23:38:02 Page(25~37): Text merged (0.00s)
2025-08-12 23:38:03,760 INFO     35 layouts cost: 33.67173860303592s
2025-08-12 23:38:03,866 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.8, progress_msg: 23:38:03 Page(25~37): Finish parsing.
2025-08-12 23:38:04,170 INFO     35 naive_merge(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf): 0.3039524368941784
2025-08-12 23:38:04,172 INFO     35 Chunking(94.48727851302829) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf done
2025-08-12 23:38:04,335 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.1, progress_msg: 23:38:04 Page(37~46): Start to parse.
2025-08-12 23:38:04,337 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:38:04,363 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:38:04,376 INFO     35 load_model /ragflow/rag/res/deepdoc/layout.onnx reuses cached model
2025-08-12 23:38:04,378 INFO     35 load_model /ragflow/rag/res/deepdoc/tsr.onnx reuses cached model
2025-08-12 23:38:04,791 INFO     35 MINIO PUT(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf) cost 0.607 s
2025-08-12 23:38:04,805 INFO     35 Build document Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf: 95.12s
2025-08-12 23:38:04,806 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:38:04 Page(37~46): OCR started
2025-08-12 23:38:04,881 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:38:04 Page(25~37): Generate 37 chunks
2025-08-12 23:38:05,469 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 33
2025-08-12 23:38:06,024 INFO     35 __images__ dedupe_chars cost 1.2145032660337165s
2025-08-12 23:38:06,026 WARNING  35 Miss outlines
2025-08-12 23:38:06,233 INFO     35 __ocr detecting boxes of a image cost (0.16837515798397362s)
2025-08-12 23:38:06,233 INFO     35 __ocr sorting 0 chars cost 0.00020497804507613182s
2025-08-12 23:38:06,523 INFO     35 __ocr recognize 11 boxes cost 0.2603968239855021s
2025-08-12 23:38:06,701 INFO     35 __ocr detecting boxes of a image cost (0.17681982507929206s)
2025-08-12 23:38:06,702 INFO     35 __ocr sorting 0 chars cost 0.000600575003772974s
2025-08-12 23:38:07,510 INFO     35 __ocr recognize 35 boxes cost 0.7993291169404984s
2025-08-12 23:38:07,755 INFO     35 __ocr detecting boxes of a image cost (0.24364657897967845s)
2025-08-12 23:38:07,758 INFO     35 __ocr sorting 0 chars cost 0.002455806010402739s
2025-08-12 23:38:09,246 INFO     35 __ocr recognize 203 boxes cost 1.4880205480149016s
2025-08-12 23:38:09,430 INFO     35 __ocr detecting boxes of a image cost (0.1823611279251054s)
2025-08-12 23:38:09,431 INFO     35 __ocr sorting 0 chars cost 0.0006185940001159906s
2025-08-12 23:38:10,744 INFO     35 __ocr recognize 36 boxes cost 1.3110172449378297s
2025-08-12 23:38:10,915 INFO     35 __ocr detecting boxes of a image cost (0.17040717299096286s)
2025-08-12 23:38:10,917 INFO     35 __ocr sorting 0 chars cost 0.0014456870267167687s
2025-08-12 23:38:11,237 INFO     35 __ocr recognize 25 boxes cost 0.3166933869943023s
2025-08-12 23:38:11,407 INFO     35 __ocr detecting boxes of a image cost (0.16881273302715272s)
2025-08-12 23:38:11,408 INFO     35 __ocr sorting 0 chars cost 0.0008241619216278195s
2025-08-12 23:38:13,096 INFO     35 __ocr recognize 46 boxes cost 1.686834383988753s
2025-08-12 23:38:13,107 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.39999999999999997, progress_msg: 
2025-08-12 23:38:13,286 INFO     35 __ocr detecting boxes of a image cost (0.17862150399014354s)
2025-08-12 23:38:13,287 INFO     35 __ocr sorting 0 chars cost 0.0008150020148605108s
2025-08-12 23:38:13,368 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 4377
2025-08-12 23:38:13,377 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.7054054054054053, progress_msg: 
2025-08-12 23:38:14,431 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:38:14.426+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 2, "lag": 0, "done": 2, "failed": 0, "current": {"17f96a9c779211f0ac8e02420ae90a05": {"id": "17f96a9c779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 24, "to_page": 36, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979981, "task_type": ""}, "17f96b14779211f0ac8e02420ae90a05": {"id": "17f96b14779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 36, "to_page": 45, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979982, "task_type": ""}}}
2025-08-12 23:38:14,718 INFO     35 __ocr recognize 45 boxes cost 1.4257312499685213s
2025-08-12 23:38:14,898 INFO     35 __ocr detecting boxes of a image cost (0.17879084195010364s)
2025-08-12 23:38:14,899 INFO     35 __ocr sorting 0 chars cost 0.0007747790077701211s
2025-08-12 23:38:15,972 INFO     35 __ocr recognize 45 boxes cost 1.0713258020114154s
2025-08-12 23:38:16,174 INFO     35 __ocr detecting boxes of a image cost (0.2000242000212893s)
2025-08-12 23:38:16,178 INFO     35 __ocr sorting 0 chars cost 0.004265215015038848s
2025-08-12 23:38:16,661 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 571
2025-08-12 23:38:16,668 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.7918918918918918, progress_msg: 
2025-08-12 23:38:17,049 INFO     35 __ocr recognize 100 boxes cost 0.8703734000446275s
2025-08-12 23:38:17,051 INFO     35 __images__ 9 pages cost 10.988563564023934s
2025-08-12 23:38:17,069 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:38:17 Page(37~46): OCR finished (12.38s)
2025-08-12 23:38:17,069 INFO     35 OCR(36~45): 12.39s
2025-08-12 23:38:17,071 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:17,487 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 117
2025-08-12 23:38:17,506 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:17,507 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.8783783783783783, progress_msg: 
2025-08-12 23:38:17,508 DEBUG    35 DiT API response for page 0: boxes=8
2025-08-12 23:38:17,514 INFO     35 Embedding chunks (12.63s)
2025-08-12 23:38:17,514 DEBUG    35 DiT raw API boxes for page 0 (first 2): [[215.02294647216794, 247.36222677027928, 1520.1564964294432, 1052.5807897689497], [181.35596145629881, 1156.5742395279256, 1536.2563847351073, 1949.3393554687502]]
2025-08-12 23:38:17,527 DEBUG    35 DiT raw API classes for page 0 (first 2): ['FIGURE', 'FIGURE']
2025-08-12 23:38:17,531 DEBUG    35 DiT raw API scores for page 0 (first 2): [0.9994452595710754, 0.9988285899162292]
2025-08-12 23:38:17,534 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-12 23:38:17,541 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:38:17 Page(25~37): Embedding chunks (12.63s)
2025-08-12 23:38:17,588 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-12 23:38:17,643 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.021s]
2025-08-12 23:38:17,663 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 0.8027027027027027, progress_msg: 
2025-08-12 23:38:17,704 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.016s]
2025-08-12 23:38:17,753 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.020s]
2025-08-12 23:38:17,787 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.008s]
2025-08-12 23:38:17,816 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.009s]
2025-08-12 23:38:17,834 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.007s]
2025-08-12 23:38:17,849 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.006s]
2025-08-12 23:38:17,863 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.005s]
2025-08-12 23:38:17,878 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.005s]
2025-08-12 23:38:17,887 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.004s]
2025-08-12 23:38:17,891 INFO     35 Indexing doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(24-36), chunks(37), elapsed: 0.30
2025-08-12 23:38:17,940 INFO     35 set_progress(17f96a9c779211f0ac8e02420ae90a05), progress: 1.0, progress_msg: 23:38:17 Page(25~37): Indexing done (0.33s). Task done (108.30s)
2025-08-12 23:38:17,945 INFO     35 Chunk doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(24-36), chunks(37), token(5098), elapsed:108.30
2025-08-12 23:38:17,969 INFO     35 handle_task done for task {"id": "17f96a9c779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 24, "to_page": 36, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979981, "task_type": ""}
2025-08-12 23:38:18,053 DEBUG    35 Applied OCR adjustment to 8 boxes
2025-08-12 23:38:18,054 DEBUG    35 DiT processed 8 boxes for page 0, first 3: [{'type': 'figure', 'x0': 215.02294647216794, 'top': 247.36222677027928, 'x1': 1520.1564964294432, 'bottom': 1052.5807897689497, 'score': 0.9994452595710754}, {'type': 'figure', 'x0': 181.35596145629881, 'top': 1156.5742395279256, 'x1': 1536.256384735107, 'bottom': 1955.0, 'score': 0.9988285899162292}, {'type': 'figure', 'x0': 200.5224667739868, 'top': 100.07069948886304, 'x1': 613.0, 'bottom': 223.08523494639297, 'score': 0.9961923360824585}]
2025-08-12 23:38:18,055 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:18,470 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:18,471 DEBUG    35 DiT API response for page 1: boxes=11
2025-08-12 23:38:18,473 DEBUG    35 DiT raw API boxes for page 1 (first 2): [[218.1448484802246, 249.4913070354056, 1516.6286335754394, 1185.391611951463], [213.36436153411864, 1299.8588659408247, 961.3203314208984, 1349.4403985206118]]
2025-08-12 23:38:18,474 DEBUG    35 DiT raw API classes for page 1 (first 2): ['FIGURE', 'TITLE']
2025-08-12 23:38:18,477 DEBUG    35 DiT raw API scores for page 1 (first 2): [0.999144434928894, 0.9990803003311157]
2025-08-12 23:38:19,334 DEBUG    35 Applied OCR adjustment to 11 boxes
2025-08-12 23:38:19,334 DEBUG    35 DiT processed 11 boxes for page 1, first 3: [{'type': 'figure', 'x0': 218.1448484802246, 'top': 249.4913070354056, 'x1': 1516.6286335754394, 'bottom': 1185.391611951463, 'score': 0.999144434928894}, {'type': 'title', 'x0': 213.36436153411864, 'top': 1299.8588659408247, 'x1': 961.3203314208984, 'bottom': 1349.4403985206118, 'score': 0.9990803003311157}, {'type': 'text', 'x0': 267.0, 'top': 1923.535275723072, 'x1': 1619.0456137084957, 'bottom': 2146.0, 'score': 0.9990430474281311}]
2025-08-12 23:38:19,335 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:19,757 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:19,758 DEBUG    35 DiT API response for page 2: boxes=10
2025-08-12 23:38:19,760 DEBUG    35 DiT raw API boxes for page 2 (first 2): [[241.7719800567627, 374.80550303357717, 1686.5989007568357, 1408.8540870179522], [275.9302197647095, 1791.5418363530587, 936.1595933532715, 2027.4114756482716]]
2025-08-12 23:38:19,767 DEBUG    35 DiT raw API classes for page 2 (first 2): ['TABLE', 'LIST']
2025-08-12 23:38:19,771 DEBUG    35 DiT raw API scores for page 2 (first 2): [0.9986308217048645, 0.9982726573944092]
2025-08-12 23:38:21,251 DEBUG    35 Applied OCR adjustment to 10 boxes
2025-08-12 23:38:21,251 DEBUG    35 DiT processed 10 boxes for page 2, first 3: [{'type': 'table', 'x0': 222.0, 'top': 374.80550303357717, 'x1': 1686.5989007568357, 'bottom': 1408.8540870179522, 'score': 0.9986308217048645}, {'type': 'text', 'x0': 275.9302197647095, 'top': 1791.5418363530587, 'x1': 936.1595933532715, 'bottom': 2032.0, 'score': 0.9982726573944092}, {'type': 'title', 'x0': 215.0, 'top': 1470.057658743351, 'x1': 1026.2973446655274, 'bottom': 1511.5694294381651, 'score': 0.9972978234291077}]
2025-08-12 23:38:21,283 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:21,705 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:21,706 DEBUG    35 DiT API response for page 3: boxes=12
2025-08-12 23:38:21,708 DEBUG    35 DiT raw API boxes for page 3 (first 2): [[216.5519955444336, 937.4667683053525, 806.9182069396973, 976.9622906624004], [230.1078625488281, 1526.5849817154258, 1550.5248918151854, 1644.761594082447]]
2025-08-12 23:38:21,711 DEBUG    35 DiT raw API classes for page 3 (first 2): ['TITLE', 'TEXT']
2025-08-12 23:38:21,713 DEBUG    35 DiT raw API scores for page 3 (first 2): [0.9986679553985596, 0.9984859824180603]
2025-08-12 23:38:22,847 DEBUG    35 Applied OCR adjustment to 12 boxes
2025-08-12 23:38:22,848 DEBUG    35 DiT processed 12 boxes for page 3, first 3: [{'type': 'title', 'x0': 212.0, 'top': 937.4667683053525, 'x1': 806.9182069396973, 'bottom': 980.0, 'score': 0.9986679553985596}, {'type': 'text', 'x0': 212.0, 'top': 1526.5849817154258, 'x1': 1551.0, 'bottom': 1646.0, 'score': 0.9984859824180603}, {'type': 'text', 'x0': 210.0, 'top': 1273.1427339594416, 'x1': 1609.3944029235838, 'bottom': 1436.0, 'score': 0.997735857963562}]
2025-08-12 23:38:22,850 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:23,257 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:23,258 DEBUG    35 DiT API response for page 4: boxes=7
2025-08-12 23:38:23,260 DEBUG    35 DiT raw API boxes for page 4 (first 2): [[198.7149594116211, 246.70963887965428, 1590.301061706543, 2096.1298360621677], [200.82492162704466, 101.91194087901015, 616.903219833374, 220.22288740442156]]
2025-08-12 23:38:23,262 DEBUG    35 DiT raw API classes for page 4 (first 2): ['FIGURE', 'LOGO']
2025-08-12 23:38:23,264 DEBUG    35 DiT raw API scores for page 4 (first 2): [0.9980666041374207, 0.9937829375267029]
2025-08-12 23:38:23,627 DEBUG    35 Applied OCR adjustment to 7 boxes
2025-08-12 23:38:23,627 DEBUG    35 DiT processed 7 boxes for page 4, first 3: [{'type': 'figure', 'x0': 198.7149594116211, 'top': 246.70963887965428, 'x1': 1590.301061706543, 'bottom': 2109.0, 'score': 0.9980666041374207}, {'type': 'figure', 'x0': 200.82492162704466, 'top': 101.91194087901015, 'x1': 616.903219833374, 'bottom': 220.22288740442156, 'score': 0.9937829375267029}, {'type': 'figure caption', 'x0': 215.0, 'top': 2122.5472334192154, 'x1': 792.0, 'bottom': 2158.843069522939, 'score': 0.9930760860443115}]
2025-08-12 23:38:23,631 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:24,055 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:24,056 DEBUG    35 DiT API response for page 5: boxes=15
2025-08-12 23:38:24,060 DEBUG    35 DiT raw API boxes for page 5 (first 2): [[218.2749803161621, 248.71857663418388, 1041.3258889770507, 298.40527993060175], [232.28862251281737, 1224.4206594913564, 1559.4295272827148, 1341.6264544547873]]
2025-08-12 23:38:24,062 DEBUG    35 DiT raw API classes for page 5 (first 2): ['TITLE', 'TEXT']
2025-08-12 23:38:24,066 DEBUG    35 DiT raw API scores for page 5 (first 2): [0.9990291595458984, 0.9984196424484253]
2025-08-12 23:38:25,476 DEBUG    35 Applied OCR adjustment to 15 boxes
2025-08-12 23:38:25,476 DEBUG    35 DiT processed 15 boxes for page 5, first 3: [{'type': 'title', 'x0': 212.0, 'top': 248.71857663418388, 'x1': 1041.3258889770507, 'bottom': 299.0, 'score': 0.9990291595458984}, {'type': 'text', 'x0': 210.0, 'top': 1224.4206594913564, 'x1': 1559.4295272827148, 'bottom': 1351.0, 'score': 0.9984196424484253}, {'type': 'text', 'x0': 212.0, 'top': 991.9640983419216, 'x1': 1606.0, 'bottom': 1200.0, 'score': 0.9984184503555298}]
2025-08-12 23:38:25,477 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:25,894 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:25,895 DEBUG    35 DiT API response for page 6: boxes=13
2025-08-12 23:38:25,901 DEBUG    35 DiT raw API boxes for page 6 (first 2): [[289.6341327667236, 347.4475824883644, 1618.5475228881835, 895.9124314328458], [217.18134002685545, 1723.1898115442157, 1619.3567802429197, 2104.957514336769]]
2025-08-12 23:38:25,905 DEBUG    35 DiT raw API classes for page 6 (first 2): ['LIST', 'TEXT']
2025-08-12 23:38:25,909 DEBUG    35 DiT raw API scores for page 6 (first 2): [0.9993700385093689, 0.9978469610214233]
2025-08-12 23:38:27,240 DEBUG    35 Applied OCR adjustment to 13 boxes
2025-08-12 23:38:27,240 DEBUG    35 DiT processed 13 boxes for page 6, first 3: [{'type': 'text', 'x0': 265.0, 'top': 347.4475824883644, 'x1': 1618.5475228881837, 'bottom': 898.0, 'score': 0.9993700385093689}, {'type': 'text', 'x0': 210.0, 'top': 1693.0, 'x1': 1619.3567802429197, 'bottom': 2104.957514336769, 'score': 0.9978469610214233}, {'type': 'text', 'x0': 206.27466552734373, 'top': 1151.884176051363, 'x1': 1626.0, 'bottom': 1401.0, 'score': 0.9977502226829529}]
2025-08-12 23:38:27,242 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:27,648 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:27,649 DEBUG    35 DiT API response for page 7: boxes=12
2025-08-12 23:38:27,653 DEBUG    35 DiT raw API boxes for page 7 (first 2): [[217.99739845275877, 2004.9279785156252, 1627.139309082031, 2121.4681422456783], [213.52944526672363, 937.3508508560507, 793.9384677886962, 974.899128366024]]
2025-08-12 23:38:27,655 DEBUG    35 DiT raw API classes for page 7 (first 2): ['CAPTION', 'TITLE']
2025-08-12 23:38:27,657 DEBUG    35 DiT raw API scores for page 7 (first 2): [0.9992295503616333, 0.9991201758384705]
2025-08-12 23:38:28,694 DEBUG    35 Applied OCR adjustment to 12 boxes
2025-08-12 23:38:28,695 DEBUG    35 DiT processed 12 boxes for page 7, first 3: [{'type': 'figure caption', 'x0': 210.0, 'top': 2004.9279785156252, 'x1': 1627.139309082031, 'bottom': 2126.0, 'score': 0.9992295503616333}, {'type': 'title', 'x0': 212.0, 'top': 937.3508508560507, 'x1': 793.9384677886962, 'bottom': 975.0, 'score': 0.9991201758384705}, {'type': 'figure', 'x0': 409.66378379821776, 'top': 1275.726858585439, 'x1': 1423.9317163085936, 'bottom': 2007.0, 'score': 0.9988471269607544}]
2025-08-12 23:38:28,697 DEBUG    35 DiT API call with original image size: (1838, 2376)
2025-08-12 23:38:29,099 DEBUG    35 DiT API call successful for image size: (1838, 2376)
2025-08-12 23:38:29,100 DEBUG    35 DiT API response for page 8: boxes=10
2025-08-12 23:38:29,105 DEBUG    35 DiT raw API boxes for page 8 (first 2): [[215.53849571228025, 395.55847817278925, 1008.2710704803467, 435.58246628781586], [251.3858200073242, 638.7806006898272, 1623.9031210327148, 2027.3353193567157]]
2025-08-12 23:38:29,107 DEBUG    35 DiT raw API classes for page 8 (first 2): ['TITLE', 'TABLE']
2025-08-12 23:38:29,113 DEBUG    35 DiT raw API scores for page 8 (first 2): [0.9981411695480347, 0.9966901540756226]
2025-08-12 23:38:30,097 DEBUG    35 Applied OCR adjustment to 10 boxes
2025-08-12 23:38:30,098 DEBUG    35 DiT processed 10 boxes for page 8, first 3: [{'type': 'title', 'x0': 215.0, 'top': 395.55847817278925, 'x1': 1008.2710704803467, 'bottom': 436.0, 'score': 0.9981411695480347}, {'type': 'table', 'x0': 231.0, 'top': 638.7806006898272, 'x1': 1628.0, 'bottom': 2036.0, 'score': 0.9966901540756226}, {'type': 'footer', 'x0': 1458.0, 'top': 2194.706371550865, 'x1': 1627.2735075378416, 'bottom': 2237.0, 'score': 0.9929965138435364}]
2025-08-12 23:38:30,100 DEBUG    35 DiT original boxes for page 0 (before scale_factor=3 division): [{'type': 'figure', 'x0': 215.02294647216794, 'top': 247.36222677027928, 'x1': 1520.1564964294432, 'bottom': 1052.5807897689497, 'score': 0.9994452595710754}, {'type': 'figure', 'x0': 181.35596145629881, 'top': 1156.5742395279256, 'x1': 1536.256384735107, 'bottom': 1955.0, 'score': 0.9988285899162292}]
2025-08-12 23:38:30,102 DEBUG    35 DiT scaled boxes for page 0 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9994452595710754, 'x0': 71.67431549072265, 'x1': 506.7188321431477, 'top': 82.4540755900931, 'bottom': 350.86026325631656, 'page_number': 0}, {'type': 'figure', 'score': 0.9988285899162292, 'x0': 60.451987152099605, 'x1': 512.0854615783691, 'top': 385.5247465093085, 'bottom': 651.6666666666666, 'page_number': 0}]
2025-08-12 23:38:30,109 DEBUG    35 Image 0 dimensions: (1838, 2376)
2025-08-12 23:38:30,112 DEBUG    35 OCR boxes sample for page 0: [{'x0': 453.0, 'x1': 539.3333333333334, 'top': 34.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.333333333333336, 'page_number': 1}, {'x0': 95.66666666666667, 'x1': 204.33333333333334, 'top': 44.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 1}]
2025-08-12 23:38:30,116 DEBUG    35 DiT original boxes for page 1 (before scale_factor=3 division): [{'type': 'figure', 'x0': 218.1448484802246, 'top': 249.4913070354056, 'x1': 1516.6286335754394, 'bottom': 1185.391611951463, 'score': 0.999144434928894}, {'type': 'title', 'x0': 213.36436153411864, 'top': 1299.8588659408247, 'x1': 961.3203314208984, 'bottom': 1349.4403985206118, 'score': 0.9990803003311157}]
2025-08-12 23:38:30,117 DEBUG    35 DiT scaled boxes for page 1 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.999144434928894, 'x0': 72.7149494934082, 'x1': 505.5428778584798, 'top': 83.16376901180188, 'bottom': 395.1305373171543, 'page_number': 1}, {'type': 'title', 'score': 0.9990803003311157, 'x0': 71.12145384470621, 'x1': 320.4401104736328, 'top': 433.28628864694156, 'bottom': 449.8134661735373, 'page_number': 1}]
2025-08-12 23:38:30,119 DEBUG    35 Image 1 dimensions: (1838, 2376)
2025-08-12 23:38:30,122 DEBUG    35 OCR boxes sample for page 1: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 2}, {'x0': 94.0, 'x1': 205.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 2}]
2025-08-12 23:38:30,128 DEBUG    35 DiT original boxes for page 2 (before scale_factor=3 division): [{'type': 'table', 'x0': 222.0, 'top': 374.80550303357717, 'x1': 1686.5989007568357, 'bottom': 1408.8540870179522, 'score': 0.9986308217048645}, {'type': 'text', 'x0': 275.9302197647095, 'top': 1791.5418363530587, 'x1': 936.1595933532715, 'bottom': 2032.0, 'score': 0.9982726573944092}]
2025-08-12 23:38:30,128 DEBUG    35 DiT scaled boxes for page 2 (after scale_factor=3 division): [{'type': 'table', 'score': 0.9986308217048645, 'x0': 74.0, 'x1': 562.1996335856119, 'top': 124.93516767785906, 'bottom': 469.61802900598406, 'page_number': 2}, {'type': 'text', 'score': 0.9982726573944092, 'x0': 91.97673992156983, 'x1': 312.05319778442384, 'top': 597.1806121176862, 'bottom': 677.3333333333334, 'page_number': 2}]
2025-08-12 23:38:30,130 DEBUG    35 Image 2 dimensions: (1838, 2376)
2025-08-12 23:38:30,133 DEBUG    35 OCR boxes sample for page 2: [{'x0': 452.3333333333333, 'x1': 541.3333333333334, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 46.0, 'page_number': 3}, {'x0': 100.66666666666667, 'x1': 204.66666666666666, 'top': 43.0, 'text': 'capstone', 'bottom': 63.666666666666664, 'page_number': 3}]
2025-08-12 23:38:30,152 DEBUG    35 DiT original boxes for page 3 (before scale_factor=3 division): [{'type': 'title', 'x0': 212.0, 'top': 937.4667683053525, 'x1': 806.9182069396973, 'bottom': 980.0, 'score': 0.9986679553985596}, {'type': 'text', 'x0': 212.0, 'top': 1526.5849817154258, 'x1': 1551.0, 'bottom': 1646.0, 'score': 0.9984859824180603}]
2025-08-12 23:38:30,152 DEBUG    35 DiT scaled boxes for page 3 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9986679553985596, 'x0': 70.66666666666667, 'x1': 268.97273564656575, 'top': 312.48892276845083, 'bottom': 326.6666666666667, 'page_number': 3}, {'type': 'text', 'score': 0.9984859824180603, 'x0': 70.66666666666667, 'x1': 517.0, 'top': 508.8616605718086, 'bottom': 548.6666666666666, 'page_number': 3}]
2025-08-12 23:38:30,154 DEBUG    35 Image 3 dimensions: (1838, 2376)
2025-08-12 23:38:30,156 DEBUG    35 OCR boxes sample for page 3: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 36.333333333333336, 'text': 'PintoValleyMine', 'bottom': 48.0, 'page_number': 4}, {'x0': 98.0, 'x1': 202.66666666666666, 'top': 46.333333333333336, 'text': 'capstone', 'bottom': 63.666666666666664, 'page_number': 4}]
2025-08-12 23:38:30,162 DEBUG    35 DiT original boxes for page 4 (before scale_factor=3 division): [{'type': 'figure', 'x0': 198.7149594116211, 'top': 246.70963887965428, 'x1': 1590.301061706543, 'bottom': 2109.0, 'score': 0.9980666041374207}, {'type': 'figure', 'x0': 200.82492162704466, 'top': 101.91194087901015, 'x1': 616.903219833374, 'bottom': 220.22288740442156, 'score': 0.9937829375267029}]
2025-08-12 23:38:30,165 DEBUG    35 DiT scaled boxes for page 4 (after scale_factor=3 division): [{'type': 'figure', 'score': 0.9980666041374207, 'x0': 66.2383198038737, 'x1': 530.1003539021809, 'top': 82.2365462932181, 'bottom': 703.0, 'page_number': 4}, {'type': 'figure', 'score': 0.9937829375267029, 'x0': 66.94164054234822, 'x1': 205.63440661112466, 'top': 33.97064695967005, 'bottom': 73.40762913480718, 'page_number': 4}]
2025-08-12 23:38:30,169 DEBUG    35 Image 4 dimensions: (1838, 2376)
2025-08-12 23:38:30,172 DEBUG    35 OCR boxes sample for page 4: [{'x0': 452.3333333333333, 'x1': 542.0, 'top': 33.0, 'text': 'PintoValleyMine', 'bottom': 46.0, 'page_number': 5}, {'x0': 98.66666666666667, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 66.33333333333333, 'page_number': 5}]
2025-08-12 23:38:30,176 DEBUG    35 DiT original boxes for page 5 (before scale_factor=3 division): [{'type': 'title', 'x0': 212.0, 'top': 248.71857663418388, 'x1': 1041.3258889770507, 'bottom': 299.0, 'score': 0.9990291595458984}, {'type': 'text', 'x0': 210.0, 'top': 1224.4206594913564, 'x1': 1559.4295272827148, 'bottom': 1351.0, 'score': 0.9984196424484253}]
2025-08-12 23:38:30,177 DEBUG    35 DiT scaled boxes for page 5 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9990291595458984, 'x0': 70.66666666666667, 'x1': 347.1086296590169, 'top': 82.90619221139463, 'bottom': 99.66666666666667, 'page_number': 5}, {'type': 'text', 'score': 0.9984196424484253, 'x0': 70.0, 'x1': 519.8098424275717, 'top': 408.14021983045217, 'bottom': 450.3333333333333, 'page_number': 5}]
2025-08-12 23:38:30,179 DEBUG    35 Image 5 dimensions: (1838, 2376)
2025-08-12 23:38:30,181 DEBUG    35 OCR boxes sample for page 5: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 36.333333333333336, 'text': 'PintoValleyMine', 'bottom': 48.0, 'page_number': 6}, {'x0': 93.0, 'x1': 203.33333333333334, 'top': 45.666666666666664, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 6}]
2025-08-12 23:38:30,194 DEBUG    35 DiT original boxes for page 6 (before scale_factor=3 division): [{'type': 'text', 'x0': 265.0, 'top': 347.4475824883644, 'x1': 1618.5475228881837, 'bottom': 898.0, 'score': 0.9993700385093689}, {'type': 'text', 'x0': 210.0, 'top': 1693.0, 'x1': 1619.3567802429197, 'bottom': 2104.957514336769, 'score': 0.9978469610214233}]
2025-08-12 23:38:30,194 DEBUG    35 DiT scaled boxes for page 6 (after scale_factor=3 division): [{'type': 'text', 'score': 0.9993700385093689, 'x0': 88.33333333333333, 'x1': 539.5158409627279, 'top': 115.81586082945479, 'bottom': 299.3333333333333, 'page_number': 6}, {'type': 'text', 'score': 0.9978469610214233, 'x0': 70.0, 'x1': 539.7855934143066, 'top': 564.3333333333334, 'bottom': 701.6525047789229, 'page_number': 6}]
2025-08-12 23:38:30,197 DEBUG    35 Image 6 dimensions: (1838, 2376)
2025-08-12 23:38:30,199 DEBUG    35 OCR boxes sample for page 6: [{'x0': 454.6666666666667, 'x1': 539.3333333333334, 'top': 36.333333333333336, 'text': 'PintoValleyMine', 'bottom': 48.0, 'page_number': 7}, {'x0': 94.66666666666667, 'x1': 202.66666666666666, 'top': 46.333333333333336, 'text': 'capstone', 'bottom': 63.666666666666664, 'page_number': 7}]
2025-08-12 23:38:30,209 DEBUG    35 DiT original boxes for page 7 (before scale_factor=3 division): [{'type': 'figure caption', 'x0': 210.0, 'top': 2004.9279785156252, 'x1': 1627.139309082031, 'bottom': 2126.0, 'score': 0.9992295503616333}, {'type': 'title', 'x0': 212.0, 'top': 937.3508508560507, 'x1': 793.9384677886962, 'bottom': 975.0, 'score': 0.9991201758384705}]
2025-08-12 23:38:30,209 DEBUG    35 DiT scaled boxes for page 7 (after scale_factor=3 division): [{'type': 'figure caption', 'score': 0.9992295503616333, 'x0': 70.0, 'x1': 542.3797696940104, 'top': 668.3093261718751, 'bottom': 708.6666666666666, 'page_number': 7}, {'type': 'title', 'score': 0.9991201758384705, 'x0': 70.66666666666667, 'x1': 264.6461559295654, 'top': 312.45028361868356, 'bottom': 325.0, 'page_number': 7}]
2025-08-12 23:38:30,211 DEBUG    35 Image 7 dimensions: (1838, 2376)
2025-08-12 23:38:30,213 DEBUG    35 OCR boxes sample for page 7: [{'x0': 454.6666666666667, 'x1': 540.3333333333334, 'top': 35.333333333333336, 'text': 'PintoValleyMine', 'bottom': 47.0, 'page_number': 8}, {'x0': 99.0, 'x1': 204.33333333333334, 'top': 43.0, 'text': 'capstone', 'bottom': 65.33333333333333, 'page_number': 8}]
2025-08-12 23:38:30,229 DEBUG    35 DiT original boxes for page 8 (before scale_factor=3 division): [{'type': 'title', 'x0': 215.0, 'top': 395.55847817278925, 'x1': 1008.2710704803467, 'bottom': 436.0, 'score': 0.9981411695480347}, {'type': 'table', 'x0': 231.0, 'top': 638.7806006898272, 'x1': 1628.0, 'bottom': 2036.0, 'score': 0.9966901540756226}]
2025-08-12 23:38:30,229 DEBUG    35 DiT scaled boxes for page 8 (after scale_factor=3 division): [{'type': 'title', 'score': 0.9981411695480347, 'x0': 71.66666666666667, 'x1': 336.0903568267822, 'top': 131.85282605759642, 'bottom': 145.33333333333334, 'page_number': 8}, {'type': 'table', 'score': 0.9966901540756226, 'x0': 77.0, 'x1': 542.6666666666666, 'top': 212.92686689660908, 'bottom': 678.6666666666666, 'page_number': 8}]
2025-08-12 23:38:30,231 DEBUG    35 Image 8 dimensions: (1838, 2376)
2025-08-12 23:38:30,237 DEBUG    35 OCR boxes sample for page 8: [{'x0': 453.6666666666667, 'x1': 540.3333333333334, 'top': 33.666666666666664, 'text': 'PintoValleyMine', 'bottom': 46.0, 'page_number': 9}, {'x0': 117.33333333333333, 'x1': 204.66666666666666, 'top': 43.0, 'text': 'apstone', 'bottom': 62.666666666666664, 'page_number': 9}]
2025-08-12 23:38:30,288 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.63, progress_msg: 23:38:30 Page(37~46): Layout analysis (13.19s)
2025-08-12 23:38:30,578 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.65, progress_msg: 23:38:30 Page(37~46): Table analysis (0.28s)
2025-08-12 23:38:30,601 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.67, progress_msg: 23:38:30 Page(37~46): Text merged (0.00s)
2025-08-12 23:38:31,612 INFO     35 layouts cost: 26.93700944702141s
2025-08-12 23:38:31,668 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.8, progress_msg: 23:38:31 Page(37~46): Finish parsing.
2025-08-12 23:38:32,016 INFO     35 naive_merge(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf): 0.2619311569724232
2025-08-12 23:38:32,017 INFO     35 Chunking(122.24769584008027) Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf/Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf done
2025-08-12 23:38:32,388 INFO     35 MINIO PUT(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf) cost 0.365 s
2025-08-12 23:38:32,402 INFO     35 Build document Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf: 122.63s
2025-08-12 23:38:32,420 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:38:32 Page(37~46): Generate 23 chunks
2025-08-12 23:38:32,754 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 33
2025-08-12 23:38:38,057 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 4180
2025-08-12 23:38:38,075 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.708695652173913, progress_msg: 
2025-08-12 23:38:38,911 ERROR    35 LLMBundle.encode can't update token usage for 6d948564779111f0aca102420ae90a05/EMBEDDING used_tokens: 178
2025-08-12 23:38:38,933 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.8478260869565217, progress_msg: 
2025-08-12 23:38:38,942 INFO     35 Embedding chunks (6.52s)
2025-08-12 23:38:38,961 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: None, progress_msg: 23:38:38 Page(37~46): Embedding chunks (6.52s)
2025-08-12 23:38:39,002 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.020s]
2025-08-12 23:38:39,021 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 0.8043478260869565, progress_msg: 
2025-08-12 23:38:39,060 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.015s]
2025-08-12 23:38:39,101 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.016s]
2025-08-12 23:38:39,142 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.017s]
2025-08-12 23:38:39,175 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.009s]
2025-08-12 23:38:39,204 INFO     35 PUT http://es01:9200/ragflow_6d948564779111f0aca102420ae90a05/_bulk?refresh=false&timeout=60s [status:200 duration:0.009s]
2025-08-12 23:38:39,211 INFO     35 Indexing doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(36-45), chunks(23), elapsed: 0.25
2025-08-12 23:38:39,248 INFO     35 set_progress(17f96b14779211f0ac8e02420ae90a05), progress: 1.0, progress_msg: 23:38:39 Page(37~46): Indexing done (0.26s). Task done (129.52s)
2025-08-12 23:38:39,249 INFO     35 Chunk doc(Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf), page(36-45), chunks(23), token(4391), elapsed:129.52
2025-08-12 23:38:39,252 INFO     35 handle_task done for task {"id": "17f96b14779211f0ac8e02420ae90a05", "doc_id": "160732c8779211f0a16502420ae90a05", "from_page": 36, "to_page": 45, "retry_count": 0, "kb_id": "05697d4a779211f08aea02420ae90a05", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "type": "pdf", "location": "Capstone_Mining_Pinto_Valley_Mine_Gila_County_AZ_Technical Report_NI 43-101_11Jun2021.pdf", "size": 12549890, "tenant_id": "6d948564779111f0aca102420ae90a05", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 512, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "qwen2-vl-7b-instruct", "asr_id": "paraformer-zh", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct", "update_time": 1755012979982, "task_type": ""}
2025-08-12 23:38:44,438 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:38:44.436+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:39:14,445 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:39:14.443+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:39:44,452 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:39:44.450+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:40:14,460 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:40:14.458+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:40:44,488 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:40:44.486+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:41:14,494 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:41:14.492+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:41:44,501 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:41:44.499+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:42:14,508 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:42:14.506+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:42:44,514 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:42:44.513+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:43:14,519 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:43:14.517+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:43:44,526 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:43:44.525+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:44:14,534 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:44:14.532+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:44:44,541 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:44:44.539+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:45:14,549 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:45:14.547+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:45:44,556 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:45:44.554+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:46:14,563 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:46:14.561+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:46:44,570 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:46:44.568+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:47:14,578 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:47:14.576+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
2025-08-12 23:47:44,585 INFO     35 task_executor_5c26685c2fac_0 reported heartbeat: {"name": "task_executor_5c26685c2fac_0", "now": "2025-08-12T23:47:44.583+08:00", "boot_at": "2025-08-12T21:37:42.334+08:00", "pending": 0, "lag": 0, "done": 4, "failed": 0, "current": {}}
