#!/usr/bin/env python3
"""
Complete RAGFlow User Creation Script
Creates a fully functional user account with proper RSA password handling and tenant setup.
"""

import sys
import os
import base64

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import LLMService, TenantLLMService
from api.db.services.file_service import FileService
from api.db import FileType, UserTenantRole
from api.utils import get_uuid, get_format_time
from api.utils.t_crypt import crypt
from api.utils import decrypt
from werkzeug.security import generate_password_hash
from api import settings


def create_complete_user(email, nickname, password):

    
    print("Creating complete user account")
    print(f"Email: {email}")
    print(f"Nickname: {nickname}")
    print(f"Password: {password}")
    
    # Step 1: Check if user already exists
    print("Step 1: Checking if user exists")
    existing_users = UserService.query(email=email)
    if existing_users:
        print(f"User with email {email} already exists")
        return False
    print("User does not exist, proceeding with creation")

    # Step 2: Create user account with proper RSA password handling
    print("Step 2: Creating user account")
    try:
        user_id = get_uuid()
        
        # The key insight: We need to create a password hash that works with RSA decryption
        # When user logs in via website:
        # 1. Frontend encrypts Base64.encode(password) with RSA
        # 2. Backend decrypts RSA, gets Base64.encode(password)
        # 3. Backend checks hash against Base64.encode(password)

        # So we need to hash the base64-encoded version of the password
        base64_password = base64.b64encode(password.encode('utf-8')).decode('utf-8')
        password_hash = generate_password_hash(base64_password)

        print(f"Debug: Base64 password for hashing: {base64_password}")
        print(f"Debug: Generated hash: {password_hash[:50]}...")
        
        user_dict = {
            "id": user_id,
            "access_token": get_uuid(),
            "email": email,
            "nickname": nickname,
            "password": base64_password,  # Store base64 password directly, UserService.save will hash it
            "login_channel": "admin_created",
            "last_login_time": get_format_time(),
            "is_superuser": False,
            "creator": "system",
            "status": "1",
        }
        
        # Create user
        if not UserService.save(**user_dict):
            print("Failed to create user account")
            return False
        
        print("User account created successfully")
        
    except Exception as e:
        print(f"Error creating user: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Step 3: Create tenant setup
    print("Step 3: Creating tenant setup")
    try:
        # Use default parser_ids if settings.PARSERS is None
        default_parsers = "naive:General,qa:Q&A,resume:Resume,manual:Manual,table:Table,paper:Paper,book:Book,laws:Laws,presentation:Presentation,picture:Picture,one:One,audio:Audio,email:Email,tag:Tag"
        parser_ids = settings.PARSERS if settings.PARSERS else default_parsers
        
        # Create tenant
        tenant = {
            "id": user_id,
            "name": nickname + "'s Kingdom",
            "llm_id": settings.CHAT_MDL or "Qwen/Qwen2.5-VL-72B-Instruct",
            "embd_id": settings.EMBEDDING_MDL or "Alibaba-NLP/gte-large-en-v1.5",
            "asr_id": settings.ASR_MDL or "paraformer-zh",
            "parser_ids": parser_ids,
            "img2txt_id": settings.IMAGE2TEXT_MDL or "qwen2-vl-7b-instruct",
            "rerank_id": settings.RERANK_MDL or "BAAI/bge-reranker-v2-m3",
        }
        TenantService.insert(**tenant)
        print("Tenant created")
        
        # Create user-tenant relationship
        usr_tenant = {
            "tenant_id": user_id,
            "user_id": user_id,
            "invited_by": user_id,
            "role": UserTenantRole.OWNER,
        }
        UserTenantService.insert(**usr_tenant)
        print("User-tenant relationship created")
        
        # Create LLM configurations
        tenant_llm = []
        
        # Add factory LLMs
        try:
            for llm in LLMService.query(fid=settings.LLM_FACTORY):
                tenant_llm.append({
                    "tenant_id": user_id,
                    "llm_factory": settings.LLM_FACTORY,
                    "llm_name": llm.llm_name,
                    "model_type": llm.model_type,
                    "api_key": settings.API_KEY,
                    "api_base": settings.LLM_BASE_URL,
                    "max_tokens": llm.max_tokens if llm.max_tokens else 32000,
                })
        except Exception as e:
            print(f"Warning: Could not add factory LLMs: {e}")
        
        # Add builtin embedding models
        if settings.LIGHTEN != 1:
            try:
                for buildin_embedding_model in settings.BUILTIN_EMBEDDING_MODELS:
                    mdlnm, fid = TenantLLMService.split_model_name_and_factory(buildin_embedding_model)
                    tenant_llm.append({
                        "tenant_id": user_id,
                        "llm_factory": fid,
                        "llm_name": mdlnm,
                        "model_type": "embedding",
                        "api_key": "",
                        "api_base": "",
                        "max_tokens": 32000 if buildin_embedding_model == "BAAI/bge-large-zh-v1.5@BAAI" else 512,
                    })
            except Exception as e:
                print(f"Warning: Could not add builtin embedding models: {e}")
        
        if tenant_llm:
            TenantLLMService.insert_many(tenant_llm)
            print(f"{len(tenant_llm)} LLM configurations created")
        else:
            print("No LLM configurations created")
        
        # Create root folder
        file_id = get_uuid()
        file = {
            "id": file_id,
            "parent_id": file_id,
            "tenant_id": user_id,
            "created_by": user_id,
            "name": "/",
            "type": FileType.FOLDER.value,
            "size": 0,
            "location": "",
        }
        FileService.insert(file)
        print("Root folder created")
        
    except Exception as e:
        print(f"Error creating tenant setup: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Step 4: Test the complete setup
    print("Step 4: Testing complete setup")
    try:
        # Test RSA login flow (like website does)
        encrypted_password = crypt(password)
        decrypted_password = decrypt(encrypted_password)

        print(f"Debug: Original password: {password}")
        print(f"Debug: Decrypted password: {decrypted_password}")
        print(f"Debug: Base64 of original: {base64.b64encode(password.encode()).decode()}")

        # Test login with decrypted password
        test_user = UserService.query_user(email, decrypted_password)
        if test_user:
            print("RSA login flow test: PASSED")
        else:
            print("RSA login flow test: FAILED")
            # Try with base64 encoded password directly
            base64_pwd = base64.b64encode(password.encode()).decode()
            test_user2 = UserService.query_user(email, base64_pwd)
            if test_user2:
                print("Debug: Login works with base64 password directly")
            else:
                print("Debug: Login fails even with base64 password")
            return False
        
        # Test tenant access
        tenant_info = TenantService.get_info_by(user_id)
        if tenant_info:
            print("Tenant access test: PASSED")
        else:
            print("Tenant access test: FAILED")
            return False
        
    except Exception as e:
        print(f"c Error testing setup: {e}")
        return False
    
    # Success!
    print("User account created successfully")
    print(f"Email: {email}")
    print(f"Nickname: {nickname}")
    print(f"Password: {password}")
    print(f"User ID: {user_id}")
    print(f"Tenant: {nickname} Kingdom")
    print("User can now login to RAGFlow and use all features")
    
    return True


def main():
    """
    Main function - MODIFY THE VALUES BELOW TO CREATE DIFFERENT USERS
    """
    
    print("RAGFlow Complete User Creator")
    print("=" * 35)
    
    # ========================================
    # MODIFY THESE VALUES TO CREATE ANY USER:
    # ========================================
    
    email = "<EMAIL>"    # ← Change this
    nickname = "User3"                           # ← Change this
    password = "User20250812"                   # ← Change this
    
    # ========================================
    # END OF USER CONFIGURATION
    # ========================================
    
    print(f"Creating user: {email}")
    
    success = create_complete_user(email, nickname, password)
    
    if success:
        print("User creation completed successfully")
        print("The user can now login to RAGFlow")
    else:
        print("User creation failed")
        print("Check the error messages above for details")
    
    print("\n" + "=" * 35)
    print("Script completed.")


if __name__ == "__main__":
    main()
