#!/usr/bin/env python3
"""
Simple test to check if we can import and run basic functions
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if we can import all required modules."""
    print("Testing imports...")
    
    try:
        from api.db.services.user_service import UserService
        print("✅ UserService imported")
    except Exception as e:
        print(f"❌ UserService import failed: {e}")
        return False
    
    try:
        from api.utils import get_uuid
        print("✅ get_uuid imported")
    except Exception as e:
        print(f"❌ get_uuid import failed: {e}")
        return False
    
    try:
        from api import settings
        print("✅ settings imported")
    except Exception as e:
        print(f"❌ settings import failed: {e}")
        return False
    
    return True


def test_database_connection():
    """Test if we can connect to the database."""
    print("\nTesting database connection...")
    
    try:
        from api.db.services.user_service import UserService
        
        # Try to query users (this will test DB connection)
        users = UserService.query(email="<EMAIL>")
        print("✅ Database connection works")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def test_user_exists():
    """Test if Carl's user already exists."""
    print("\nTesting if Carl's user exists...")
    
    try:
        from api.db.services.user_service import UserService
        
        carl_email = "<EMAIL>"
        users = UserService.query(email=carl_email)
        
        if users:
            print(f"✅ User {carl_email} already exists!")
            user = users[0]
            print(f"   Nickname: {user.nickname}")
            print(f"   User ID: {user.id}")
            return True
        else:
            print(f"ℹ️  User {carl_email} does not exist yet")
            return False
    except Exception as e:
        print(f"❌ Error checking user: {e}")
        return False


def main():
    """Main test function."""
    print("RAGFlow Simple Test")
    print("=" * 25)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed. Cannot proceed.")
        return
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Database connection test failed. Cannot proceed.")
        return
    
    # Test if user exists
    user_exists = test_user_exists()
    
    print("\n" + "=" * 25)
    print("SUMMARY:")
    print("✅ Imports: OK")
    print("✅ Database: OK")
    print(f"{'✅' if user_exists else 'ℹ️ '} User exists: {'YES' if user_exists else 'NO'}")
    
    if not user_exists:
        print("\nReady to create user! Run create_carl_user.py")
    else:
        print("\nUser already exists. No need to create.")
    
    print("\n" + "=" * 25)
    print("Test completed.")


if __name__ == "__main__":
    main()
