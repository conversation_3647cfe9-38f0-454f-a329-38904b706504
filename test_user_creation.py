#!/usr/bin/env python3
"""
Test script to verify user creation functionality and test login
"""

import sys
import os
import base64

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import TenantLLMService
from api.db.services.file_service import FileService


def encode_to_base64(input_string):
    """Encode string to base64."""
    base64_encoded = base64.b64encode(input_string.encode('utf-8'))
    return base64_encoded.decode('utf-8')


def test_user_exists(email):
    """Test if a user exists in the database."""
    try:
        users = UserService.query(email=email)
        if users:
            user = users[0]
            print(f"✅ User found:")
            print(f"   Email: {user.email}")
            print(f"   Nickname: {user.nickname}")
            print(f"   Is Superuser: {user.is_superuser}")
            print(f"   Status: {user.status}")
            print(f"   User ID: {user.id}")
            print(f"   Login Channel: {user.login_channel}")
            return user
        else:
            print(f"❌ User with email {email} not found.")
            return None
    except Exception as e:
        print(f"❌ Error checking user: {e}")
        return None


def test_user_complete_setup(user):
    """Test if user has complete setup (tenant, LLM configs, etc.)."""
    if not user:
        return False

    user_id = user.id
    print(f"\n🔍 Checking complete setup for user {user.email}...")

    # Check tenant
    try:
        tenants = TenantService.query(id=user_id)
        if tenants:
            tenant = tenants[0]
            print(f"✅ Tenant found: {tenant.name}")
        else:
            print(f"❌ No tenant found for user")
            return False
    except Exception as e:
        print(f"❌ Error checking tenant: {e}")
        return False

    # Check user-tenant relationship
    try:
        user_tenants = UserTenantService.query(user_id=user_id, tenant_id=user_id)
        if user_tenants:
            print(f"✅ User-tenant relationship found")
        else:
            print(f"❌ No user-tenant relationship found")
            return False
    except Exception as e:
        print(f"❌ Error checking user-tenant relationship: {e}")
        return False

    # Check LLM configurations
    try:
        llm_configs = TenantLLMService.query(tenant_id=user_id)
        if llm_configs:
            print(f"✅ LLM configurations found: {len(llm_configs)} configs")
        else:
            print(f"⚠️  No LLM configurations found (this might be OK)")
    except Exception as e:
        print(f"❌ Error checking LLM configs: {e}")

    # Check file system setup
    try:
        files = FileService.query(tenant_id=user_id, name="/")
        if files:
            print(f"✅ Root folder found")
        else:
            print(f"❌ No root folder found")
            return False
    except Exception as e:
        print(f"❌ Error checking file system: {e}")
        return False

    return True


def test_login_credentials(email, password):
    """Test if login credentials work."""
    print(f"\n🔐 Testing login credentials for {email}...")

    try:
        # This mimics the login process
        user = UserService.query_user(email, password)
        if user:
            print(f"✅ Login credentials are valid!")
            print(f"   User can login with email: {email}")
            print(f"   Password verification: SUCCESS")
            return True
        else:
            print(f"❌ Login credentials are invalid!")
            print(f"   Either email doesn't exist or password is wrong")
            return False
    except Exception as e:
        print(f"❌ Error testing login: {e}")
        return False


def main():
    """Main test function."""
    print("RAGFlow User Creation & Login Test")
    print("=" * 40)

    # Test for Carl's account
    carl_email = "<EMAIL>"
    carl_password = "Carl20250812"

    print(f"Checking if user {carl_email} exists...")
    user = test_user_exists(carl_email)

    if user:
        # Test complete setup
        setup_ok = test_user_complete_setup(user)

        # Test login credentials
        login_ok = test_login_credentials(carl_email, carl_password)

        print("\n" + "=" * 40)
        print("SUMMARY:")
        print(f"✅ User exists: YES")
        print(f"{'✅' if setup_ok else '❌'} Complete setup: {'YES' if setup_ok else 'NO'}")
        print(f"{'✅' if login_ok else '❌'} Login works: {'YES' if login_ok else 'NO'}")

        if setup_ok and login_ok:
            print("\n🎉 SUCCESS! Carl can login to RAGFlow!")
        else:
            print("\n❌ ISSUES FOUND! User may not be able to login properly.")
    else:
        print("\n❌ User does not exist. Run create_carl_user.py first.")

    print("\n" + "=" * 40)
    print("Test completed.")


if __name__ == "__main__":
    main()
