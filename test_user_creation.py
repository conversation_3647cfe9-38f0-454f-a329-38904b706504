#!/usr/bin/env python3
"""
Test script to verify user creation functionality
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService


def test_user_exists(email):
    """Test if a user exists in the database."""
    try:
        users = UserService.query(email=email)
        if users:
            user = users[0]
            print(f"✅ User found:")
            print(f"   Email: {user.email}")
            print(f"   Nickname: {user.nickname}")
            print(f"   Is Superuser: {user.is_superuser}")
            print(f"   Status: {user.status}")
            print(f"   User ID: {user.id}")
            return True
        else:
            print(f"❌ User with email {email} not found.")
            return False
    except Exception as e:
        print(f"❌ Error checking user: {e}")
        return False


def main():
    """Main test function."""
    print("RAGFlow User Creation Test")
    print("=" * 30)
    
    # Test for <PERSON>'s account
    carl_email = "<PERSON><PERSON>@viridiengroup.com"
    print(f"Checking if user {carl_email} exists...")
    test_user_exists(carl_email)
    
    print("\n" + "=" * 30)
    print("Test completed.")


if __name__ == "__main__":
    main()
