#!/usr/bin/env python3
"""
Create RAGFlow user account only (without tenant setup)
"""

import sys
import os
import base64

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService
from api.utils import get_uuid, get_format_time


def create_user_account(email, nickname, password):
    """
    Create a user account with proper RSA password handling.
    
    Args:
        email (str): User email address
        nickname (str): User nickname/display name
        password (str): User password (plain text)
    
    Returns:
        str: User ID if successful, None if failed
    """
    
    print("Creating user account")
    print(f"Email: {email}")
    print(f"Nickname: {nickname}")
    print(f"Password: {password}")
    
    # Check if user already exists
    print("Checking if user exists")
    existing_users = UserService.query(email=email)
    if existing_users:
        print(f"User with email {email} already exists")
        return None
    print("User does not exist, proceeding with creation")
    
    try:
        user_id = get_uuid()
        
        # For RSA compatibility: store base64-encoded password
        # UserService.save will hash it automatically
        base64_password = base64.b64encode(password.encode('utf-8')).decode('utf-8')
        
        user_dict = {
            "id": user_id,
            "access_token": get_uuid(),
            "email": email,
            "nickname": nickname,
            "password": base64_password,  # UserService.save will hash this
            "login_channel": "admin_created",
            "last_login_time": get_format_time(),
            "is_superuser": False,
            "creator": "system",
            "status": "1",
        }
        
        # Create user
        if not UserService.save(**user_dict):
            print("Failed to create user account")
            return None
        
        print("User account created successfully")
        print(f"User ID: {user_id}")
        
        return user_id
        
    except Exception as e:
        print(f"Error creating user: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """
    Main function - MODIFY THE VALUES BELOW TO CREATE DIFFERENT USERS
    """
    
    print("RAGFlow User Account Creator")
    print("=" * 35)
    
    # ========================================
    # MODIFY THESE VALUES TO CREATE ANY USER:
    # ========================================
    
    email = "<EMAIL>"    # ← Change this
    nickname = "UserAccount"                    # ← Change this
    password = "UserAccount123"                 # ← Change this
    
    # ========================================
    # END OF USER CONFIGURATION
    # ========================================
    
    print(f"Creating user account: {email}")
    
    user_id = create_user_account(email, nickname, password)
    
    if user_id:
        print("User account creation completed successfully")
        print(f"User ID: {user_id}")
        print("Note: User needs tenant setup to login to RAGFlow")
    else:
        print("User account creation failed")
        print("Check the error messages above for details")
    
    print("=" * 35)
    print("Script completed")


if __name__ == "__main__":
    main()
