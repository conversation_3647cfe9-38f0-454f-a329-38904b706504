"""
Access request utility functions for Viridoc
"""
import logging


def check_email_already_requested(user_email: str):
    """
    Check if an email has already been used for an access request.

    Args:
        user_email (str): Email to check

    Returns:
        bool: True if email already exists in requests, False otherwise
    """
    try:
        import csv
        import os

        csv_file = os.path.join(os.getcwd(), "viridoc_access_requests.csv")

        if not os.path.exists(csv_file):
            return False

        with open(csv_file, 'r', newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row.get('User_Email', '').lower() == user_email.lower():
                    return True

        return False
    except Exception as e:
        logging.error(f"Error checking for duplicate email: {str(e)}")
        return False


def send_access_request_notification(user_email: str, department: str = "", reason: str = ""):
    """
    Save access request to CSV file for tracking.

    Args:
        user_email (str): Email of the user requesting access
        department (str): User's department
        reason (str): Reason for application (optional)

    Returns:
        bool: True if request saved successfully, False otherwise
    """
    try:
        import csv
        import datetime
        import os

        # Check if email already exists
        if check_email_already_requested(user_email):
            logging.debug(f"Access request already exists for email: {user_email}")
            return False

        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Save CSV in the project root directory
        csv_file = os.path.join(os.getcwd(), "viridoc_access_requests.csv")

        # Check if CSV file exists, if not create it with headers
        logging.debug(f"CWD: {os.getcwd()}")
        logging.debug(f"CSV path: {csv_file}")
        file_exists = os.path.exists(csv_file)
        logging.debug(f"File exists: {file_exists}")

        # Get current user count
        user_num = 1
        if file_exists:
            try:
                with open(csv_file, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    if len(rows) > 1:  # More than just header
                        user_num = len(rows)  # Header + data rows = next user number
            except:
                user_num = 1

        # Write to CSV file
        with open(csv_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write header if file is new
            if not file_exists:
                writer.writerow(['User_Num', 'Timestamp', 'User_Email', 'Department', 'Reason', 'Status'])

            # Write the new access request
            writer.writerow([user_num, timestamp, user_email, department, reason, 'PENDING'])

        # Log the request
        logging.debug("=" * 50)
        logging.debug("🚨 NEW VIRIDOC ACCESS REQUEST 🚨")
        logging.debug(f"User #{user_num}: {user_email}")
        logging.debug(f"Department: {department}")
        logging.debug(f"Reason: {reason}")
        logging.debug(f"Request Time: {timestamp}")
        logging.debug(f"CSV Updated: {csv_file}")
        logging.debug("=" * 50)

        return True

    except Exception as e:
        logging.error(f"Failed to send access request notification: {str(e)}")
        return False



