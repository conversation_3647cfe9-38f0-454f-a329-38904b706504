#!/usr/bin/env python3
"""
Admin User Management Script for RAGFlow
This script demonstrates how to use the admin API endpoints to create users.
"""

import requests
import json
import getpass
import base64


class RAGFlowAdminClient:
    """Client for RAGFlow admin operations."""
    
    def __init__(self, base_url="http://eoaagmld007.int.cgg.com"):
        """
        Initialize the admin client.
        
        Args:
            base_url (str): Base URL of the RAGFlow server
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.auth_token = None
    
    def login(self, email, password):
        """
        Login as admin user.
        
        Args:
            email (str): Admin email
            password (str): Admin password
            
        Returns:
            bool: True if login successful, False otherwise
        """
        # Encrypt password (base64 encoding as used in the frontend)
        encrypted_password = base64.b64encode(password.encode()).decode()
        
        login_data = {
            "email": email,
            "password": encrypted_password
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/user/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    self.auth_token = response.headers.get("Authorization")
                    if self.auth_token:
                        self.session.headers.update({"Authorization": self.auth_token})
                        print(f"Successfully logged in as {email}")
                        return True
                    else:
                        print("Login successful but no authorization token received")
                        return False
                else:
                    print(f"Login failed: {result.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"Login request failed with status code: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"Error during login: {e}")
            return False
    
    def create_user(self, email, password, nickname, is_superuser=False):
        """
        Create a new user using admin privileges.
        
        Args:
            email (str): User email
            password (str): User password
            nickname (str): User nickname
            is_superuser (bool): Whether user should be a superuser
            
        Returns:
            bool: True if user created successfully, False otherwise
        """
        if not self.auth_token:
            print("Not logged in. Please login first.")
            return False
        
        # Encrypt password (base64 encoding as used in the frontend)
        encrypted_password = base64.b64encode(password.encode()).decode()
        
        user_data = {
            "email": email,
            "password": encrypted_password,
            "nickname": nickname,
            "is_superuser": is_superuser
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/user/admin/create_user",
                json=user_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    print(f"Successfully created user: {email}")
                    return True
                else:
                    print(f"User creation failed: {result.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"User creation request failed with status code: {response.status_code}")
                try:
                    error_result = response.json()
                    print(f"Error details: {error_result.get('message', 'No details available')}")
                except:
                    print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"Error during user creation: {e}")
            return False
    
    def check_registration_status(self):
        """
        Check if user registration is enabled.
        
        Returns:
            bool: True if registration is enabled, False otherwise
        """
        try:
            response = self.session.get(f"{self.base_url}/v1/system/config")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    registration_enabled = result.get("data", {}).get("registerEnabled", 0)
                    print(f"Registration enabled: {'Yes' if registration_enabled else 'No'}")
                    return bool(registration_enabled)
                else:
                    print(f"Failed to get config: {result.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"Config request failed with status code: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"Error checking registration status: {e}")
            return False


def main():
    """Main function for interactive admin user management."""
    print("RAGFlow Admin User Management")
    print("=" * 35)
    
    # Initialize client
    base_url = input("Enter RAGFlow server URL (default: http://eoaagmld007.int.cgg.com): ").strip()
    if not base_url:
        base_url = "http://eoaagmld007.int.cgg.com"
    
    client = RAGFlowAdminClient(base_url)
    
    # Check registration status
    print("\nChecking registration status...")
    client.check_registration_status()
    
    # Admin login
    print("\nAdmin Login")
    print("-" * 15)
    admin_email = input("Enter admin email (default: <EMAIL>): ").strip()
    if not admin_email:
        admin_email = "<EMAIL>"
    
    admin_password = getpass.getpass("Enter admin password (default: admin): ").strip()
    if not admin_password:
        admin_password = "admin"
    
    if not client.login(admin_email, admin_password):
        print("Failed to login as admin. Exiting.")
        return
    
    # User creation loop
    while True:
        print("\nCreate New User")
        print("-" * 15)
        
        user_email = input("Enter user email (or 'quit' to exit): ").strip()
        if user_email.lower() == 'quit':
            break
        
        if not user_email:
            print("Email is required!")
            continue
        
        user_nickname = input("Enter user nickname: ").strip()
        if not user_nickname:
            print("Nickname is required!")
            continue
        
        user_password = getpass.getpass("Enter user password: ").strip()
        if not user_password:
            print("Password is required!")
            continue
        
        is_superuser_input = input("Make user a superuser? (y/N): ").strip().lower()
        is_superuser = is_superuser_input in ['y', 'yes']
        
        # Create the user
        success = client.create_user(user_email, user_password, user_nickname, is_superuser)
        
        if success:
            print(f"\nUser created successfully!")
            print(f"Email: {user_email}")
            print(f"Nickname: {user_nickname}")
            print(f"Superuser: {'Yes' if is_superuser else 'No'}")
        else:
            print("Failed to create user.")
        
        continue_input = input("\nCreate another user? (Y/n): ").strip().lower()
        if continue_input in ['n', 'no']:
            break
    
    print("\nGoodbye!")


if __name__ == "__main__":
    main()
