# Admin User Creation Guide for RAGFlow

When user registration is disabled (`REGISTER_ENABLED=0`), regular users cannot create accounts through the normal signup process. However, administrators can still create user accounts using several methods.

## Overview

I've implemented several solutions for admin user creation:

1. **New Admin API Endpoint** - `/v1/user/admin/create_user`
2. **Admin User Creation Script** - `create_admin_user.py`
3. **Admin Management Client** - `admin_user_management.py`

## Method 1: Using the Admin API Endpoint

### New Endpoint: `/v1/user/admin/create_user`

**Requirements:**
- Must be logged in as a superuser (`is_superuser=True`)
- Bypasses the `REGISTER_ENABLED` setting

**Request:**
```bash
POST /v1/user/admin/create_user
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "base64_encoded_password",
  "nickname": "New User",
  "is_superuser": false  // optional, defaults to false
}
```

**Response:**
```json
{
  "code": 0,
  "message": "User New User created successfully by admin!",
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "nickname": "New User",
    // ... other user fields
  }
}
```

### Using curl:
```bash
# First login as admin
curl -X POST http://your-ragflow-server/v1/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "YWRtaW4="
  }'

# Use the Authorization token from login response
curl -X POST http://your-ragflow-server/v1/user/admin/create_user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token_from_login>" \
  -d '{
    "email": "<EMAIL>",
    "password": "cGFzc3dvcmQ=",
    "nickname": "New User"
  }'
```

## Method 2: Using the Admin Creation Script

### Script: `create_admin_user.py`

This script directly creates admin users in the database.

**Usage:**
```bash
# Create default admin user (<EMAIL> / admin)
python3 create_admin_user.py

# Interactive mode
python3 create_admin_user.py --interactive

# Help
python3 create_admin_user.py --help
```

**Features:**
- Creates superuser accounts directly in the database
- Sets up tenant and LLM configurations
- Can create custom admin users with different credentials

## Method 3: Using the Admin Management Client

### Script: `admin_user_management.py`

This is an interactive Python client that uses the API endpoints.

**Usage:**
```bash
python3 admin_user_management.py
```

**Features:**
- Interactive user creation interface
- Checks registration status
- Handles authentication automatically
- Can create both regular users and superusers

## Default Admin User

The system can be initialized with a default admin user:

- **Email:** `<EMAIL>`
- **Password:** `admin`
- **Superuser:** `True`

**Important:** Change the default password after first login!

## Step-by-Step Process

### 1. Ensure Admin User Exists

If you don't have an admin user yet:

```bash
python3 create_admin_user.py
```

### 2. Login as Admin

Use the web interface or API to login with admin credentials:
- Email: `<EMAIL>`
- Password: `admin`

### 3. Create Users via API

Use the admin management script:

```bash
python3 admin_user_management.py
```

Or use the API directly with curl/Postman/etc.

### 4. Alternative: Direct Database Creation

For emergency access, you can create users directly in the database using the `create_admin_user.py` script.

## Security Considerations

1. **Change Default Password:** Always change the default admin password after first login
2. **Limit Superuser Access:** Only create superuser accounts when necessary
3. **Use Strong Passwords:** Enforce strong password policies for all users
4. **Monitor Admin Actions:** Keep track of who creates users and when

## Troubleshooting

### Common Issues:

1. **"Access denied. Admin privileges required."**
   - Make sure you're logged in as a superuser
   - Check that `is_superuser=True` in the database for your admin user

2. **"User with email X already exists!"**
   - The email address is already registered
   - Use a different email address

3. **Database connection errors**
   - Ensure the database is running and accessible
   - Check database configuration in settings

### Checking Admin Status:

You can verify if a user is an admin by checking the database:

```sql
SELECT email, nickname, is_superuser FROM user WHERE email = '<EMAIL>';
```

## Environment Variables

Make sure these are set correctly:

- `REGISTER_ENABLED=0` (to disable public registration)
- Database connection settings
- LLM and other service configurations

## API Integration

The admin endpoint can be integrated into custom admin panels or management tools. The endpoint follows the same authentication and response patterns as other RAGFlow APIs.

## Summary

With these tools, you have multiple ways to create user accounts even when public registration is disabled:

1. **API Endpoint** - For programmatic user creation
2. **Interactive Script** - For manual user creation with a friendly interface  
3. **Direct Script** - For emergency admin user creation

Choose the method that best fits your workflow and security requirements.
