#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create an admin user for RAGFlow when registration is disabled.
This script can be run directly to create the default admin user or create custom admin users.
"""

import sys
import os
import base64
import uuid
import getpass

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import LLMService, TenantLLMService
from api.db import UserTenantRole
from api import settings


def encode_to_base64(input_string):
    """Encode string to base64."""
    base64_encoded = base64.b64encode(input_string.encode('utf-8'))
    return base64_encoded.decode('utf-8')


def create_admin_user(email="<EMAIL>", password="admin", nickname="admin"):
    """
    Create an admin user with superuser privileges.
    
    Args:
        email (str): Admin email address
        password (str): Admin password
        nickname (str): Admin nickname
    
    Returns:
        bool: True if successful, False otherwise
    """
    
    # Check if user already exists
    existing_users = UserService.query(email=email)
    if existing_users:
        print(f"User with email {email} already exists!")
        return False
    
    user_id = uuid.uuid1().hex
    user_info = {
        "id": user_id,
        "password": encode_to_base64(password),
        "nickname": nickname,
        "is_superuser": True,
        "email": email,
        "creator": "system",
        "status": "1",
    }
    
    tenant = {
        "id": user_id,
        "name": nickname + "'s Kingdom",
        "llm_id": settings.CHAT_MDL,
        "embd_id": settings.EMBEDDING_MDL,
        "asr_id": settings.ASR_MDL,
        "parser_ids": settings.PARSERS,
        "img2txt_id": settings.IMAGE2TEXT_MDL
    }
    
    usr_tenant = {
        "tenant_id": user_id,
        "user_id": user_id,
        "invited_by": user_id,
        "role": UserTenantRole.OWNER
    }
    
    # Create tenant LLM configurations
    tenant_llm = []
    try:
        for llm in LLMService.query(fid=settings.LLM_FACTORY):
            tenant_llm.append({
                "tenant_id": user_id,
                "llm_factory": settings.LLM_FACTORY,
                "llm_name": llm.llm_name,
                "model_type": llm.model_type,
                "api_key": settings.API_KEY,
                "api_base": settings.LLM_BASE_URL
            })
    except Exception as e:
        print(f"Warning: Could not set up LLM configurations: {e}")
    
    try:
        # Create user
        if not UserService.save(**user_info):
            print("Failed to create admin user.")
            return False
        
        # Create tenant
        TenantService.insert(**tenant)
        
        # Create user-tenant relationship
        UserTenantService.insert(**usr_tenant)
        
        # Create tenant LLM configurations if available
        if tenant_llm:
            TenantLLMService.insert_many(tenant_llm)
        
        print(f"Admin user created successfully!")
        print(f"Email: {email}")
        print(f"Password: {password}")
        print("Please change the password after first login.")
        return True
        
    except Exception as e:
        print(f"Error creating admin user: {e}")
        return False


def main():
    """Main function to handle command line usage."""
    print("RAGFlow Admin User Creator")
    print("=" * 30)
    
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("Usage:")
            print("  python create_admin_user.py                    # Create default admin user")
            print("  python create_admin_user.py --interactive      # Interactive mode")
            print("  python create_admin_user.py --help             # Show this help")
            return
        elif sys.argv[1] == '--interactive':
            print("Interactive admin user creation")
            email = input("Enter admin email (default: <EMAIL>): ").strip()
            if not email:
                email = "<EMAIL>"
            
            nickname = input("Enter admin nickname (default: admin): ").strip()
            if not nickname:
                nickname = "admin"
            
            password = getpass.getpass("Enter admin password (default: admin): ").strip()
            if not password:
                password = "admin"
            
            create_admin_user(email, password, nickname)
            return
    
    # Default: create standard admin user
    print("Creating default admin user...")
    create_admin_user()


if __name__ == "__main__":
    main()
