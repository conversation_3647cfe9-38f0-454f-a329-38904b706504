#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create user accounts for RAGFlow when registration is disabled.
This script can create both regular users and admin users directly in the database.
"""

import sys
import os
import base64
import uuid
import getpass

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.llm_service import LLMService, TenantLLMService
from api.db import UserTenantRole
from api import settings


def encode_to_base64(input_string):
    """Encode string to base64."""
    base64_encoded = base64.b64encode(input_string.encode('utf-8'))
    return base64_encoded.decode('utf-8')


def create_user(email, password, nickname, is_superuser=False):
    """
    Create a user account (admin or regular user).

    Args:
        email (str): User email address
        password (str): User password
        nickname (str): User nickname
        is_superuser (bool): Whether user should be a superuser

    Returns:
        bool: True if successful, False otherwise
    """

    # Check if user already exists
    existing_users = UserService.query(email=email)
    if existing_users:
        print(f"User with email {email} already exists!")
        return False

    user_id = uuid.uuid1().hex
    user_info = {
        "id": user_id,
        "password": encode_to_base64(password),
        "nickname": nickname,
        "is_superuser": is_superuser,
        "email": email,
        "creator": "system",
        "status": "1",
    }
    
    tenant = {
        "id": user_id,
        "name": nickname + "'s Kingdom",
        "llm_id": settings.CHAT_MDL,
        "embd_id": settings.EMBEDDING_MDL,
        "asr_id": settings.ASR_MDL,
        "parser_ids": settings.PARSERS,
        "img2txt_id": settings.IMAGE2TEXT_MDL
    }

    usr_tenant = {
        "tenant_id": user_id,
        "user_id": user_id,
        "invited_by": user_id,
        "role": UserTenantRole.OWNER
    }

    # Create tenant LLM configurations
    tenant_llm = []
    try:
        for llm in LLMService.query(fid=settings.LLM_FACTORY):
            tenant_llm.append({
                "tenant_id": user_id,
                "llm_factory": settings.LLM_FACTORY,
                "llm_name": llm.llm_name,
                "model_type": llm.model_type,
                "api_key": settings.API_KEY,
                "api_base": settings.LLM_BASE_URL
            })
    except Exception as e:
        print(f"Warning: Could not set up LLM configurations: {e}")

    try:
        # Create user
        if not UserService.save(**user_info):
            print(f"Failed to create user {email}.")
            return False

        # Create tenant
        TenantService.insert(**tenant)

        # Create user-tenant relationship
        UserTenantService.insert(**usr_tenant)

        # Create tenant LLM configurations if available
        if tenant_llm:
            TenantLLMService.insert_many(tenant_llm)

        user_type = "Admin user" if is_superuser else "User"
        print(f"{user_type} created successfully!")
        print(f"Email: {email}")
        print(f"Nickname: {nickname}")
        print(f"Password: {password}")
        if not is_superuser:
            print("User can now login to RAGFlow with these credentials.")
        return True

    except Exception as e:
        print(f"Error creating user: {e}")
        return False


def create_admin_user(email="<EMAIL>", password="admin", nickname="admin"):
    """
    Create an admin user with superuser privileges.
    This is a wrapper for backward compatibility.
    """
    return create_user(email, password, nickname, is_superuser=True)


def main():
    """Main function to handle command line usage."""
    print("RAGFlow User Creator")
    print("=" * 25)

    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("Usage:")
            print("  python create_admin_user.py                    # Interactive mode")
            print("  python create_admin_user.py --admin            # Create default admin user")
            print("  python create_admin_user.py --help             # Show this help")
            print("\nExamples:")
            print("  # Create a regular user interactively")
            print("  python create_admin_user.py")
            print("")
            print("  # Create default admin user")
            print("  python create_admin_user.py --admin")
            return
        elif sys.argv[1] == '--admin':
            print("Creating default admin user...")
            create_admin_user()
            return

    # Default: Interactive user creation
    print("Interactive user creation")
    print("-" * 25)

    email = input("Enter user email: ").strip()
    if not email:
        print("Email is required!")
        return

    nickname = input("Enter user nickname: ").strip()
    if not nickname:
        print("Nickname is required!")
        return

    password = getpass.getpass("Enter user password: ").strip()
    if not password:
        print("Password is required!")
        return

    is_admin_input = input("Make this user an admin? (y/N): ").strip().lower()
    is_admin = is_admin_input in ['y', 'yes']

    create_user(email, password, nickname, is_superuser=is_admin)


if __name__ == "__main__":
    main()
