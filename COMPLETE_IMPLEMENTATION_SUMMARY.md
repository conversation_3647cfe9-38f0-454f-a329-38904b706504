# Complete Dynamic Layout Model Selection Implementation

##  IMPLEMENTATION COMPLETE - Dynamic Model Selection Working

### What Was Achieved

**🎯 Goal**: Add `layout_model` parameter to RAGFlow API for dynamic model selection between DiT and YOLO without manual code changes.

## Implementation Overview

### 1. API Layer (api/apps/sdk/doc.py)
- Added `layout_model` parameter validation to `/datasets/{dataset_id}/chunks` endpoint
- Valid values: `dit`, `yolo`, `deepdoc`, `plain_text`
-  Error handling for invalid values
-  Pass parameter to task queue with debug logging

### 2. Task Queue (api/db/services/task_service.py)
-  Override `parser_config.layout_recognize` based on API parameter
-  **CRITICAL FIX**: Update document's parser_config in database so task executor gets correct config
- Debug logging for parameter tracing

### 3. Model Mapping (api/utils/api_utils.py)
-  Convert API values to internal format (`dit` → `DiT`, `yolo` → `YOLO`)
- Case insensitive handling
- Default fallback to `DeepDOC`

### 4. DiT Model Integration (deepdoc/vision/layout_recognizer.py)
-  **NEW**: Complete DiT class implementation with API integration
-  Custom `__call__` method override for string class names
-  OCR adjustment post-processing (`ocr_adjust_bbox`)
-  Coordinate sorting fix for bounding box errors
-  Detailed debug logging for API calls and processing

### 5. PDF Parser Integration (deepdoc/parser/pdf_parser.py)
-  Pass layout model through the parsing pipeline
-  Map internal values to model parameters
-  Coordinate sorting fixes for image cropping



###  Enhanced main_ragapi.py Integration
```python
def parse_documents(self, dataset_id, document_ids, layout_model="dit"):
    """Parse documents with dynamic layout model selection"""
    url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
    payload = {
        "document_ids": document_ids,
        "layout_model": layout_model  # Dynamic model selection
    }
    response = self.session.post(url, json=payload)
    return response.json()

# Usage in main_ragapi.py
LAYOUT_MODEL = "dit"  # Options: "dit", "yolo", "deepdoc", "plain_text"
client.parse_documents(dataset_id, [document_id], layout_model=LAYOUT_MODEL)
```



##  VERIFIED Technical Flow

```
API Request (layout_model="dit")
    ↓
Parameter Validation (api/apps/sdk/doc.py)
    ↓
Task Queue Override (api/db/services/task_service.py)
    ↓
Database Update (Document.parser_config.layout_recognize = "DiT")
    ↓
Task Executor Retrieval (gets updated config from database)
    ↓
PDF Parser Processing (deepdoc/parser/pdf_parser.py)
    ↓
DiT Model Instantiation (deepdoc/vision/layout_recognizer.py)
    ↓
 DiT API Calls with Debug Output:
   - "DiT API call with original image size: (1786, 2526)"
   - "DiT API response for page X: boxes=Y"
   - "DiT processed X boxes for page Y"
```

## Files Modified (8 total)

### Core Implementation Files

1. **api/apps/sdk/doc.py** - API parameter validation and passing
   - Added layout_model parameter validation to chunks endpoint
   - Valid values: dit, yolo, deepdoc, plain_text
   - Pass parameter to queue_tasks function

2. **api/db/services/task_service.py** - Parser config override and database update
   - Override parser_config.layout_recognize based on API parameter
   - CRITICAL FIX: Update document's parser_config in database so task executor gets correct config
   - Debug logging for parameter tracing
   - Import and use map_layout_model_to_internal function

3. **api/utils/api_utils.py** - Model mapping function (NEW FILE)
   - Convert API values to internal format (dit to DiT, yolo to YOLO)
   - Case insensitive handling
   - Default fallback to DeepDOC
   - Single mapping function for consistency

4. **deepdoc/vision/layout_recognizer.py** - DiT model implementation
   - Complete DiT class implementation with API integration
   - Custom __call__ method override for string class names
   - OCR adjustment post-processing (ocr_adjust_bbox)
   - Coordinate sorting fix for bounding box errors
   - Detailed debug logging for API calls and processing

5. **deepdoc/parser/pdf_parser.py** - PDF parser coordinate fixes
   - Added coordinate sorting to prevent bounding box errors
   - Fixed x0,x1 = sorted() and y0,y1 = sorted() for image cropping
   - Ensures proper coordinate ordering for DiT model processing

6. **rag/app/naive.py** - Parser integration
   - Pass layout model parameter through parsing pipeline
   - Map internal values to model parameters
   - Integration with PDF parser for layout recognition

7. **deepdoc/vision/__init__.py** - Dynamic model factory
   - Replaced static import with dynamic factory pattern
   - Support for both DiT and YOLO models
   - Runtime model selection based on parameter
   - Factory function for layout recognizer instantiation

8. **main_ragapi.py** - Client integration (OPTIONAL)
   - Added layout_model parameter to parse_documents function
   - Configurable LAYOUT_MODEL variable
   - Example usage with dynamic model selection
   - Integration with RAGFlow API endpoint


### Verified Log Output
```
2025-07-16 21:00:46,402 INFO handle_task begin for task {..., "parser_config": {"layout_recognize": "DiT"}, ...}
2025-07-16 21:00:53,842 DEBUG DiT API call with original image size: (1786, 2526)
2025-07-16 21:00:54,356 DEBUG DiT API call successful for image size: (1786, 2526)
2025-07-16 21:00:54,356 DEBUG DiT API response for page 0: boxes=13
2025-07-16 21:00:56,137 DEBUG DiT processed 13 boxes for page 0, first 3: [...]
2025-07-16 21:01:01,511 INFO handle_task done for task {...}
```


