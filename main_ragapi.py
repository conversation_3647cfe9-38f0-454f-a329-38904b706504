#!/usr/bin/env python3


import os
import json
import time
import requests
import glob
from datetime import datetime

class RAGFlowAPIClient:
    """RAGFlow API client with all critical fixes applied"""
    
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
    
    def create_output_directory(self, base_output_path, pdf_filename):
        """Create timestamped output directory"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = os.path.splitext(pdf_filename)[0]
        output_dir = os.path.join(base_output_path, f"{pdf_name}_api_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def save_step_output(self, data, step_name, output_dir):
        """Save output from each processing step"""
        filename = f"{step_name}.json"
        filepath = os.path.join(output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"   Saved {step_name} output to: {filepath}")
            return filepath
        except Exception as e:
            print(f"   Failed to save {step_name} output: {str(e)}")
            return None
    
    def create_dataset(self, dataset_name, chunk_method="naive"):
        """Step 1: Create a dataset with configurable chunking method"""
        print(f"1. Creating dataset: {dataset_name} (chunk_method: {chunk_method})")
        url = f"{self.api_base_url}/api/v1/datasets"

        # Use minimal payload - only required parameters
        payload = {
            "name": dataset_name,
            "description": f"Dataset for {dataset_name} with {chunk_method} chunking"
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                dataset_id = result["data"]["id"]
                print(f"   Dataset created: {dataset_id}")
                return dataset_id
            else:
                print(f"   Failed: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  Request failed: {str(e)}")
            return None

    def create_or_find_dataset(self, dataset_name, chunk_method="naive"):
        """Create dataset or find existing one"""
        # First try to create the dataset
        dataset_id = self.create_dataset(dataset_name, chunk_method)
        if dataset_id:
            return dataset_id

        # If creation failed, try to find existing dataset
        print(f"   Searching for existing dataset: {dataset_name}")
        try:
            url = f"{self.api_base_url}/api/v1/datasets"
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                # Handle different possible response formats
                data = result["data"]
                if isinstance(data, list):
                    datasets = data
                else:
                    datasets = data.get("datasets", [])

                for dataset in datasets:
                    if dataset.get("name") == dataset_name:
                        dataset_id = dataset.get("id")
                        print(f"   Found existing dataset: {dataset_id}")
                        return dataset_id

                # If not found, create with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_name = f"{dataset_name}_{timestamp}"
                print(f"  🔄 Creating new dataset with timestamp: {new_name}")
                return self.create_dataset(new_name, chunk_method)
            else:
                print(f"  ❌ Failed to list datasets: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  ❌ Error searching for dataset: {str(e)}")
            return None

    def check_document_exists(self, dataset_id, filename):
        """Check if document already exists in dataset - improved duplicate detection"""
        try:
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", [])
                # Handle different response formats
                if isinstance(data, list):
                    documents = data
                elif isinstance(data, dict):
                    documents = data.get("docs", [])  # Use "docs" key for document list
                else:
                    documents = []

                print(f"    Checking for existing file: {filename}")
                print(f"    Found {len(documents)} documents in dataset")

                # Get base filename without extension for comparison
                base_name = filename.rsplit('.', 1)[0] if '.' in filename else filename

                for doc in documents:
                    if isinstance(doc, dict):
                        doc_name = doc.get("name", "")
                        doc_base = doc_name.rsplit('.', 1)[0] if '.' in doc_name else doc_name

                        # Check for exact match first
                        if doc_name == filename:
                            print(f"    Found exact match: {doc_name} (ID: {doc.get('id')})")
                            return doc.get("id")

                        # Check for base name match (handles g2mod.pdf vs g2mod(1).pdf)
                        if doc_base == base_name or doc_base.startswith(f"{base_name}("):
                            print(f"    Found similar file: {doc_name} (ID: {doc.get('id')})")
                            print(f"   ♻️ Will reuse existing document instead of uploading")
                            return doc.get("id")

                print(f"    No existing document found for: {filename}")
                return None
            else:
                print(f"    Failed to check existing documents: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"    Error checking existing documents: {str(e)}")
            return None

    def upload_document(self, dataset_id, pdf_path):
        """Step 2: Upload PDF (WORKING: uses 'file' field name)"""
        print(f"2. Uploading: {os.path.basename(pdf_path)}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"

        try:
            with open(pdf_path, 'rb') as file_handle:
                files = {'file': (os.path.basename(pdf_path), file_handle, 'application/pdf')}
                headers = {'Authorization': f'Bearer {self.api_key}'}

                response = requests.post(url, files=files, headers=headers)
                response.raise_for_status()
                result = response.json()

                if result.get("code") == 0:
                    # Handle response format: data is a list with document info
                    data = result.get("data", [])
                    if isinstance(data, list) and len(data) > 0:
                        document_id = data[0].get("id")
                        print(f"   Document uploaded: {document_id}")
                        return document_id
                    else:
                        print(f"  Failed: Unexpected response format")
                        return None
                else:
                    print(f"  Failed: {result.get('message', 'Unknown error')}")
                    return None
        except Exception as e:
            print(f"  Upload failed: {str(e)}")
            return None
    
    def parse_documents(self, dataset_id, document_id, layout_model="dit"):
        """Step 3: Parse documents with optional layout model selection"""
        if isinstance(document_id, str):
            document_ids = [document_id]
        else:
            document_ids = document_id

        print(f"3. Parsing documents: {document_ids}")
        print(f"   Using layout model: {layout_model}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"

        payload = {
            "document_ids": document_ids,
            "layout_model": layout_model
        }

        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                print("    Batch parsing initiated successfully")
                print("   Pipeline will process: OCR → Layout → Tokenization → Embedding → Chunking")
                return True
            else:
                error_msg = result.get('message', 'Unknown error')
                print(f"    Parsing failed: {error_msg}")

                # Check for specific embedding-related errors
                if 'embedding' in error_msg.lower() or 'float' in error_msg.lower():
                    print("    Embedding error detected - this may resolve during processing")
                    print("    Will continue monitoring for successful chunk generation")

                return False
        except Exception as e:
            print(f"    Parse request failed: {str(e)}")
            return False
    
    def wait_for_parsing_completion(self, dataset_id, document_id, timeout=600, interval=10):
        """Step 4: Wait for completion (CORRECTED status: 'indexed')"""
        print(f"4. Waiting for completion (timeout: {timeout}s)...")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = self.session.get(url)
                response.raise_for_status()

                # Handle empty response
                if not response.text.strip():
                    print(f"   Empty response, retrying...")
                    time.sleep(interval)
                    continue

                result = response.json()
                print(f"   Debug - Status response: {result}")

                if result.get("code") == 0:
                    doc_data = result["data"]
                    status = doc_data.get("status", "unknown")
                    progress = doc_data.get("progress", 0)

                    print(f"   Status: {status}, Progress: {progress:.0%}")

                    # CORRECTED STATUS CHECK
                    if status == "indexed":
                        print("   Processing completed!")
                        return True, doc_data
                    elif status == "error":
                        print(f"   Processing failed: {doc_data.get('progress_msg', 'Unknown error')}")
                        return False, doc_data
                else:
                    print(f"   API error: {result.get('message', 'Unknown error')}")

                time.sleep(interval)
            except requests.exceptions.JSONDecodeError as e:
                print(f"   JSON decode error (empty response?): {str(e)}")
                time.sleep(interval)
            except Exception as e:
                print(f"   Status check failed: {str(e)}")
                time.sleep(interval)

        print(f" Timeout after {timeout}s")
        return False, None
    
    


def PDFs_processor(folder_path, output_base, api_base_url, api_key, dataset_name, chunk_method, layout_model="dit"):
    """Improved version: Check existing files, batch parsing, extended processing time, configurable chunking"""
    print(f" Processing PDFs and extracting Elasticsearch data from: {folder_path}")
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))

    if not pdf_files:
        print(" No PDF files found")
        return []

    print(f" Found {len(pdf_files)} PDF files")
    print(f" Using dataset: {dataset_name}")
    print(f" Chunking method: {chunk_method}")
    print(f" Layout model: {layout_model}")

    client = RAGFlowAPIClient(api_base_url, api_key)

    # Create output directory - COMMENTED OUT FOR UPLOAD-ONLY MODE
    # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # output_dir = os.path.join(output_base, f"{dataset_name}_elasticsearch_{timestamp}")
    # os.makedirs(output_dir, exist_ok=True)
    # print(f" Output directory: {output_dir}")

    # Step 1: Create or find dataset
    print(f"\n1. Creating/finding dataset: {dataset_name}")
    dataset_id = client.create_or_find_dataset(dataset_name, chunk_method)
    if not dataset_id:
        print(" Failed to create or find dataset")
        return []

    print(f"    Dataset ready: {dataset_id}")

    # Step 2: Check existing documents and collect document IDs for processing
    print(f"\n2. Checking existing documents and preparing for batch processing...")
    document_ids = []
    new_uploads = 0
    existing_docs = 0

    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")

        try:
            # Check if document already exists
            doc_id = client.check_document_exists(dataset_id, filename)

            if doc_id:
                # Document exists - skip upload, go directly to parsing
                print(f"    Document already exists: {doc_id}")
                print(f"    Skipping upload, will include in batch parsing")
                document_ids.append(doc_id)
                existing_docs += 1
            else:
                # Document doesn't exist - upload it
                print(f"    Uploading new document...")
                doc_id = client.upload_document(dataset_id, pdf_path)
                if not doc_id:
                    print(f"    Upload failed for {filename}")
                    continue
                print(f"    Uploaded successfully: {doc_id}")
                document_ids.append(doc_id)
                new_uploads += 1

        except Exception as e:
            print(f"    Error processing {filename}: {str(e)}")

    if not document_ids:
        print(" No documents to process")
        return []

    print(f"\n Processing Summary:")
    print(f"    Total documents: {len(document_ids)}")
    print(f"    New uploads: {new_uploads}")
    print(f"    Existing documents: {existing_docs}")

    # Step 3: Batch parse all documents at once
    print(f"\n3.  Batch parsing {len(document_ids)} documents...")
    print(f"    Document IDs: {document_ids[:3]}{'...' if len(document_ids) > 3 else ''}")

    if not client.parse_documents(dataset_id, document_ids, layout_model):
        print(" Batch parsing failed")
        return []

    print(f"    Batch parsing initiated for {len(document_ids)} documents")

    # Wait longer for the complete processing pipeline
    wait_time = 600  # 10 minutes for multiple documents with full pipeline
    interval = 20    # Check every 20 seconds

    # Track actual processing time
    import time
    start_time = time.time()

    for elapsed in range(0, wait_time, interval):
        print(f"    Processing... {elapsed//60}:{elapsed%60:02d} / {wait_time//60}:{wait_time%60:02d}")

        # Check document status periodically
        if elapsed > 0 and elapsed % 120 == 0:  # Check every 2 minutes
            try:
                url = f"{client.api_base_url}/api/v1/datasets/{dataset_id}/documents"
                response = client.session.get(url)
                if response.status_code == 200:
                    docs_result = response.json()
                    if docs_result.get("code") == 0:
                        docs_data = docs_result.get("data", {})
                        docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data

                        completed = 0
                        processing = 0
                        failed = 0
                        for doc in docs_list:
                            status = doc.get('run', 'unknown')
                            if status in ['FINISH', 'DONE']:  # Both FINISH and DONE mean completed
                                completed += 1
                            elif status in ['RUNNING', 'WAITING']:
                                processing += 1
                            elif status in ['FAIL', 'ERROR']:
                                failed += 1

                        print(f"    Status check: {completed} completed, {processing} processing, {failed} failed, {len(docs_list)} total")

                        # If all documents are completed or failed (no more processing), break early
                        if processing == 0:
                            if completed > 0:
                                print(f"    All documents completed processing! ({completed} successful, {failed} failed)")
                                # Calculate actual processing time when completed early
                                actual_elapsed = time.time() - start_time
                                break
                            else:
                                print(f"     No documents completed successfully ({failed} failed)")
                                actual_elapsed = time.time() - start_time
                                break
            except Exception as e:
                print(f"    Status check failed: {str(e)}")

        time.sleep(interval)

    # Calculate final actual processing time
    actual_elapsed = time.time() - start_time

    # Step 5: Final document status check and chunk extraction
    print(f"\n5. 📋 Final document status check...")
    try:
        url = f"{client.api_base_url}/api/v1/datasets/{dataset_id}/documents"
        response = client.session.get(url)
        if response.status_code == 200:
            docs_result = response.json()
            if docs_result.get("code") == 0:
                docs_data = docs_result.get("data", {})
                docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data

                completed_docs = 0
                total_chunks = 0

                print(f"    Final Status Report:")
                for doc in docs_list:
                    status = doc.get('run', 'Unknown')
                    progress = doc.get('progress', 0)
                    chunk_count = doc.get('chunk_count', 0)
                    progress_msg = doc.get('progress_msg', '')
                    name = doc.get('name', 'Unknown')

                    print(f"       {name}: Status={status}, Progress={progress:.1%}, Chunks={chunk_count}")

                    if status in ['FINISH', 'DONE']:  # Both FINISH and DONE mean completed
                        completed_docs += 1
                        total_chunks += chunk_count
                    elif progress_msg and 'ERROR' in progress_msg:
                        print(f"          Error: {progress_msg}")

                print(f"    Completed: {completed_docs}/{len(docs_list)} documents")
                print(f"    Total chunks generated: {total_chunks}")

    except Exception as e:
        print(f"    Final status check failed: {str(e)}")

    # UPLOAD-ONLY MODE: Just return success after processing
    print(f"\n Upload and Processing Complete!")
    print(f" Final Summary:")
    print(f"    Dataset: {dataset_name}")
    print(f"    PDF files processed: {len(pdf_files)}")
    print(f"    New uploads: {new_uploads}")
    print(f"    Existing documents: {existing_docs}")
    print(f"    Total documents processed: {len(document_ids)}")
    print(f"    Total processing time: {actual_elapsed/60:.1f} minutes ({actual_elapsed:.0f} seconds)")
    print(f" All files uploaded and processed in RAGFlow website!")

    return True  

def main():
    # Configuration - USE ENVIRONMENT VARIABLES IN PRODUCTION!
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")

    # Path configuration (replace with your paths)
    PDF_FOLDER = r"/ml/shared/lazhang/data/pdfdata"
    OUTPUT_BASE = r"/ml/shared/lazhang/data"

    # User-provided dataset name - NO AUTO-GENERATION
    DATASET_NAME = "test_tt"  


    CHUNK_METHOD = "naive"  # Options: "naive", "paper", etc.
    print(f" Using chunking method: {CHUNK_METHOD}")


    LAYOUT_MODEL = "dit"  # Options: "dit", "yolo", "deepdoc", "plain_text"
    print(f" Using layout model: {LAYOUT_MODEL}")


    success = PDFs_processor(PDF_FOLDER, OUTPUT_BASE, API_BASE_URL, API_KEY, DATASET_NAME, CHUNK_METHOD, LAYOUT_MODEL)

    if success:
        print(f"\n SUCCESS: All PDFs uploaded and processed in RAGFlow website!")
       # print("📋 Files are now available in the RAGFlow interface for querying and retrieval.")
    else:
        print("\n FAILED: Some issues occurred during upload/processing. Check the logs above.")

if __name__ == "__main__":
    main()