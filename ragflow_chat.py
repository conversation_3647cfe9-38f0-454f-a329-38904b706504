#!/usr/bin/env python3

"""
RAGFlow Chat API Client for Chat ID: f85bd66e619511f0b83602420ae90a06
Simple API client to chat with your specific RAGFlow assistant
"""

import os
import json
import requests
from typing import Optional, List, Dict, Any, Generator


class RAGFlowChatClient:
    """Simple RAGFlow Chat API client for your specific chat assistant"""
    
    def __init__(self, api_base_url: str, api_key: str):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
    
    def create_session(self, chat_id: str, name: str = "Chat Session") -> Optional[str]:
        """Create a new chat session"""
        url = f"{self.api_base_url}/api/v1/chats/{chat_id}/sessions"
        payload = {"name": name}
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                return result["data"]["id"]
            else:
                print(f"Failed to create session: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"Session creation failed: {str(e)}")
            return None
    
    def ask_question(self, chat_id: str, question: str, session_id: str, stream: bool = False) -> Any:
        """Ask a question to the chat assistant"""
        url = f"{self.api_base_url}/api/v1/chats/{chat_id}/completions"
        payload = {
            "question": question,
            "stream": stream,
            "session_id": session_id
        }
        
        try:
            if stream:
                return self._stream_response(url, payload)
            else:
                response = self.session.post(url, json=payload)
                response.raise_for_status()
                result = response.json()
                
                if result.get("code") == 0:
                    return result["data"]
                else:
                    print(f"Question failed: {result.get('message', 'Unknown error')}")
                    return None
        except Exception as e:
            print(f"Request failed: {str(e)}")
            return None
    
    def list_sessions(self, chat_id: str) -> List[Dict]:
        """List all sessions for a chat assistant"""
        url = f"{self.api_base_url}/api/v1/chats/{chat_id}/sessions"

        try:
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                return result.get("data", [])
            else:
                print(f"Failed to list sessions: {result.get('message', 'Unknown error')}")
                return []
        except Exception as e:
            print(f"List sessions failed: {str(e)}")
            return []

    def _stream_response(self, url: str, payload: Dict) -> Generator[Dict, None, None]:
        """Handle streaming response"""
        try:
            response = self.session.post(url, json=payload, stream=True)
            response.raise_for_status()

            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data:'):
                        data_str = line_str[5:].strip()
                        if data_str:
                            try:
                                data = json.loads(data_str)
                                yield data
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            print(f"Streaming failed: {str(e)}")
            yield {"error": str(e)}


def ask_question_for_user(question: str, user_id: str):
    """Ask a question for a specific user (creates/reuses user's session)"""
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    CHAT_ID = "f85bd66e619511f0b83602420ae90a06"

    client = RAGFlowChatClient(API_BASE_URL, API_KEY)

    # Get or create session for this specific user
    session_id = get_user_session(user_id, client, CHAT_ID)
    if not session_id:
        print(f"Failed to create session for user {user_id}")
        return None

    # Ask question
    response = client.ask_question(CHAT_ID, question, session_id, stream=False)

    if response:
        return response.get('answer', 'No answer')
    else:
        return None


def get_user_session(user_id: str, client: RAGFlowChatClient, chat_id: str, session_timeout_hours: int = 24):
    """Get existing session for user or create new one with automatic expiration"""
    import time
    session_file = f"user_session_{user_id}.txt"

    # Try to load existing session and check if it's still valid
    if os.path.exists(session_file):
        try:
            with open(session_file, 'r') as f:
                lines = f.read().strip().split('\n')
                if len(lines) >= 2:
                    session_id = lines[0]
                    timestamp = float(lines[1])

                    # Check if session is still valid (not expired)
                    current_time = time.time()
                    if current_time - timestamp < session_timeout_hours * 3600:
                        print(f"Reusing existing session for {user_id}")
                        return session_id
                    else:
                        print(f"Session expired for {user_id}, creating new one")
                        os.remove(session_file)  # Remove expired session
        except:
            pass

    # Create new session for this user
    print(f"Creating new session for {user_id}")
    session_id = client.create_session(chat_id, f"User_{user_id}_Session")

    # Save session with timestamp
    if session_id:
        try:
            with open(session_file, 'w') as f:
                f.write(f"{session_id}\n{time.time()}")
        except:
            pass

    return session_id


def end_user_session(user_id: str):
    """End session for a specific user"""
    session_file = f"user_session_{user_id}.txt"
    if os.path.exists(session_file):
        os.remove(session_file)
        print(f"Session ended for {user_id}")
        return True
    return False


def start_new_session_for_user(user_id: str):
    """Force start a new session for user (ends existing one)"""
    end_user_session(user_id)  # End existing session
    # Next call to ask_question_for_user will create new session
    print(f"Ready to start new session for {user_id}")


def list_active_users():
    """List all users with active sessions"""
    import glob
    import time

    session_files = glob.glob("user_session_*.txt")
    active_users = []

    for file in session_files:
        try:
            user_id = file.replace("user_session_", "").replace(".txt", "")
            with open(file, 'r') as f:
                lines = f.read().strip().split('\n')
                if len(lines) >= 2:
                    session_id = lines[0]
                    timestamp = float(lines[1])
                    hours_ago = (time.time() - timestamp) / 3600
                    active_users.append({
                        "user_id": user_id,
                        "session_id": session_id[:8] + "...",
                        "hours_ago": round(hours_ago, 1)
                    })
        except:
            continue

    return active_users


def ask_question_simple(question: str, session_id: str = None):
    """Ask a single question (for backward compatibility)"""
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    CHAT_ID = "f85bd66e619511f0b83602420ae90a06"

    client = RAGFlowChatClient(API_BASE_URL, API_KEY)

    if not session_id:
        session_id = client.create_session(CHAT_ID, "API Session")
        if not session_id:
            print("Failed to create session")
            return None

    response = client.ask_question(CHAT_ID, question, session_id, stream=False)

    if response:
        return response.get('answer', 'No answer')
    else:
        return None


def ask_with_web_session(question: str):
    """Ask question using the same session as web interface"""
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    CHAT_ID = "f85bd66e619511f0b83602420ae90a06"
    WEB_SESSION_ID = "KWbgjH2GNcF4FLnP_j7mfUxpxaw-bX5r2FeQdHymCMM"

    client = RAGFlowChatClient(API_BASE_URL, API_KEY)

    # Ask question using the web interface session ID
    response = client.ask_question(CHAT_ID, question, WEB_SESSION_ID, stream=False)

    if response:
        return response.get('answer', 'No answer')
    else:
        return None


def list_all_sessions():
    """List all existing sessions for your chat assistant"""
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    CHAT_ID = "f85bd66e619511f0b83602420ae90a06"

    client = RAGFlowChatClient(API_BASE_URL, API_KEY)

    print(f"Listing all sessions for chat assistant: {CHAT_ID}")
    print("=" * 60)

    sessions = client.list_sessions(CHAT_ID)

    if sessions:
        print(f"Found {len(sessions)} sessions:")
        print()
        for i, session in enumerate(sessions, 1):
            session_id = session.get("id", "Unknown")
            session_name = session.get("name", "Unnamed")
            create_time = session.get("create_time", "Unknown")

            # Show recent messages if available
            messages = session.get("messages", [])
            last_message = ""
            if messages:
                for msg in reversed(messages):
                    if msg.get("role") == "user":
                        last_message = msg.get("content", "")[:50] + "..."
                        break

            print(f"{i}. Session ID: {session_id}")
            print(f"   Name: {session_name}")
            print(f"   Created: {create_time}")
            if last_message:
                print(f"   Last question: {last_message}")
            print()

        # Show how to use a specific session
        if sessions:
            first_session_id = sessions[0]["id"]
            print("To use an existing session:")
            print(f"python ragflow_chat.py use_session {first_session_id} 'Your question'")
    else:
        print("No existing sessions found.")
        print("Create a new session by running: python ragflow_chat.py chat")


def use_existing_session(session_id: str, question: str):
    """Use a specific existing session ID"""
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    CHAT_ID = "f85bd66e619511f0b83602420ae90a06"

    client = RAGFlowChatClient(API_BASE_URL, API_KEY)

    print(f"Using existing session: {session_id}")
    print(f"Question: {question}")
    print()

    # Ask question with existing session
    response = client.ask_question(CHAT_ID, question, session_id, stream=False)

    if response:
        print(f"Answer: {response.get('answer', 'No answer')}")

        # Show reference info
        reference = response.get('reference', {})
        if reference and reference.get('chunks'):
            print(f"Knowledge base chunks used: {len(reference['chunks'])}")
    else:
        print("No response received - session might be invalid")





def interactive_chat():
    """Interactive chat with your assistant"""
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    CHAT_ID = "f85bd66e619511f0b83602420ae90a06"

    client = RAGFlowChatClient(API_BASE_URL, API_KEY)

    print("Interactive Chat - Type 'quit' to exit")

    # Create session
    session_id = client.create_session(CHAT_ID, "Interactive Chat")
    if not session_id:
        print("Failed to create session")
        return

    print("Assistant: Hello! How can I help you?")

    while True:
        try:
            user_input = input("\nYou: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            elif not user_input:
                continue

            # Get response (non-streaming for simplicity)
            response = client.ask_question(CHAT_ID, user_input, session_id, stream=False)
            if response:
                print(f"Assistant: {response.get('answer', 'No answer')}")
            else:
                print("Assistant: Sorry, I couldn't process that.")

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"\nError: {str(e)}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "chat":
            interactive_chat()
        elif sys.argv[1] == "ask" and len(sys.argv) > 2:
            question = " ".join(sys.argv[2:])
            answer = ask_question_simple(question)
            if answer:
                print(f"Answer: {answer}")
            else:
                print("No answer received")
        elif sys.argv[1] == "user" and len(sys.argv) > 3:
            user_id = sys.argv[2]
            question = " ".join(sys.argv[3:])
            answer = ask_question_for_user(question, user_id)
            if answer:
                print(f"Answer for {user_id}: {answer}")
            else:
                print("No answer received")
        elif sys.argv[1] == "web" and len(sys.argv) > 2:
            question = " ".join(sys.argv[2:])
            answer = ask_with_web_session(question)
            if answer:
                print(f"Answer: {answer}")
            else:
                print("No answer received")
        elif sys.argv[1] == "end" and len(sys.argv) > 2:
            user_id = sys.argv[2]
            end_user_session(user_id)
        elif sys.argv[1] == "new" and len(sys.argv) > 2:
            user_id = sys.argv[2]
            start_new_session_for_user(user_id)
        elif sys.argv[1] == "users":
            users = list_active_users()
            if users:
                print("Active users:")
                for user in users:
                    print(f"  {user['user_id']}: {user['session_id']} ({user['hours_ago']}h ago)")
            else:
                print("No active users")
        else:
            print("Usage:")
            print("  python ragflow_chat.py chat                      # Interactive chat")
            print("  python ragflow_chat.py ask 'question'            # Single question (new session)")
            print("  python ragflow_chat.py user <user_id> 'question' # Question for specific user")
            print("  python ragflow_chat.py web 'question'            # Use web interface session")
            print("  python ragflow_chat.py end <user_id>             # End user's session")
            print("  python ragflow_chat.py new <user_id>             # Start new session for user")
            print("  python ragflow_chat.py users                     # List active users")
    else:
        # Default: ask a test question
        answer = ask_question_simple("Hello! What can you help me with?")
        if answer:
            print(f"Test Answer: {answer}")
        else:
            print("Test failed")
